const fs = require('fs');
const path = require('path');
const readline = require('readline');

/**
 * Get the subenv value from external input.
 * @returns {string|null} The subenv value, or null if not provided.
 */
function getSubenvValue() {
  const args = process.argv.slice(2);
  const subenvParam = args.find(arg => arg.startsWith('subenv='));
  return subenvParam ? subenvParam.split('=')[1] : null;
}

/**
 * Validate the subenv value.
 * @param {string|null} subenvValue - The subenv value to validate.
 */
function validateSubenv(subenvValue) {
  if (!subenvValue) {
    console.error('Error: Please provide a valid subenv value, e.g., yarn pre:env subenv=2');
    process.exit(1);
  }
}

/**
 * Read the content of a file.
 * @param {string} filePath - The path to the file.
 * @returns {string} The file content.
 */
function readFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.error(`Error: File not found at ${filePath}`);
    process.exit(1);
  }
  return fs.readFileSync(filePath, 'utf-8');
}

/**
 * Replace occurrences of "test.shub.us" with "test{subenv}.shub.us" in the file content.
 * @param {string} content - The original file content.
 * @param {string} subenvValue - The subenv value to replace with.
 * @returns {string} The updated file content.
 */
function replaceSubenv(content, subenvValue) {
  // Replace "test.shub.us" with "test{subenv}.shub.us"
  return content.replace(/test\.shub\.us/g, `test${subenvValue}.shub.us`);
}

/**
 * 强制将 REACT_APP_GROWTHBOOK_ENABLED 的值设置为 true
 * @param {string} content - .env 文件内容
 * @returns {string} 修改后的内容
 */
function forceGrowthbookEnabled(content) {
  // 匹配变量并替换为 true，如果没有则添加
  if (/^REACT_APP_GROWTHBOOK_ENABLED=.*/m.test(content)) {
    return content.replace(/^REACT_APP_GROWTHBOOK_ENABLED=.*/m, 'REACT_APP_GROWTHBOOK_ENABLED=true');
  } else {
    return content + '\nREACT_APP_GROWTHBOOK_ENABLED=true\n';
  }
}

/**
 * Write content to a file.
 * @param {string} filePath - The path to the file.
 * @param {string} content - The content to write.
 */
function writeFile(filePath, content) {
  fs.writeFileSync(filePath, content, 'utf-8');
  console.log(`Success: Updated ${filePath}`);
}

async function ask(question) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => rl.question(question, ans => {
    rl.close();
    resolve(ans);
  }));
}

function setGrowthbook(content, enabled, clientKey) {
  let result = content;
  // 设置 ENABLED
  if (/^REACT_APP_GROWTHBOOK_ENABLED=.*/m.test(result)) {
    result = result.replace(/^REACT_APP_GROWTHBOOK_ENABLED=.*/m, `REACT_APP_GROWTHBOOK_ENABLED=${enabled}`);
  } else {
    result += `\nREACT_APP_GROWTHBOOK_ENABLED=${enabled}\n`;
  }
  // 设置 CLIENT_KEY
  if (enabled === 'true') {
    if (/^REACT_APP_GROWTHBOOK_CLIENT_KEY=.*/m.test(result)) {
      result = result.replace(/^REACT_APP_GROWTHBOOK_CLIENT_KEY=.*/m, `REACT_APP_GROWTHBOOK_CLIENT_KEY=${clientKey}`);
    } else {
      result += `REACT_APP_GROWTHBOOK_CLIENT_KEY=${clientKey}\n`;
    }
  }
  return result;
}

async function main() {
  const envExamplePath = path.join(__dirname, '.env.example');
  const envPath = path.join(__dirname, '.env');

  // 1. Ask for subenv
  let subenvValue = await ask('Please enter the test environment number (e.g. 6, 17, press Enter for default 17): ');
  if (!subenvValue) subenvValue = '17';

  // 2. Ask for Growthbook
  let enableGrowthbook = await ask('Enable Growthbook? (y/n, default n): ');
  enableGrowthbook = enableGrowthbook.trim().toLowerCase();
  let growthbookEnabled = 'false';
  let growthbookClientKey = '';
  if (enableGrowthbook === 'y' || enableGrowthbook === 'yes') {
    growthbookEnabled = 'true';
    growthbookClientKey = await ask('Please enter REACT_APP_GROWTHBOOK_CLIENT_KEY (press Enter to leave empty): ');
    if (!growthbookClientKey) {
      growthbookEnabled = 'false';
    }
  }

  // 3. Generate content
  let envContent = readFile(envExamplePath);
  envContent = replaceSubenv(envContent, subenvValue);
  envContent = setGrowthbook(envContent, growthbookEnabled, growthbookClientKey);
  writeFile(envPath, envContent);

  console.log(`.env file generated. Test environment: test${subenvValue}.shub.us, Growthbook ${growthbookEnabled === 'true' ? 'enabled' : 'disabled'}`);
}

main();
