{"TableSummary": "Table Summary", "SubmitOrderPromptDescription": "Cart will be locked until payment is completed", "OrderPlaced": "Order placed", "PendingPayment": "Pending payment", "AlipayMiniProgramAndPayByCashOnly": "Proceed to the counter to pay", "SelectPaymentMethod": "Select payment method", "CreatedOrderTime": "Created at {{submittedTime}}", "UnableBackMenuTitle": "Want to view the menu?", "UnableBackMenuDescription": "You may only return to the menu once you <bold>completed your payment</bold>. Select ‘Pay Now’ to proceed.", "UnableBackMenuAndPendingPaymentDescription": "You may only return to the menu once you <bold>completed your payment</bold>. Select payment method to proceed", "UnableBackMenuDynamicUrlExpiredDescription": "QR has expired, please ask the cashier for a new QR code and scan again to Add items", "AddItems": "Add Items", "ReceiptNumberInValidTitle": "Order does not exist", "ReceiptNumberInValidDescription": "Please return to the cart and try again. You may also reach out to the staff for assistance", "ReturnToCart": "Return to cart", "ErrorOrderCreationLimitedTitle": "Thanks for your interest!", "ErrorOrderCreationLimitedDescription": "This store is not accepting orders at the moment. Please check back soon.", "ErrorStoreIdIsRequiredTitle": "No store selected (#393474)", "ErrorStoreIdIsRequiredDescription": "Please scan the table QR code again.", "ErrorStoreOutOfStockTitle": "We're sorry, some items are unavailable (#393478)", "ErrorStoreOutOfStockDescription": "Some items in your cart are sold out or unavailable. Please edit the cart and try again.", "ErrorCartHasInvalidBirItemTitle": "Invalid item in cart (#393479)", "ErrorCartHasInvalidBirItemDescription": "Oops! There is an invalid product in your cart that cannot be processed. Please remove the item and try again", "MakePayment": "Make Payment"}