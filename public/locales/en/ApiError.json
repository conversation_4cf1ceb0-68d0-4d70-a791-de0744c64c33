{"40000Title": "Service Unavailable (Error: {{error_code}})", "40000Description": "Our service is temporarily unavailable, please try again later.", "40001Title": "Verification Failed (Error: {{error_code}})", "40001Description": "An error occurred while verifying your details. Please try again.", "40002Title": "Verification Failed (Error: {{error_code}})", "40002Description": "An error occurred while verifying your details. Please try again.", "40003Title": "Verification Failed (Error: {{error_code}})", "40003Description": "An error occurred while verifying your details. Please try again.", "40008Title": "Incomplete delivery info (Error: {{error_code}})", "40008Description": "Please complete your delivery information and try again.", "40009Title": "Out of Range (Error: {{error_code}})", "40009Description": "The delivery address is out of range, please key in your address again.", "40012Title": "Verification Failed (Error: {{error_code}})", "40012Description": "An error occurred while verifying your details. Please try again.", "40013Title": "Store is Closed (Error: {{error_code}})", "40013Description": "The store is closed at the moment. Please try again later.", "40015Title": "Unable to apply CashBack (Error: {{error_code}})", "40015Description": "An error occurred while applying CashBack. Please try again.", "40016Title": "Verification Failed (Error: {{error_code}})", "40016Description": "An error occurred while verifying your details. Please try again.", "40017Title": "We're sorry, pre-order time not available", "40017Description": "Apologies, the pre-order time needs to be a minimum of 1 hour in advance. Please select the time again.", "40018Title": "Verification Failed (Error: {{error_code}})", "40018Description": "An error occurred while verifying your details. Please try again.", "40019Title": "Out of Range (Error: {{error_code}})", "40019Description": "The delivery address is out of range, please key in your address again. ", "40020Title": "Insufficient CashBack (Error: {{error_code}})", "40020Description": "Insufficient CashBack balance. Please try again.", "40022Title": "Unable to process order (Error: {{error_code}})", "40022Description": "This store no longer supports pre-order, please try again.", "41000Title": "Time slot exceeded order limit (Error: {{error_code}})", "41000Description": "The selected time slot is no longer available. We have selected the next available time.", "41014Title": "No items in cart (Error: {{error_code}})", "41014Description": "There are no items in your cart, please reorder again.", "41026Title": "Payment Failed (Error: {{error_code}})", "41026Description": "We could not process your payment. Please check your network and try again.", "40025Title": "Unable to access storage", "40025Description": "<PERSON><PERSON> is not available.", "40026Title": "Unable to save data", "40026Description": "Data should not be empty.", "40027Title": "Store is Closed (Error: 40027)", "40027Description": "The store is closed at the moment. Please try again later.", "54012Title": "We're sorry, some items are unavailable (#54012)", "54012Description": "Some items in your cart are sold out or unavailable. Please edit the cart and try again.", "54013Title": "Out of Stock", "54013Description": "One or more items in your cart are currently out of stock. Please edit your cart and checkout again (Error: 54013).", "41016Title": "Cancellation Error (Error: 41016)", "41016Description": "Please select a valid cancellation reason and try again.", "41017Title": "Cancellation Error (Error: 41017)", "41017Description": "Please specify cancellation reason and try again.", "40024Title": "Oops! This is awkward…", "40024Description": "Looks like you’ve entered this email before. Please try logging in or use a different email address.", "40028Title": "Submission Failed (Error: 40028)", "40028Description": "This store review has already submitted.", "54023Title": "Cancellation Error (Error: 54023)", "54023Description": "Your order cannot be cancelled. Please contact customer support if you wish to proceed.", "54028Title": "Cancellation Error (Error: 54028)", "54028Description": "Your order cannot be cancelled. Please contact customer support if you wish to proceed.", "51001Title": "You’ve submitted a report", "51001Description": "Rest assured we have received your previous submission. We sincerely apologies for the inconvenience caused and our Customer Care team will be in touch shortly.", "51002Title": "Submission failed", "51002Description": "This restaurant uses its own delivery drivers. Please contact them directly to resolve the issue.", "51003Title": "Submission failed", "51003Description": "You can only submit a report after your food has been delivered. If you are having issues with your order, you can contact us via live chat by clicking on the \"Need Help?\" button on your order tracking page.", "51004Title": "Submission failed", "51004Description": "You are unable to submit a report as this is not a delivery order. Please send us an <NAME_EMAIL> if you've got any feedback.", "57001Title": "Change shipping type error (#57001)", "57001Description": "Rider has been found", "57002Title": "Change shipping type error (#57002)", "57002Description": "Change shipping type failed", "57003Title": "Change shipping type error (#57003)", "57003Description": "Only support delivery order to change shipping type", "57004Title": "Change shipping type error (#57004)", "57004Description": "Only support using StoreHub logistics delivery order to change shipping type", "57005Title": "Change shipping type error (#57005)", "57005Description": "Can not change to pickup now", "57006Title": "Change shipping type error (#57006)", "57006Description": "Cancel order failed, generated vouchers have been used", "57008Title": "Unable to apply CashBack (#57008)", "57008Description": "An error occurred while applying CashBack. Please try again", "57009Title": "Minimum spend not met (#57009)", "57009Description": "You haven't reached the minimum order amount. Please edit your cart and try again", "57010Title": "Unable to apply CashBack (#57010)", "57010Description": "An error occurred while applying CashBack. Please try again", "57011Title": "Payment method unavailable (#57011)", "57011Description": "Please choose a different payment method to continue", "57013Title": "Unable to process order (#57013)", "57013Description": "The selected shipping method is not supported. Please select another shipping method and try again", "57014Title": "Failed to Place Order", "57014Description": "Oops! There is an invalid product in your cart that cannot be processed. Please remove the item and try again", "41027Title": "Online payment is not supported", "41027Description": "Online payment via Beep is not supported at this store.", "B0001Description": "An error occurred. Exit app and try again", "41001ShortDescription": "Please enter a valid phone number", "394761ShortDescription": "Please enter a valid phone number", "395011ShortDescription": "Please enter a valid phone number", "394755Title": "Reached Daily Limit", "394755ShortDescription": "You have exceeded your daily SMS limit", "394755Description": "Your OTP request has reached a daily limit, please try again with another phone number", "394756Title": "OTP Verification Failed", "394756Description": "We have detected unusual traffic from your network, please try again later", "394757ShortDescription": "You are being rate limited", "395012ShortDescription": "Sending SMS to this number is not supported", "UnableToPlaceOrder": "Unable to place order", "HasBeenPlacedContentDescription": "Someone from your table is in the process of checking out, please <bold>complete payment</bold> before placing a new order", "ViewOrder": "View order", "NoInternetConnection": "No internet connection", "UnexpectedErrorOccurred": "Oops! An unexpected error has occurred"}