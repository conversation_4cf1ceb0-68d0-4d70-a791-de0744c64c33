{"JoinNow": "Join Now", "JoinOurMembership": "Join our Membership", "TermsAndConditionsDescription": "By tapping on Join Now, you agree to the <1>Term & Conditions</1>", "MembershipUnavailableTitle": "Oops, join membership for this store is unavailable yet", "MembershipUnavailableDescription": "Ask the merchant to enable the membership!", "MembershipDetailPageTitle": "Membership - {{merchantDisplayName}}", "CashbackDetailPageTitle": "Store Cashback Details", "CashbackBalanceTitle": "Cashback Balance", "OrderRedeemButtonText": "Order & redeem now", "MerchantDisabledMembershipTitle": "Oops! Access to this page is restricted.", "MerchantDisabledMembershipDescription": "This page is exclusively for our registered members. It seems like you’re not signed up yet! Please make sure you are logged in with the correct phone number.", "DefaultNewMemberTitle": "Yay! You are now a Member!", "DefaultNewMemberDescription": "Enjoy exclusive rewards.", "EnabledPointsNewMemberTitle": "Yay! You will earn points from this transaction", "EnabledPointsNewMemberDescription": "You have joined as a Member", "RedeemCashbackNewMemberTitle": "Yay! You are now a Member!", "RedeemCashbackNewMemberDescription": "Your cashback balance will be auto redeemed at the cashier.", "EarnedCashbackNewMemberTitle": "You’ve claimed {{cashbackValue}} cashback and are now a Member!", "ClaimedCashbackRepeatNewMemberTitle": "Oops! You've already claimed cashback for this order", "ClaimedCashbackRepeatNewMemberDescription": "Yay! You are now a Member", "ClaimedCashbackBySomeElseNewMemberTitle": "Oops! Someone has already claimed cashback for this order", "ClaimedCashbackBySomeElseNewMemberDescription": "Yay! You are now a Member", "NotClaimExpiredNewMemberTitle": "Oops! This QR code has already expired", "NotClaimExpiredNewMemberDescription": "Yay! You are now a Member", "NotClaimCancelledNewMemberTitle": "This transaction has been cancelled/refunded.", "NotClaimCancelledNewMemberDescription": "Yay! You are now a Member", "NotClaimReachLimitNewMemberTitle": "Oops, you've exceeded your cashback limit for today.", "NotClaimReachLimitNewMemberDescription": "Yay! You are now a Member", "NotClaimReachMerchantLimitNewMemberTitle": "The cashback is processed, you will receive a confirmation SMS.", "NotClaimReachMerchantLimitNewMemberDescription": "Yay! You are now a Member", "NotClaimDefaultNewMemberTitle": "Something went wrong. Please scan the QR again, or ask the staff for help.", "NotClaimDefaultNewMemberDescription": "Yay! You are now a Member", "DefaultReturningMemberMessage": "You are already a Member. Enjoy your rewards!", "EnabledPointsReturningMemberTitle": "Yay! You will earn points from this transaction", "RedeemCashbackReturningMemberMessage": "Your cashback balance will be auto redeemed at the cashier", "ThanksComingBackReturningMemberTitle": "Thanks for coming back!", "ThanksComingBackReturningMemberDescription": "Visit us again next time.", "EarnedCashbackReturningMemberTitle": "You’ve claimed {{cashbackValue}} cashback!", "EarnedCashbackReturningMemberDescription": "Check your total balance", "ClaimedCashbackRepeatReturningMemberTitle": "Oops! You've already claimed cashback for this order", "ClaimedCashbackRepeatReturningMemberDescription": "Check your total balance", "ClaimedCashbackBySomeElseReturningMemberTitle": "Oops! Someone has already claimed cashback for this order", "NotClaimExpiredReturningMemberTitle": "Oops! This QR code has already expired", "NotClaimCancelledReturningMemberTitle": "This transaction has been cancelled/refunded.", "NotClaimReachLimitReturningMemberTitle": "Oops, you've exceeded your cashback limit for today.", "NotClaimReachMerchantLimitReturningMemberTitle": "The cashback is processed, you will receive a confirmation SMS.", "NotClaimDefaultReturningMemberTitle": "Something went wrong. Please scan the QR again, or ask the staff for help.", "ReceiptClaimedRepeatTitle": "Oops! You have already claimed {{receiptRewards}} for this order", "ReceiptNotClaimedReachLimitTitle": "You’ve reached the daily {{receiptRewardType}} claim limit", "ReceiptNotClaimedDefaultTitle": "Something went wrong with claiming {{receiptRewards}}", "ReceiptNotClaimedDefaultDescription": "Please scan the QR again, or ask the staff for help.", "ReceiptClaimedSomeElseTitle": "Oops! Someone else has already claimed {{receiptRewards}} for this order", "ReceiptNotClaimedExpiredTitle": "Oops! This QR code has already expired", "ReceiptNotClaimedCancelledNoTransactionTitle": "Transaction error. Could not claim rewards", "ReceiptBindCustomerFailedTitle": "Something went wrong.", "ReceiptEarnedOnlyCashbackTitle": "You’ve claimed {{receiptCashbackValue}} cashback!", "ReceiptEarnedOnlyPointsTitle": "You’ve claimed {{receiptPointsValue}} points!", "ReceiptEarnedPointsAndCashbackTitle": "You’ve claimed {{receiptPointsValue}} points & {{receiptCashbackValue}} cashback!", "YouAreNowAMember": "You are now a Member", "DiscountValueText": "{{discount}} OFF", "History": "History", "Expired": "Expired", "ExpiringInDays": "Expiring in {{count}} day", "ExpiringInDays_plural": "Expiring in {{count}} days", "ExpiringToday": "Expiring today", "Redeemed": "Redeemed", "MinConsumption": "Min. spend {{amount}}", "MinSpend": "Min. spend: <0>{{amount}}</0>", "ValidUntil": "Valid until {{date}}", "Joining": "Joining...", "ClaimPromotion": "Claim Promotion", "UniquePromoHeaderTitle": "Promotion", "UniquePromoDownloadBannerText": "Get reminder for your promotions on Beep app now", "UniquePromoDuplicatedTitle": "Please try another QR code to claim promotion", "UniquePromoDuplicatedDescription": "This promotion has been claimed", "UniquePromoExpiredTitle": "Please try another QR code to claim promotion", "UniquePromoExpiredDescription": "Failed to claim. Promotion expired.", "UnlockNextLevelMessage": "Spend {{unlockSpendPrice}} more by {{nextReviewDate}} to unlock {{nextLevelName}}", "PointsUnlockNextLevelMessage": "Collect {{unlockCollectPoints}} points by {{nextReviewDate}} to unlock {{nextLevelName}}", "LevelCompletedMessage": "Enjoy {{currentLevelName}} benefits until {{nextReviewDate}}", "LevelMaintainMessage": "Spend {{maintainSpendPrice}} more by {{nextReviewDate}} to maintain {{currentLevelName}}", "PointsLevelMaintainMessage": "Collect {{maintainCollectPoints}} points by {{nextReviewDate}} to maintain {{currentLevelName}}", "UnlockNextTierMessage": "<0>{{totalSpendPrice}} / {{nextTierSpendingThresholdPrice}}</0> by {{nextReviewDate}} to unlock {{nextTierName}}", "PointsUnlockNextTierMessage": "<0>{{pointsTotalEarned}} / {{nextTierPointsThreshold}} Points</0> by {{nextReviewDate}} to unlock {{nextTierName}}", "TiersCompletedMessage": "Enjoy {{currentTierName}} benefits until {{nextReviewDate}}", "TierMaintainMessage": "<0>{{totalSpendPrice}} / {{maintainSpendPrice}}</0> by {{nextReviewDate}} to maintain {{currentTierName}}", "PointsTierMaintainMessage": "<0>{{pointsTotalEarned}} / {{maintainTotalPoints}} Points</0> by {{nextReviewDate}} to maintain {{currentTierName}}", "LevelUpdateRuleText": "Your progress updates on a daily basis", "EarnedCashbackTitle": "You’ve claimed {{cashbackValue}} cashback!", "EarnedCashbackDescription": "Check your total balance", "ClaimedProcessingMessage": "You've earned more cashback! We'll add it once it's been processed.😉", "ClaimedRepeatMessage": "You've already earned cashback for this receipt.👍", "ClaimedSomeoneElseMessage": "Someone else has already earned cashback for this receipt.😅", "NotClaimedExpiredMessage": "This cashback has expired and cannot be earned anymore.😭", "NotClaimedCancelledMessage": "This transaction has been cancelled/refunded.", "NotClaimedReachLimitMessage": "Oops, you've exceeded your cashback limit for today.", "NotClaimedReachMerchantLimitMessage": "The cashback is processed, you will receive a confirmation SMS.", "NotClaimedMessage": "Something went wrong. Please scan the QR again, or ask the staff for help.", "GetRewards": "Get Re<PERSON>s", "RewardsCostOfPointsText": "{{costOfPoints}} points", "RewardsCostOfPointsConfirmMessage": "Are you sure want to spend {{costOfPoints}} points?", "PointsRewardClaimedTitle": "Successfully redeemed!", "PointsRewardClaimedDescription": "Added to '<PERSON> Rewards' tab, ready for you to view and use.", "Points": "Points", "MyRewards": "My Rewards", "Rewards": "Rewards", "Reward": "<PERSON><PERSON>", "UniquePromoListEmptyTitle": "You don't have any rewards yet", "EarnedPointsPromptDrawerTitle": "How to use points", "EarnedPointsPromptDrawerDescription": "Earn points with every purchase at your favourite stores. ", "EarnedPointsPromptRedeemOnlineTitle": "Redeem rewards online", "EarnedPointsPromptRedeemOnlineDescription": "Exchange points for exclusive rewards on your Membership page.", "EarnedPointsPromptRedeemInStoreTitle": "Redeem rewards in-store", "EarnedPointsPromptRedeemInStoreDescription": "Tell the cashier you want to redeem your reward and provide your phone number.", "PromotionIsNotRedeemableTitle": "Unable to get reward", "PromotionIsNotRedeemableDescription": "Something went wrong. Please try again later, or try a different reward", "InsufficientPointsTitle": "Insufficient points", "InsufficientPointsDescription": "You don’t have enough points to get this reward. Please refresh the page & try again", "PointsDetail": "Points Detail", "PointsBalanceTitle": "Points Balance", "CustomerPoints": "{{customerAvailablePointsBalance}} Points", "ExpirationDurationDays": "{{count}} day", "ExpirationDurationDays_plural": "{{count}} days", "ExpirationDurationWeeks": "{{count}} week", "ExpirationDurationWeeks__plural": "{{count}} weeks", "ExpirationDurationMonths": "{{count}} month", "ExpirationDurationMonths_plural": "{{count}} months", "PointsExpiringTimePrompt": "Points expire {{expirationDuration}} after they were earned", "HowToUsePoints": "How to use points", "PointsHistory": "Points History", "ChangePointsText": "{{changeValue}} points", "NoPointsCollectedTitle": "No points collected yet :(", "NoPointsCollectedDescription": "Earn {{emptyPromptEarnPointsNumber}} points with every {{emptyPromptBaseSpent}} spent", "CashbackDetails": "Cashback Details", "CashbackBalance": "Cashback Balance", "HowToUseCashback": "How to use cashback", "CashbackHistory": "Cashback History", "NoCashbackCollectedTitle": "No cashback earned yet", "NoCashbackCollectedDescription": "Earn {{cashbackPercentage}}% cashback with every purchase", "EarnedCashbackPromptDrawerTitle": "How to use cashback", "EarnedCashbackPromptRedeemOnlineTitle": "Redeem online", "EarnedCashbackPromptRedeemOnlineDescription": "Use your cashback balance at the Store checkout page for instant discounts. ", "EarnedCashbackPromptRedeemInStoreTitle": "Redeem in-store", "EarnedCashbackPromptRedeemInStoreDescription": "Tell the cashier you want to redeem your cashback and provide your phone number.", "StoreCreditDetails": "Store Credit Details", "StoreCreditsBalance": "Store Credits Balance", "HowToUseStoreCredits": "How to use store credits", "StoreCreditsHistory": "Store Credits History", "NoStoreCreditsCollectedTitle": "No store credits earned yet", "NoStoreCreditsCollectedDescription": "Earn {{emptyPromptEarnStoreCreditsNumber}} store credits with every {{emptyPromptBaseSpent}} spent", "EarnedStoreCreditsPromptDrawerTitle": "How to use store credits", "EarnedStoreCreditsPromptRedeemInStoreTitle": "Redeem in-store", "EarnedStoreCreditsPromptRedeemInStoreDescription": "During payment, tell the cashier you want to use your store credit balance and provide your phone number.", "MembershipBenefits": "Membership Benefits", "Earn": "Payment", "Earned": "Earned", "Returned": "Payment Refunded", "Refunded": "Payment Refunded", "Spend": "Redemption", "Adjustment": "Adjustment", "ManualAdjustment": "Manual Adjustment", "ImportedEarned": "Account <PERSON>", "ImportedSpent": "Account <PERSON>", "Pending": "Pending", "PointsHistoryExpired": "Expiry", "StoreCredits": "Store Credits", "JoinMembershipRewardsPrompt": "Join as a Member and Get these Perks!", "JoinMembershipGetRewardsTitle": "You'll get", "JoinMembershipGetRewardsDescription": "Claim now and enjoy these perks!", "Discounts": "Discounts", "Vouchers": "Vouchers", "UnlockOneTierLevelPrompt": "You’ll unlock", "UnlockLevelPrompt": "You’ll unlock this tier", "UnlockHigherLevelPrompt": "Become a {{levelName}} Member and unlock", "ViewAll": "View all", "ErrorReceiptGetRewardsNoTransaction": "Transaction not found. <0></0>Please scan the QR again later", "ErrorGetRewardsExpiredTitle": "Oops! This QR code has already expired", "ErrorGetRewardsCanceledRefundTitle": "Transaction was cancelled. Could not claim rewards", "BirthdayCampaignTitle": "Get Goodies on Your Birthday!", "BirthdayCampaignDescription": "Complete your profile to unlock", "ViewProfile": "View profile", "HowToUse": "How to use", "UniquePromoDetails": "My Reward Details", "ViewDetails": "View details", "POS": "POS", "Ecommerce": "Webstore", "BeepPickup": "<PERSON><PERSON>", "BeepDelivery": "Beep Delivery", "BeepTakeaway": "QR Order and Pay - Takeaway", "BeepDineIn": "QR Order and Pay - Dine-in", "UniquePromoApplicableProductsTitle": "Applicable products", "UniquePromoAllProductsText": "All products", "UniquePromoSelectedProductsText": "Selected products", "UniquePromoApplicableStoresTitle": "Applicable stores", "UniquePromoAllStoresText": "All stores", "UniquePromoSelectedStoresText": "Selected stores", "UniquePromoRedeemOnlineTitle": "Redeem online", "UniquePromoRedeemOnlineDescription": "Apply this promotion during checkout on any of the order methods below:", "UniquePromoRedeemInStoreTitle": "Redeem in store", "UniquePromoRedeemInStoreDescription": "Show this promotion to the cashier for redemption", "PointsRewardDetails": "Get Reward Details", "PointsRewardValidityTitle": "Validity", "PointsRewardValidityText": "{{count}} day", "PointsRewardValidityText_plural": "{{count}} days", "PointsRewardMinSpendTitle": "<PERSON><PERSON>", "PointsRewardApplicableProductsTitle": "Applicable products", "PointsRewardAllProductsText": "All products", "PointsRewardSelectedProductsText": "Selected products", "PointsRewardApplicableStoresTitle": "Applicable stores", "PointsRewardAllStoresText": "All stores", "PointsRewardSelectedStoresText": "Selected stores", "PointsRewardRedeemOnlineTitle": "Redeem online", "PointsRewardRedeemOnlineDescription": "Apply this promotion during checkout on any of the order methods below:", "PointsRewardRedeemInStoreTitle": "Redeem in store", "PointsRewardRedeemInStoreDescription": "Show this promotion to the cashier for redemption", "GetReward": "Get reward", "ViewReward": "View reward", "EnterRewardCodeHere": "Have a promo/voucher code? Enter here", "FillInvalidPromoCode": "Please enter a valid code. Only A-Z, a-z and 0-9 are allowed", "SearchRewardEmptyResult": "Unable to find this promo/voucher code", "CustomerNoRewardResultDescription": "Enter a Promo/Voucher Code to search for it", "YourVouchers": "Your voucher(s)", "RewardDetailApplicableMerchantsTitle": "Applicable merchants", "RewardAllMerchantsText": "All Merchants", "RewardSelectedMerchantsOnlyText": "Selected Merchants Only", "VoucherPromoDetails": "Voucher & Promo Details", "RewardDetailAllProductsText": "All products", "RewardDetailSelectedProductsText": "Selected products", "RewardDetailAllStoresText": "All stores", "RewardDetailSelectedStoresText": "Selected stores", "RewardDetailApplicableProductsTitle": "Applicable products", "RewardDetailApplicableStoresTitle": "Applicable stores", "RewardDetailRedeemOnlineTitle": "Redeem online", "RewardDetailRedeemOnlineDescription": "Apply this promotion during checkout on any of the order methods below:", "RewardDetailRedeemInStoreTitle": "Redeem in store", "RewardDetailRedeemInStoreDescription": "Show this promotion to the cashier for redemption", "ApplyNow": "Apply now", "MembershipDisabledTitle": "Oops!", "MembershipDisabledDescription": "Membership is unavailable. Please check back later or contact {{merchantDisplayName}} for more information.", "SeamlessLoyaltyCashbackDescription": "Total balance to redeem", "SeamlessLoyaltyUserGreetings": "Thanks for coming back! Visit us<1></1>again next time.", "SeamlessLoyaltyNewUserGreetings": "Thanks for signing up with us!", "SeamlessLoyaltyCashRedeemAlert": "Your cashback balance will be auto redeemed at the cashier", "SeamlessLoyaltyWebTitle": "Please choose the<0></0>app below to continue", "SeamlessLoyaltyBeepAppButtonText": "Scan with <PERSON><PERSON> App", "SeamlessLoyaltyTNGMiniProgramButtonText": "Scan with TNG eWallet App"}