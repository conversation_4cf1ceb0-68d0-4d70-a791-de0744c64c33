{"MyVouchersAndPromos": "My Vouchers & Promos", "DiscountValueText": "{{discount}} OFF", "YourVouchers": "Your voucher(s)", "MinConsumption": "Min. spend {{amount}}", "ExpiringInDays": "Expiring in {{count}} day", "ExpiringInDays_plural": "Expiring in {{count}} days", "ExpiringToday": "Expiring today", "PromoValidUntil": "Valid until {{date}}", "CustomerNoRewardResultDescription": "Enter a Promo Code to search for it", "FillInvalidPromoCode": "Please enter a valid promo code. Only A-Z, a-z and 0-9 are allowed", "SearchRewardEmptyResult": "Unable to find this promo/voucher code", "ViewDetails": "View details", "VoucherPromoDetails": "Voucher & Promo Details", "RewardDetailApplicableProductsTitle": "Applicable products", "RewardDetailAllProductsText": "All products", "RewardDetailSelectedProductsText": "Selected products", "RewardDetailApplicableStoresTitle": "Applicable stores", "RewardDetailAllStoresText": "All stores", "RewardDetailSelectedStoresText": "Selected stores", "HowToUse": "How to use", "RewardDetailRedeemOnlineTitle": "Redeem online", "RewardDetailRedeemOnlineDescription": "Apply this promotion during checkout on any of the order methods below:", "RewardDetailRedeemInStoreTitle": "Redeem in store", "RewardDetailRedeemInStoreDescription": "Show this promotion to the cashier for redemption", "POS": "POS", "Ecommerce": "Webstore", "BeepPickup": "<PERSON><PERSON>", "BeepDelivery": "Beep Delivery", "BeepTakeaway": "QR Order and Pay - Takeaway", "BeepDineIn": "QR Order and Pay - Dine-in", "ApplyNow": "Apply now", "Success": "Apply Success", "EnterPromoCodeHere": "Enter Promo Code Here", "41003UniversalPromotionError": "Promo code is applicable for delivery orders only", "54403NotStartOrExpired": "This promotion has expired or has not started (#54403)", "54404WeekdayNotMatch": "This promo code can only be used on {{validDaysString}}.", "54405TimeNotMatch": "This promo code can only be used between {{validTimeFrom}} to {{validTimeTo}}", "54406FreePromotionNotMatchCondition": "This promotion cannot be used because did not meet minimum condition yet (Error: 54406)", "54301InvalidPromotionCode": "This promo code does not exist (#54301)", "54410DeletedPromotion": "This promotion is no longer valid (Error: 54410)", "54401ProductNotExist": "This promo code is invalid (Error: 54401)", "54407NotMatchCondition": "This promotion can only be applied when the promo criteria has been met (#54407)", "54408RequireSameBusiness": "This promo code can only be used for a specific store outlet. Please check with the restaurant for more info (#54408)", "54409StoreDoesNotSatisfy": "This promo code can only be used for a specific store outlet. Please check with the restaurant for more info (#54409)", "54411PromotionReachesMaxClaimCount": "This promotion cannot be used because it's fully redeemed (Error: 54411)", "54412RequireCustomer": "This promotion cannot be used. Please check with the seller for more info (Error: 54412)", "54413ReachCustomerClaimCountLimit": "This promotion cannot be used because you've reach the claim limit (Error: 54413)", "54414RequireFirstTimePurchase": "This promotion cannot be used because it is only applicable for first purchase (Error: 54414)", "54415NoSourceProperty": "This promo code is invalid (Error: 54415)", "54416AppliedSourceDoesNotMatch": "This promotion is only for {{supportedChannel}} order (Error: 54416)", "54417NotMatchMinSubtotalConsumingPromo": "This promotion cannot be used because the minimum order value is not matched ({{minSubtotalConsumingPromo}}) (Error: 54417)", "54418NotMatchAppliedClientType": "This promo code can only be used on {{supportClient}} (#54418)", "54419OnlyApplicableForParticipating": "Sorry, this promo code is only applicable for participating restaurants (#54419)", "54420ReachMaxBusinessClaimCount": "Sorry, this promo code has been fully redeemed (#54420)", "60001NotExisted": "This voucher code does not exist", "60002NotActive": "This voucher cannot be used because it's not activated yet (Error: NOT ACTIVE)", "60003Expired": "This voucher cannot be used because it's expired (Error: EXPIRED)", "60004LessThanMinSubtotalConsumingVoucher": "The minimum requirements to use this voucher not met (Error: MIN_SPEND_NOT_SATISFIED)", "60005ChannelNotMatch": "This voucher cannot be used here (Error: CHANNEL NOT MATCH)", "60006ApplyFailed": "This voucher code is invalid (Error: FAILED)", "60007Forbidden": "This voucher code is invalid (Error: FORBIDDEN)", "60009VoucherHasBeenUsed": "This voucher has been redeemed (Error: USED)", "60008UpdateVoucherStatusFailed": "This voucher code is invalid (Error: UPDATE STATUS FAILED)", "60000InvalidPromotionOrVoucher": "This code is invalid (Error: 60000)", "60010VoucherNotMatchSource": "This voucher is only valid on app. (Error: 60010)", "PromoExpiredLabel": "Expired", "PromoAppOnlyLabel": "App Only", "PromoRedeemedLabel": "Redeemed", "PromoInvalidLabel": "Invalid", "PromoTitleAbsolute": "<0></0> off", "PromoTitlePercentage": "{{amount}}% off", "PromoTitleFreeShipping": "Free Shipping", "Voucher": "Voucher", "PromoValidFrom": "Valid from {{date}}", "InapplicableVouchers": "Inapplicable voucher(s)", "PromoInvalid": "Sorry, the promo/voucher code is invalid", "NoVouchersYet": "You don't have any vouchers yet"}