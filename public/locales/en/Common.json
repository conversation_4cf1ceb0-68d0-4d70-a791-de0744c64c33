{"StoreHub": "StoreHub", "OK": "OK", "Open": "Open", "Close": "Close", "Cancel": "Cancel", "Remove": "Remove", "PreOrder": "Pre-order", "Continue": "Continue", "Confirm": "Confirm", "ConfirmCloseButtonText": "No, I’m not", "Edit": "Edit", "Sorry": "Sorry", "Processing": "Processing", "Loading": "Loading", "BackToHome": "Back to home", "Save": "Save", "Complete": "Complete", "Free": "Free", "ContactUs": "Contact Us", "OrderDetails": "Order Details", "TermsAndPrivacyDescription": "By tapping to continue, you agree to our<1/><2>Terms of Service</2> and <5>Privacy Policy</5>.", "DefaultMessage": "Oops, please scan QR to claim again.", "InvalidMessage": "After your purchase, just scan your receipt and enter your mobile number to earn cashback for your next visit. It’s that simple!", "ClaimedFirstTimeTitle": "Awesome, you've earned your first cashback! 🎉", "ClaimedFirstTimeTitleInThankYou": "Awesome, you've earned {{currencySymbol}} {{cashback}} your first cashback! 🎉 ", "ClaimedNotFirstTimeTitleInThankYou": "You've earned {{currencySymbol}} {{cashback}} Store Cashback for this order. You can use this on your next purchase!", "ClaimedFirstTimeDescription": "Tap the button below to learn how to use your cashback.", "ClaimedNotFirstTimeTitle": "You've earned more cashback! 🎉", "ClaimedProcessing": "You've earned more cashback! We'll add it once it's been processed.😉", "ClaimedSomeoneElse": "Someone else has already earned cashback for this receipt.😅", "ClaimedRepeat": "You've already earned cashback for this receipt.👍", "NotClaimed": "Looks like something went wrong. Please scan the QR again, or ask the staff for help.", "NotClaimedExpired": "This cashback has expired and cannot be earned anymore.😭", "NotClaimedCancelled": "This transaction has been cancelled/refunded.", "NotClaimedReachLimit": "Oops, you've exceeded your cashback limit for today. The limit is {{claimCashbackCountPerDay}} time(s) a day. 😭", "NotClaimedReachMerchantLimit": "Sorry, Your transaction is pending, you will receive a SMS confirmation once your cashback is processed.", "NotSentOTP": "Oops! <PERSON><PERSON> not sent, please check your phone number and send again.", "SaveCashbackFailed": "Oops! please retry again later.", "ActivityIncorrect": "Activity incorrect, need retry.", "ClaimCashbackTitle": "Claim with your mobile number", "EarnClaimCashbackTitle": "Earn {{currencySymbol}} {{cashback}} CashBack with your Mobile Number", "CheckMyBalance": "Check My Balance", "Pay": "Pay", "PayNow": "Pay now", "GoBack": "Go back", "PlaceOrder": "Place order", "Back": "Back", "ClearAll": "Clear All", "TableIdText": "Table {{tableId}}", "MinimumConsumption": "<0>Min </0><1></1>", "MinimumOrder": "<0>Min Order.</0><1></1>", "RemainingConsumption": "<0>Remaining </0><1></1>", "ReceiptNumber": "Receipt Number", "Notes": "Notes", "PaymentFailed": "Payment Failed", "GotoPaymentFailedDescription": "We could not process your payment. Please check your network and try again.", "ServiceChargeTitle": "Service Charge {{serviceChargeRate}}", "Discount": "Discount", "Subtotal": "Subtotal", "BeepCashback": "<PERSON><PERSON>", "Tax": "Tax", "Total": "Total", "SoldOut": "Sold Out", "BestSeller": "Best seller", "ResendOTPTip": "Didn't receive the OTP?", "OTPSentTitle": "We’ve sent you a One-Time Password (OTP) to <1>{{phone}}</1>. Enter it below to continue.", "OTPResendTitle": "Resend code in {{currentOtpTime}}", "VerifyingCode": "Verifying Code", "ResendingCode": "Resending code", "EnterOTP": "Enter OTP", "OTPSentTip": "A code is sent to <1>{{phone}}</1>", "ResendViaSMS": "Resend Code Via SMS", "ResendViaWhatsAPP": "Resend Code Via WhatsAPP", "EnterPhoneNumber": "Enter phone number", "PeopleCountModalTitle": "Welcome! How many of you are dining today?", "ViewOrderDetails": "View order details", "Eep": "<PERSON>ep", "ErrorPageDescription": "Looks like something went wrong. Please scan the QR again, or ask the staff for help.", "NotFound": "Not Found", "PageNotFound": "Sorry page not found", "NoBusinessTitle": "Store Not Found", "NoBusinessDescription": "This store does not exist, please check your store name and try again.", "QROrderingDisabledDescription": "Oops, seems like this store no longer supports Online Ordering.", "NoDeliveryLocationDescription": "store no longer have address", "SelectStoreDescription": "Please select a store to continue…", "SelectStoreErrorMessage": "Something wrong, please try again later.", "FoodDelivery": "Food Delivery", "Delivery": "Delivery", "Pickup": "Pickup", "StoreLocation": "Store Location", "SelfPickup": "Self Pickup", "SelectYourPreference": "Select Your Preference", "DeliverTo": "Deliver To", "DeliverAt": "Deliver At", "DeliverOn": "Deliver On", "PickupAt": "Pickup At", "PickUpOn": "Pickup On", "openingHours": "Opening Hours", "DeliveryTime": "Delivery Time", "SelectedStore": "Selected Store", "WhereToDeliverFood": "Where to deliver your food", "PickupTime": "Pickup Time", "DeliverNow": "TODAY{{separator}} immediate", "DeliverToErrorMessage": "Sorry order location is too far from store", "Closed": "Closed", "DeliveryDetails": "Delivery Details", "PickUpDetails": "Pickup Details", "Name": "Name", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "DateOfBirth": "Date of Birth", "BirthdayInputPlaceholder": "DD/MM/YYYY", "MobileNumber": "Mobile Number", "AddDeliveryTimePlaceholder": "Add in your delivery time", "PickUpAtPlaceholder": "Add in your pick up time", "DeliveryFee": "Delivery Fee", "TakeawayFee": "Takeaway Fee", "FreeDeliveryPreviousPrompt": "Free Delivery with <1></1> & above", "FreeDeliveryPrompt": "Free Delivery {{freeShippingFormattedMinAmount}} & above", "SearchRestaurantPlaceholder": "Restaurants, dishes, cuisines", "NearbyRestaurants": "Restaurants Delivering Nearby", "DistanceText": "{{distance}} km", "SearchNotFoundStoreDescription": "Oops! Couldn’t find results for \"{{keyword}}\".<0></0>Let’s try a different restaurant", "FilterNotFoundStoreDescription": "Oops! That’s not on the menu yet!<0></0>Try filtering for other delicious items instead", "StartSearchDescription": "Let’s start a search for your favorite restaurant", "ClosedForNow": "Closed\nFor Now", "MvpDocumentTitle": "Beep | Food Delivery & Takeaway | Order Food Online Now", "MvpDocumentDescription": "The best restaurants near you now deliver! Get breakfast, lunch, dinner and treats delivered from your favorite local restaurants today.", "MvpDocumentKeywords": "food delivery, food takeaway, order food online, order online, restaurant delivery, cafe delivery", "OutOfDeliveryRange": "Out of Delivery Range", "OutOfDeliveryRangeDescription": "Sorry, your selected location is out of the delivery range of this store and hence is only available for Self Pickup orders", "SelfPickupOnly": "Self Pickup Only", "OfferDetails": "Offer Details", "MvpFreeDeliveryPrompt": "<0>Free Delivery above <1>{{minimumSpendForFreeDelivery}}</1></0>", "EnabledCashbackText": "{{cashbackRate}}% Store Cashback", "EnabledCashbackShortText": "{{cashbackRate}}% Cashback", "Mon": "Mon", "Tue": "<PERSON><PERSON>", "Wed": "Wed", "Thu": "<PERSON>hu", "Fri": "<PERSON><PERSON>", "Sat": "Sat", "Sun": "Sun", "Now": "Now", "Today": "Today", "Tomorrow": "Tomorrow", "Weekday": "Weekday", "Okay": "Okay", "DeliveryHours": "Delivery Hours", "AddPromoCode": "Add a promo code/voucher", "Voucher": "Voucher", "Promotion": "Promo", "ConnectionIssue": "Sorry there was some issue with the connection, please try again", "Immediate": "Immediate", "PreOrderTag": "Pre Order", "SelectTableNumber": "Select Table Number", "PleasePickOne": "Please Pick One", "Table": "Table", "DineIn": "Dine-in", "TakeAway": "Takeaway", "CustomerDetails": "Customer Details", "Login": "LOGIN", "LoginTip": "Enter your mobile number to proceed", "ContinueAsGuest": "Continue as Guest", "TryAgain": "Try Again", "Required": "Required", "Dismiss": "<PERSON><PERSON><PERSON>", "AmountTooLarge": "Amount Too Large", "AmountTooSmall": "Amount Too Small", "AuthenticationRequired": "Authentication Required", "BalanceInsufficient": "Balance Insufficient", "CardExpired": "Card Expired", "BankDeclined": "Bank Declined", "IncorrectCvc": "Incorrect Cvc", "PaymentGatewayError": "payment gateway error", "UnknownError": "Unknown Error", "OnlineBanking": "Online Banking", "CreditAndDebitCard": "Credit/Debit Card", "SelectStore": "Select a Store", "DeliveryHelpText": "Please key in your address to add items to your cart", "CompleteProfile": "Complete Profile", "CompleteProfileTip": "Share your details with us and stand a chance to enjoy future promotions!", "UnlockRewards": "Unlock Rewards", "UnlockMemberRewardsTip": "Complete your profile to access exclusive member rewards and discounts!", "AddNewAddress": "Add New Address", "SavedAddress": "Saved Address", "OutOfRange": "Out of Range", "ContactDetails": "Contact Details", "ContactTip": "Only change this if you're ordering for someone else and want us to contact them if there's an issue with the order. P.S. All order status updates will be sent to the contact person below.", "EditAddress": "Edit Address", "SaveChanges": "Save Changes", "CodeVerificationFailed": "Code verification failed", "Reorder": "Reorder", "AddressDetailLabel": "Label this address for easy reference", "Copy": "Copy", "PaymentMethod": "Payment Method", "Cashback": "Cashback", "ReceiptInfoForPickupId": "Order", "ReceiptInfoForOrderId": "Receipt", "OrderStatus": "Order Status", "StoreName": "Store Name", "DeliveryAddress": "Delivery Address", "StoreAddress": "Store Address", "Items": "Items", "ServiceCharge": "Service Charge", "Rounding": "Rounding", "ReportIssue": "Report Issue", "NeedHelp": "Need Help", "Help": "Help", "CommonErrorMessage": "Whoops, this page isn't available", "ErrorId": "Error ID: {{ id }}", "LowStockProductQuantity": "only {{quantityOnHand}} left", "EmailAddress": "Email Address (optional)", "MyOrderHistory": "My Order History", "TermsOfService": "Terms of Service", "PrivacyPolicy": "Privacy Policy", "Beep": "<PERSON><PERSON>", "GotIt": "Got It", "GotItQr": "GOT IT", "Beepit.com": "Beepit.com", "BeepApp": "Beep App", "BeepTngMiniProgram": "Beep Mini Program via the Touch ’n Go eWallet app", "BeepGCashMiniProgram": "Beep Mini Program via the GCash eWallet app", "EditCart": "<PERSON>", "TableNumber": "Table Number", "OrderNumber": "Order Number", "OrderHasPaidAlertTitle": "Order paid", "OrderHasPaidAlertDescription": " Someone from your table has paid for this order.", "SorryEmo": "Oh no :(", "SorryDescription": " Your order cannot be found.", "OrderNotFoundDescription": " Your order cannot be found. Please contact customer support if you wish to proceed.", "BackToMenu": "Back to Menu", "SomeoneElseIsPaying": "Someone else is paying", "SomeoneElseIsPayingDescription": "Someone else on the table is checking out the order. Please try again later.", "BackToTableSummary": "Back to Table Summary", "RefreshTableSummary": "Refresh table summary to continue", "RefreshTableSummaryDescription": "Some items in your group order has been added or removed.", "Refresh": "Refresh", "Menu": "<PERSON><PERSON>", "StoreCashbackPercentage": "{{cashbackPercentage}}% Store Cashback", "ReviewCart": "Review cart", "MinMumConsumptionButtonPrompt": "Min {{minimumConsumptionFormattedPrice}}", "RemoveAll": "Remove all", "Cart": "<PERSON><PERSON>", "RemoveItem": "Remove item", "InventoryItemsLeft": "{{inventory}} item(s) left", "ViewOrder": "View order", "OrderOngoing": "Order ongoing", "ProductsPromotionDescription": "Get <0>{{discountValue}} OFF</0> for {{discountProducts}} with <1>{{promotionCode}}</1>. Promo Code is valid till {{validDate}}", "StorePromotionDescription": "Get <0>{{discountValue}} OFF</0> with <1>{{promotionCode}}</1>. Promo Code is valid till {{validDate}}", "TakeAmountOffPromotionDescription": "<0>{{discountValue}} OFF</0> with promo code <1>{{promotionCode}}<1/>", "PercentagePromotionDescription": "<0>{{discountValue}} OFF</0> with promo code <1>{{promotionCode}}<1/>", "PromotionDescription": "<0>{{discountValue}} OFF</0> with promo code <1>{{promotionCode}}<1/>", "FreeDeliveryPromotionDescription": "<0>Free Delivery</0> with promo code <1>{{promotionCode}}<1/>", "FREEDELPromotionDescription": "Use <0>FREEDEL</0> to enjoy Free Delivery for your first 5KM", "PromotionOnlyMaxDiscountAmountPrompt": "capped at <0>{{maxValue}}</0>", "PromotionOnlyMinOrderAmountPrompt": "min. spend <0>{{minValue}}</0>", "PromotionPrompt": "capped at <0>{{maxValue}}</0> with min. spend <1>{{minValue}}</1>", "OnlyInBeepAppPrompt": "<0>Beep app</0> only", "FirstOrderOnly": "first time order only", "DeliveryOrderOnly": "delivery order only", "FirstDeliveryOrderOnly": "first time delivery order only", "FirstAppOnly": "first time order on <0>Beep app</0> only", "DeliveryAppOnly": "delivery order on <0>Beep app</0> only", "FirstDeliveryAppOnly": "first time delivery order on <0>Beep app</0> only", "NumSelected": "{{amount}} Selected", "Unavailable": "Unavailable", "SelectXOrMore": "Required - Select {{min}} or more", "SelectUpToX": "Select up to {{max}}", "SelectXToY": "Required - Select {{min}} to {{max}}", "SelectX": "Required - Select {{min}} only", "XItemsLeft": "{{amount}} item(s) left", "AddToCart": "ADD TO CART - {{price}}", "OutOfStock": "OUT OF STOCK", "SelectVariant": "SELECT VARIANT", "ExceedsMaximumStock": "EXCEEDS MAXIMUM STOCK", "StoreNotFound": "Oops! Store not found", "StoreNotFoundDesc": "This store may be temporarily unavailable or undergoing a delicious upgrade! Check back soon…", "SearchingProductsNoResult": "Oops! Couldn’t find results for \"{{searchingProductKeywords}}\"<0></0>Let’s try a different item", "MenuSearchingBoxPlaceholder": "What are you looking for?", "Restaurants": "Restaurants", "ShareTitle": "Hey foodie! Did you know {{storeName}} is on Beep? <PERSON><PERSON>, let's order", "MinOrderRM": "Min order {{minimumConsumptionFormattedPrice}}", "MinOrderDiffRM": "Min order  {{minimumConsumptionFormattedPrice}}, add  <1>{{formattedDiffPriceOnFulfillMinimumConsumption}}</1>  more to proceed", "PhoneNumberUnavailable": "Phone number unavailable", "SelectLocation": "Select Location", "SelectTimeSlot": "Select Time Slot", "TimeSlotUnavailable": "Time slot unavailable", "Reset": "Reset", "Apply": "Apply", "Searching": "Searching", "SelectNextAvailableTime": "Select next available time slot", "AddSpecialInstructions": "Add Special Instructions", "SpecialInstructions": "Special Instructions", "LengthLimitOfNotes": "{{inputLength}}/{{maxLength}} characters", "EgLessSugar": "Eg: Less sugar", "NetworkErrorTitle": "No internet connection", "NetworkErrorDescription": "Ugh! Something's not right with your internet, check your connection and try again", "UnknownErrorTitle": "Oops!", "UnknownErrorDescription": "Something went wrong, please try again", "SearchYourLocation": "Search your location", "AddressListEmptyDescription": "Enter your delivery location", "StoreListDrawerTitle": "Select store branch", "StoreListDrawerDescription": "{{totalOutlet}} outlets near you", "SelectStoreDefault": "Select Store", "Promos": "Promos", "Date": "Date", "Time": "Time", "ApplyCashbackFailedDescription": "Oops, unable to apply cashback. Please try again.", "RemoveCashbackFailedDescription": "Oops, unable to remove cashback. Please try again.", "CashbackInfoTitle": "Beep Cashback Info", "CashbackInfoDescription": "You can apply your cashback upon checkout. The amount may vary due to selected items or applied promo code/vouchers.", "CheckIfDrinkingAge": "Are you of legal drinking age?", "AlcoholLimitationsMY": "Our alcohol products can only be purchased by non-Muslims aged 21 years old and above.", "AlcoholLimitationsPH": "Our alcohol products can only be purchased by persons aged 18 years old and above.", "AlcoholNo": "NO, I'M NOT", "AlcoholYes": "YES, I AM", "AlcoholDenied": "Access Denied", "AlcoholNotAllowed": "Aww, sorry... This store sells alcoholic beverages. If it is not suitable, you can check out the other stores instead. 🙂", "TableNumberUpdatedTitle": "Your table number has been successfully updated!", "TableNumberUpdatedDescription": "You may proceed with your order at the new table where you are currently seated.", "TakeawayPackagingFee": "+ {{packagingFee}} for packaging", "Retry": "Retry", "SomethingWentWrongTitle": "Sorry, something went wrong", "SomethingWentWrongDescription": "Try reloading the page, we are working hard to fix this as soon as possible", "Redirecting": "Redirecting...", "UrlExpiredTitle": "Oops! This QR Code link has expired", "UrlExpiredDescription": "Please request a new QR Code with the cashier & scan again", "UrlExpiredButton": "Scan a new qr code", "LoginSessionExpiredTitle": "Oops! This session has expired", "LoginSessionExpiredDescription": "Please close this page and start again", "UnexpectedErrorOccurred": "Oops! An unexpected error has occurred", "More": "More", "UniquePromosCountText": "{{uniquePromosAvailableCount}} rewards!", "ErrorInputRequired": "Please fill {{fieldName}}, it is required!", "ErrorNotValidInteger": "Please fill a valid integer", "ErrorNumberOutOfRange": "{{fieldName}} between {{min}} and {{max}}", "ErrorNumberInvalidStep": "{{fieldName}} must be a multiple of {{step}}", "ErrorInputNumber": "Please fill a valid number", "ErrorEmailPattern": "Please fill a valid email", "ErrorInputPattern": "Please fill a valid {{fieldName}}", "ErrorNotValidBirthday": "Please select a valid date of birth (DD/MM/YYYY)", "ErrorBirthdayLaterThanToday": "Date cannot be later than today", "ErrorBirthdayTooOld": "Date cannot be earlier than {{oldestYears}}", "CompleteProfileTile": "Complete your Profile", "CompleteProfileDescription": "Unlock more rewards and special offers by completing your profile.", "CompleteBirthdayTitle": "Get Birthday Goodies", "CompleteBirthdayDescription": "Provide your birth date to receive a special gift on your big day!", "NotSupportedTngEWalletTitle": "TNG eWallet Not Supported", "NotSupportedTngEWalletDescription": "Please scan using the Beep App or your phone's QR scanner to open this page", "Others": "Others", "PaxDrawerPayFirstTitle": "Total Pax for This Order", "PaxDrawerPayLaterTitle": "Total Pax At The Table", "StartOrdering": "Start ordering", "PaxDrawerInputNumberLabel": "Total Pax", "StoreIsNotAvailableTitle": "Store is not available", "StoreIsNotAvailableDescription": "The store you selected is currently not available. Please choose a store in list.", "SSESyncUpUnavailable": "Real-time cart are unavailable. Exit app and try again.", "LoginHeaderTitle": "Login or Create Account", "LoginPhoneNumberInputTitle": "Enter your mobile number to proceed", "LoginOtpFormTitle": "Enter OTP", "LoginOtpInputDescription": "A code is sent to {{phone}}", "ResendSMSButtonText": "Resend Code Via SMS", "ResendWhatsappButtonText": "Resend Code Via Whatsapp", "OTPResendCountDownText": "Resend code in {{currentOtpTime}}", "ErrorOtpPhoneInvalidTitle": "Invalid phone number", "ErrorOtpPhoneInvalidDescription": "Service is unavailable for this phone number. Please contact support for assistance", "ErrorOtpMeetDayLimitTitle": "Reached Daily Limit", "ErrorOtpMeetDayLimitDescription": "You have exceeded your daily SMS limit", "ErrorOtpHighRiskTitle": "OTP Verification Failed", "ErrorOtpHighRiskDescription": "We have detected unusual traffic from your network, please try again later", "ErrorOtpRequestTooFastTitle": "OTP request too fast", "ErrorOtpRequestTooFastDescription": "You are being rate limited, please try again later", "ErrorOtpNoAvailableProviderTitle": "Oops!", "ErrorOtpNoAvailableProviderDescription": "Sending SMS to this number is not supported", "ErrorOtpCodeVerificationFailed": "Code verification failed"}