import i18next from 'i18next';
import { createSelector } from 'reselect';
import config from '../../../../config';
import {
  getQueryString,
  isWebview,
  isTNGMiniProgram,
  isGCashMiniProgram,
  toCapitalize,
} from '../../../../common/utils';
import {
  BECOME_MERCHANT_MEMBER_METHODS,
  MEMBER_LEVELS,
  PATH_NAME_MAPPING,
  PAGE_ROUTES,
} from '../../../../common/utils/constants';
import { isAlipayMiniProgram } from '../../../../common/utils/alipay-miniprogram-client/index';
import { getIsLogin } from '../../../../redux/modules/user/selectors';
import { getCustomerTierLevel } from '../customer/selectors';
import { getMembershipTierList } from '../../../../redux/modules/membership/selectors';

/** Utils */
export const getIsWebview = () => isWebview();

export const getIsTNGMiniProgram = () => isTNGMiniProgram();

export const getIsGCashMiniProgram = () => isGCashMiniProgram();

export const getIsAlipayMiniProgram = () => isAlipayMiniProgram();

export const getIsWeb = () => !isWebview() && !isAlipayMiniProgram();

export const getBusiness = () => getQueryString('business');

export const getQuerySource = () => getQueryString('source');

export const getIsNotLoginInWeb = createSelector(getIsLogin, getIsWeb, (isLogin, isWeb) => !isLogin && isWeb);

export const getSource = state => state.common.source;

export const getHomePageUrl = () => `${config.beepitComUrl}${PATH_NAME_MAPPING.SITE_HOME}`;

/** Router */
export const getRouter = state => state.router;

export const getLocation = state => state.router.location;

export const getLocationPathname = createSelector(getLocation, location => location.pathname);

export const getLocationSearch = createSelector(getLocation, location => location.search);

/**
 * Derived selectors
 */

// only getIsFrom** which source can use getQuerySource
export const getIsFromJoinMembershipUrlClick = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.JOIN_MEMBERSHIP_URL_CLICK
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromReceiptJoinMembershipUrlQRScan = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.RECEIPT_JOIN_MEMBERSHIP_URL_QR_SCAN
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromReceiptPointsQR = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.RECEIPT_POINTS_QR
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromReceiptPointsCashbackQR = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.RECEIPT_POINTS_CASHBACK_QR
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromReceiptMembershipDetailQRScan = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.RECEIPT_MEMBERSHIP_DETAIL_QR_SCAN
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromEarnedCashbackQRScan = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.EARNED_CASHBACK_QR_SCAN
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromSeamlessLoyaltyQrScan = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.SEAMLESS_LOYALTY_QR_SCAN
);

// only getIsFrom** which source can use getQuerySource
export const getIsFromThankYouCashbackClick = createSelector(
  getQuerySource,
  querySource => querySource === BECOME_MERCHANT_MEMBER_METHODS.SEAMLESS_LOYALTY_QR_SCAN
);

export const getIsMembershipBenefitTabsShown = createSelector(
  getMembershipTierList,
  membershipTierList => membershipTierList.length > 1
);

export const getIsMembershipBenefitsShown = createSelector(
  getMembershipTierList,
  membershipTierList => membershipTierList.length > 0
);

export const getIsJoinMembershipPathname = createSelector(
  getLocationPathname,
  locationPathName => locationPathName === PAGE_ROUTES.JOIN_MEMBERSHIP
);

export const getMerchantMembershipTiersBenefits = createSelector(
  getIsJoinMembershipPathname,
  getCustomerTierLevel,
  getMembershipTierList,
  (isJoinMembershipPathname, customerTierLevel, membershipTierList) => {
    if (membershipTierList.length === 0) {
      return [];
    }

    return membershipTierList.map(({ id, level, name, benefits = [] }) => {
      const isLocked = level > (isJoinMembershipPathname ? MEMBER_LEVELS.MEMBER : customerTierLevel);
      let prompt = null;

      if (isJoinMembershipPathname) {
        if (membershipTierList.length > 1) {
          prompt =
            level === MEMBER_LEVELS.MEMBER
              ? i18next.t('Rewards:UnlockLevelPrompt')
              : i18next.t('Rewards:UnlockHigherLevelPrompt', { levelName: toCapitalize(name) });
        } else {
          prompt = i18next.t('Rewards:UnlockOneTierLevelPrompt');
        }
      }

      return {
        key: `membership-tier-benefit-${id}`,
        isLocked,
        prompt,
        name,
        level,
        conditions: benefits,
      };
    });
  }
);

export const getMerchantMembershipTiersBenefitsLength = createSelector(
  getMerchantMembershipTiersBenefits,
  merchantMembershipTiersBenefits => merchantMembershipTiersBenefits.length
);
