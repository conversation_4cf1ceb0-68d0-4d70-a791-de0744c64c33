import React, { useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useMount } from 'react-use';
import { useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import BeepWarningImage from '../../../../../images/beep-warning.svg';
import { PAGE_ROUTES } from '../../../../../common/utils/constants';
import { closeWebView } from '../../../../../utils/native-methods';
import { getMerchantBusiness, getIsMerchantMembershipEnabled } from '../../../../../redux/modules/merchant/selectors';
import { getIsWebview, getIsWeb, getSource } from '../../../../redux/modules/common/selectors';
import {
  getIsMalaysianSeamlessLoyaltyWebShow,
  getIsNotMembershipSeamlessLoyaltyShow,
  getIsAllInitialRequestsCompleted,
  getAnyInitialRequestError,
} from './redux/selectors';
import { mounted } from './redux/thunks';
import { result } from '../../../../../common/utils/feedback/index';
import Frame from '../../../../../common/components/Frame';
import PageHeader from '../../../../../common/components/PageHeader';
import ResultContent from '../../../../../common/components/Result/ResultContent';
import PageToast from '../../../../../common/components/PageToast';
import Loader from '../../../../../common/components/Loader';
import SeamlessLoyaltyWeb from './SeamlessLoyaltyWeb';
import SeamlessLoyaltyNative from './SeamlessLoyaltyNative';

const SeamlessLoyaltyProxy = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const history = useHistory();
  const merchantBusiness = useSelector(getMerchantBusiness);
  const isWebview = useSelector(getIsWebview);
  const isWeb = useSelector(getIsWeb);
  const source = useSelector(getSource);
  const isMalaysianSeamlessLoyaltyWebShow = useSelector(getIsMalaysianSeamlessLoyaltyWebShow);
  const isNotMembershipSeamlessLoyaltyShow = useSelector(getIsNotMembershipSeamlessLoyaltyShow);
  const isMerchantMembershipEnabled = useSelector(getIsMerchantMembershipEnabled);
  const isAllInitialRequestsCompleted = useSelector(getIsAllInitialRequestsCompleted);
  const anyInitialRequestError = useSelector(getAnyInitialRequestError);
  const handleClickHeaderBackButton = useCallback(() => {
    if (isWebview) {
      closeWebView();
    }
  }, [isWebview]);

  useMount(() => {
    dispatch(mounted());
  });

  useEffect(() => {
    if (isAllInitialRequestsCompleted && !isMalaysianSeamlessLoyaltyWebShow) {
      const membershipDetailHistory = {
        pathname: PAGE_ROUTES.MEMBERSHIP_DETAIL,
        search: `?business=${merchantBusiness}&source=${source}`,
      };

      isMerchantMembershipEnabled && history.replace(membershipDetailHistory);
    }
  }, [
    isAllInitialRequestsCompleted,
    isMalaysianSeamlessLoyaltyWebShow,
    isMerchantMembershipEnabled,
    history,
    merchantBusiness,
    source,
  ]);

  useEffect(() => {
    if (anyInitialRequestError) {
      result(
        <ResultContent
          imageSrc={BeepWarningImage}
          content={t('SomethingWentWrongDescription')}
          title={t('SomethingWentWrongTitle')}
        />,
        {
          customizeContent: true,
          closeButtonContent: t('Retry'),
          onClose: () => {
            dispatch(mounted());
          },
        }
      );
    }
  }, [anyInitialRequestError, dispatch, t]);

  return (
    <Frame>
      {!isWeb && <PageHeader onBackArrowClick={handleClickHeaderBackButton} />}
      {isMalaysianSeamlessLoyaltyWebShow ? (
        <SeamlessLoyaltyWeb />
      ) : isNotMembershipSeamlessLoyaltyShow ? (
        <SeamlessLoyaltyNative />
      ) : (
        <PageToast icon={<Loader className="tw-m-8 sm:tw-m-8px" size={30} />}>{`${t('Processing')}...`}</PageToast>
      )}
    </Frame>
  );
};

SeamlessLoyaltyProxy.displayName = 'SeamlessLoyaltyProxy';

export default SeamlessLoyaltyProxy;
