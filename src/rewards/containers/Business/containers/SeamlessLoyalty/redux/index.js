import { createSlice } from '@reduxjs/toolkit';
import { API_REQUEST_STATUS } from '../../../../../../common/utils/constants';
import { updateSharingConsumerInfo } from './thunks';

const initialState = {
  updateSharingConsumerInfoRequest: {
    status: null,
    error: null,
  },
};

export const { reducer, actions } = createSlice({
  name: 'rewards/business/seamlessLoyalty',
  initialState,
  reducers: {},
  extraReducers: {
    [updateSharingConsumerInfo.pending.type]: state => {
      state.updateSharingConsumerInfoRequest.status = API_REQUEST_STATUS.PENDING;
      state.updateSharingConsumerInfoRequest.error = null;
    },
    [updateSharingConsumerInfo.fulfilled.type]: state => {
      state.updateSharingConsumerInfoRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.updateSharingConsumerInfoRequest.error = null;
    },
    [updateSharingConsumerInfo.rejected.type]: (state, { error }) => {
      state.updateSharingConsumerInfoRequest.status = API_REQUEST_STATUS.REJECTED;
      state.updateSharingConsumerInfoRequest.error = error;
    },
  },
});

export default reducer;
