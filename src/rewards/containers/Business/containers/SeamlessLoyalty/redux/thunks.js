import { createAsyncThunk } from '@reduxjs/toolkit';
import { push } from 'connected-react-router';
import { PAGE_ROUTES } from '../../../../../../common/utils/constants';
import { getSeamlessLoyaltyPlatform } from '../utils';
import Clevertap from '../../../../../../utils/clevertap';
import { getIsLogin, getEventTrackingUserCountry } from '../../../../../../redux/modules/user/selectors';
import {
  initUserInfo,
  loginUserByBeepApp,
  loginUserByAlipayMiniProgram,
} from '../../../../../../redux/modules/user/thunks';
import { fetchMerchantInfo } from '../../../../../../redux/modules/merchant/thunks';
import { getMerchantBusiness } from '../../../../../../redux/modules/merchant/selectors';
import {
  getIsWebview,
  getIsAlipayMiniProgram,
  getIsNotLoginInWeb,
  getLocationSearch,
} from '../../../../../redux/modules/common/selectors';
import { confirmToShareConsumerInfo } from '../../../redux/common/thunks';
import { getCustomerCashback } from '../../../../../redux/modules/customer/selectors';
import { fetchCustomerInfo } from '../../../../../redux/modules/customer/thunks';
import { getIsConfirmSharingNewCustomer } from '../../../redux/common/selectors';
import { patchSharingConsumerInfo } from './api-request';
import {
  getSeamlessLoyaltyRequestId,
  getIsSharingConsumerInfoEnabled,
  getIsMalaysianSeamlessLoyaltyWebShow,
} from './selectors';
import logger from '../../../../../../utils/monitoring/logger';

export const updateSharingConsumerInfo = createAsyncThunk(
  'rewards/business/seamlessLoyalty/updateSharingConsumerInfo',
  async (_, { getState }) => {
    const state = getState();
    const merchantBusiness = getMerchantBusiness(state);
    const requestId = getSeamlessLoyaltyRequestId(state);
    const source = getSeamlessLoyaltyPlatform();
    const result = await patchSharingConsumerInfo({ requestId, source, business: merchantBusiness });

    return result;
  }
);

export const mounted = createAsyncThunk(
  'rewards/business/seamlessLoyalty/mounted',
  async (_, { dispatch, getState }) => {
    const state = getState();
    const isWebview = getIsWebview(state);
    const isAlipayMiniProgram = getIsAlipayMiniProgram(state);
    const merchantBusiness = getMerchantBusiness(state);
    const requestId = getSeamlessLoyaltyRequestId(state);
    const search = getLocationSearch(state);

    await dispatch(fetchMerchantInfo(merchantBusiness));
    await dispatch(initUserInfo());

    try {
      if (isWebview) {
        await dispatch(loginUserByBeepApp());
      }

      if (isAlipayMiniProgram) {
        await dispatch(loginUserByAlipayMiniProgram());
      }

      const isLogin = getIsLogin(getState());
      const isNotLoginInWeb = getIsNotLoginInWeb(getState());
      const isMalaysianSeamlessLoyaltyWebShow = getIsMalaysianSeamlessLoyaltyWebShow(getState());

      if (isMalaysianSeamlessLoyaltyWebShow) {
        return;
      }

      if (isNotLoginInWeb) {
        dispatch(push(`${PAGE_ROUTES.REWARDS_LOGIN}${search}`, { shouldGoBack: true }));

        return;
      }

      const isSharingConsumerInfoEnabled = getIsSharingConsumerInfoEnabled(getState());

      // No need to get Customer data.
      // Completed the Shared Consumer Info request to Merchant,
      // Back-End completed creating customer and member events on the server side.
      if (isLogin && isSharingConsumerInfoEnabled) {
        await dispatch(updateSharingConsumerInfo());
        await dispatch(confirmToShareConsumerInfo(requestId));
        await dispatch(fetchCustomerInfo(merchantBusiness));

        const customerCashback = getCustomerCashback(getState());
        const eventTrackingUserCountry = getEventTrackingUserCountry(getState());
        const isConfirmSharingNewCustomer = getIsConfirmSharingNewCustomer(getState());

        Clevertap.pushEvent('POS Redemption Landing Page - View Page', {
          country: eventTrackingUserCountry,
          page:
            customerCashback > 0
              ? 'With Cashback'
              : `Without Cashback (${isConfirmSharingNewCustomer ? 'New' : 'Returning'} Customer)`,
        });
      }
    } catch (error) {
      logger.error('Rewards_SeamlessLoyalty_mountedFailed', { message: error?.message });

      throw error;
    }
  }
);
