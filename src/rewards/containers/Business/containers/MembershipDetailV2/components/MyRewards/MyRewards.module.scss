@use "../../../../../../../common/styles/variables" as *;

.MyRewardsSection {
  @apply tw-p-12 sm:tw-p-12px tw-space-y-12 sm:tw-space-y-12px;
}

.MyRewardsSectionTopContainer {
  @apply tw-flex tw-items-center tw-justify-between;
}

.MyRewardsSectionTitle {
  @apply tw-text-xl tw-leading-normal tw-font-bold;
}

.MyRewardsSectionViewAllButton:global(.type-text-info) {
  @apply tw-p-0 tw-underline;

  .MyRewardsSectionViewAllButtonContent {
    @apply tw-font-normal;
  }
}

.MyRewardsSectionEmptySection {
  @apply tw-flex tw-flex-col tw-items-center tw-p-16 sm:tw-p-16px tw-my-16 sm:tw-my-16px tw-space-y-8 sm:tw-space-y-8px;
}

.MyRewardsSectionEmptyImage {
  width: 21.333333%;
  max-width: 80px;
}

.MyRewardsSectionEmptyTitle {
  @apply tw-text-gray-700 tw-font-bold tw-leading-loose;
}

.MyRewardsList {
  @apply tw-space-y-8 sm:tw-space-y-8px;
}

.MyRewardsTicketMain {
  @apply tw-p-8 sm:tw-p-8px tw-space-y-8 sm:tw-space-y-8px;
}

.MyRewardsTicketMainTitle {
  @apply tw-leading-normal tw-font-bold;
  @include text-line-clamp(2, null);
}

.MyRewardsTicketMainDiscount {
  @apply tw-block tw-leading-normal tw-text-gray-700 tw-uppercase;
}

.MyRewardsTicketStub {
  @apply tw-flex tw-flex-col tw-items-start tw-justify-center tw-gap-12 sm:tw-gap-12px;

  width: 33.903134%;
}

.MyRewardsTicketStubRemainingExpiredDaysTag {
  @apply tw-font-bold;

  > span {
    @apply tw-flex tw-items-center;
  }
}

.MyRewardsTicketStubRemainingExpiredDaysTagLetter {
  font-size: inherit;
  font-weight: inherit;
}

.MyRewardsTicketStubRemainingExpiredDaysTagLetterHidden {
  display: none;
}

.MyRewardsTicketStubMinSpend {
  @apply tw-flex tw-items-center tw-flex-wrap	tw-gap-2px tw-text-sm tw-text-gray-700;
}

.MyRewardsTicketStubMinSpendPrice {
  font-size: inherit;
}

.MyRewardsTicketUnavailable {
  .MyRewardsTicketMain,
  .MyRewardsTicketStub > *:not(.MyRewardsTicketStubStatusTag) {
    @apply tw-opacity-50;
  }
}
