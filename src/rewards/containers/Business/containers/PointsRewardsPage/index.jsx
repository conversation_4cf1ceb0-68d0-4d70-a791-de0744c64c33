import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles } from 'react-use';
import { useTranslation } from 'react-i18next';
import { actions as rewardsCommonActions } from '../../redux/common';
import { getShouldSkeletonLoaderShow } from './redux/selectors';
import { backButtonClicked, mounted } from './redux/thunks';
import Frame from '../../../../../common/components/Frame';
import PageHeader from '../../../../../common/components/PageHeader';
import PointsRewards from './components/PointsRewards';
import SkeletonLoader from './components/SkeletonLoader';
import styles from './PointsRewardsPage.module.scss';

const PointsRewardsPage = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const shouldSkeletonLoaderShow = useSelector(getShouldSkeletonLoaderShow);
  const handleClickHeaderBackButton = useCallback(() => dispatch(backButtonClicked()), [dispatch]);

  useLifecycles(
    () => {
      dispatch(mounted());
    },
    () => {
      dispatch(rewardsCommonActions.loadPointsRewardListRequestReset());
    }
  );

  return (
    <Frame>
      <PageHeader
        className={styles.PointsRewardsPageHeader}
        title={t('GetRewards')}
        onBackArrowClick={handleClickHeaderBackButton}
      />
      {shouldSkeletonLoaderShow ? (
        <SkeletonLoader />
      ) : (
        <section className={styles.PointsRewardsSection}>
          <PointsRewards />
        </section>
      )}
    </Frame>
  );
};

PointsRewardsPage.displayName = 'PointsRewardsPage';

export default PointsRewardsPage;
