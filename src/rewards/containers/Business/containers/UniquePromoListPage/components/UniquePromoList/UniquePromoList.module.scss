.UniquePromoList {
  @apply tw-flex tw-flex-col tw-gap-y-12 sm:tw-gap-y-12px;
}

.UniquePromoButton:global(.type-text-ghost) {
  @apply tw-p-0;
}

.UniquePromoButtonContent {
  @apply tw-flex-1;
}

.UniquePromoTicketContainer {
  @apply tw-shadow-none;
}

.UniquePromoTicket {
  &::before,
  &::after {
    @apply tw-bg-gray-50;
  }
}

.UniquePromoInfoTop {
  @apply tw-flex-1 tw-flex tw-items-end tw-justify-between;

  &__Unavailable {
    @apply tw-opacity-40;
  }
}

.UniquePromoDescription {
  @apply tw-text-left;
}

.UniquePromoDiscount {
  @apply tw-leading-relaxed tw-font-bold tw-uppercase;
}

.UniquePromoDiscountName {
  @apply tw-text-sm tw-leading-loose;
}

.UniquePromoStatusTag,
.UniqueExpiringTag {
  @apply tw-flex-shrink-0 tw-font-bold;
}

.UniquePromoInfoBottom {
  @apply tw-flex-1 tw-flex tw-items-end tw-justify-between tw-border-l-0 tw-border-r-0;

  &__Unavailable {
    @apply tw-border-opacity-40;
  }
}

.UniquePromoDiscountLimitation {
  @apply tw-text-sm tw-leading-loose tw-text-gray-700;

  &__Unavailable {
    @apply tw-opacity-40;
  }
}

.UniquePromoViewDetail {
  @apply tw-text-xs tw-leading-normal tw-text-orange tw-capitalize;
}
