import { createSelector } from 'reselect';
import { getIsLoadMerchantRequestCompleted } from '../../../../../../redux/modules/merchant/selectors';
import { getIsLoadUniquePromoListCompleted } from '../../../redux/common/selectors';

export const getShouldSkeletonLoaderShow = createSelector(
  getIsLoadMerchantRequestCompleted,
  getIsLoadUniquePromoListCompleted,
  (isLoadMerchantRequestCompleted, isLoadUniquePromoListCompleted) =>
    !isLoadMerchantRequestCompleted || !isLoadUniquePromoListCompleted
);
