import React from 'react';
import { useSelector } from 'react-redux';
import { useMount } from 'react-use';
import CleverTap from '../../../../../utils/clevertap';
import { getEventTrackingUserCountry } from '../../../../../redux/modules/user/selectors';
import UniquePromoCongratulation from './components/UniquePromoCongratulation';
import UniquePromCongratulationFooter from './components/UniquePromCongratulationFooter';

const ClaimSuccess = () => {
  const eventTrackingUserCountry = useSelector(getEventTrackingUserCountry);

  useMount(() => {
    CleverTap.pushEvent('Claim Unique Promo Landing Page - Claim Unique Promo Successful', {
      country: eventTrackingUserCountry,
    });
  });

  return (
    <>
      <UniquePromoCongratulation />
      <UniquePromCongratulationFooter />
    </>
  );
};

ClaimSuccess.displayName = 'ClaimSuccess';

export default ClaimSuccess;
