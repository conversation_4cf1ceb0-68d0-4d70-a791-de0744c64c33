import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles } from 'react-use';
import { useTranslation } from 'react-i18next';
import PointsRewardClaimedIcon from '../../../../../images/rewards-points-claimed.svg';
import { REWARDS_APPLIED_SOURCE_I18KEYS } from '../../../../../common/utils/rewards/constants/index';
import CleverTap from '../../../../../utils/clevertap';
import {
  getPointsRewardFormatDiscountValue,
  getPointsRewardPromotionName,
  getPointsRewardPromotionType,
  getPointsRewardCostOfPoints,
  getPromoRewardDetailContentList,
  getIsClaimPointsRewardFulfilled,
  getIsClaimPointsRewardPending,
  getClaimPointsRewardErrorI18nKeys,
  getIsProfileModalShow,
  getIsPointsRewardGetRewardButtonDisabled,
  getShouldSkeletonLoaderShow,
} from './redux/selectors';
import { actions as pointsRewardActions } from './redux';
import {
  backButtonClicked,
  mounted,
  claimPointsReward,
  pointsClaimRewardButtonClicked,
  hideWebProfileForm,
  viewRewardButtonClicked,
} from './redux/thunks';
import Frame from '../../../../../common/components/Frame';
import PageHeader from '../../../../../common/components/PageHeader';
import PageFooter from '../../../../../common/components/PageFooter';
import Ticket from '../../../../../common/components/Ticket';
import Button from '../../../../../common/components/Button';
import PageToast from '../../../../../common/components/PageToast';
import { ObjectFitImage } from '../../../../../common/components/Image';
import Loader from '../../../../../common/components/Loader';
import { alert, confirm } from '../../../../../common/utils/feedback/index';
import CompleteProfile from '../../../CompleteProfile';
import SkeletonLoader from './components/SkeletonLoader';
import Article from '../../../../../common/components/Article';
import styles from './PointsRewardDetail.module.scss';

const PointsRewardDetail = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const shouldSkeletonLoaderShow = useSelector(getShouldSkeletonLoaderShow);
  const formatDiscountValue = useSelector(getPointsRewardFormatDiscountValue);
  const name = useSelector(getPointsRewardPromotionName);
  const type = useSelector(getPointsRewardPromotionType);
  const costOfPoints = useSelector(getPointsRewardCostOfPoints);
  const promoRewardDetailContentList = useSelector(getPromoRewardDetailContentList);
  const isPointsRewardGetRewardButtonDisabled = useSelector(getIsPointsRewardGetRewardButtonDisabled);
  const isClaimPointsRewardFulfilled = useSelector(getIsClaimPointsRewardFulfilled);
  const isClaimPointsRewardPending = useSelector(getIsClaimPointsRewardPending);
  const claimPointsRewardErrorI18nKeys = useSelector(getClaimPointsRewardErrorI18nKeys);
  const isProfileModalShow = useSelector(getIsProfileModalShow);
  const handleClickHeaderBackButton = useCallback(() => dispatch(backButtonClicked()), [dispatch]);
  const handleClickGetRewardButton = useCallback(() => {
    CleverTap.pushEvent('Points Reward Details Page - Click Get Reward Button');

    confirm('', {
      className: styles.PointsRewardConfirm,
      title: t('RewardsCostOfPointsConfirmMessage', { costOfPoints }),
      cancelButtonContent: t('Cancel'),
      confirmButtonContent: t('Confirm'),
      onSelection: async status => {
        dispatch(pointsClaimRewardButtonClicked({ status, type, costOfPoints }));
      },
    });
  }, [dispatch, t, type, costOfPoints]);
  const handleClickViewRewardButton = useCallback(
    status => {
      dispatch(viewRewardButtonClicked(status));
    },
    [dispatch]
  );
  const handleClickSkipProfileButton = useCallback(() => {
    dispatch(hideWebProfileForm());
    dispatch(claimPointsReward());
  }, [dispatch]);
  const handleClickSaveProfileButton = useCallback(() => dispatch(claimPointsReward()), [dispatch]);
  const handleCloseCompleteProfile = useCallback(() => dispatch(hideWebProfileForm()), [dispatch]);

  useLifecycles(
    () => {
      dispatch(mounted());
    },
    () => {
      dispatch(pointsRewardActions.claimPointsRewardRequestReset());
      dispatch(pointsRewardActions.loadPointsRewardDetailRequestReset());
    }
  );

  useEffect(() => {
    if (isClaimPointsRewardFulfilled) {
      confirm(
        <div className={styles.PointsRewardDetailClaimedAlertContent}>
          <div className={styles.PointsRewardDetailClaimedAlertIcon}>
            <ObjectFitImage
              noCompression
              src={PointsRewardClaimedIcon}
              alt="Points Reward Claimed Successful Icon in StoreHub"
            />
          </div>
          <h4 className={styles.PointsRewardDetailClaimedAlertTitle}>{t('PointsRewardClaimedTitle')}</h4>
          <p className={styles.PointsRewardDetailClaimedAlertDescription}>{t('PointsRewardClaimedDescription')}</p>
        </div>,
        {
          customizeContent: true,
          cancelButtonContent: t('Back'),
          confirmButtonContent: t('ViewReward'),
          onSelection: handleClickViewRewardButton,
        }
      );
    }
  }, [t, isClaimPointsRewardFulfilled, handleClickViewRewardButton, dispatch]);

  useEffect(() => {
    if (claimPointsRewardErrorI18nKeys) {
      const { titleI18nKey, descriptionI18nKey } = claimPointsRewardErrorI18nKeys || {};

      alert(t(descriptionI18nKey), {
        title: t(titleI18nKey),
        onClose: () => {
          dispatch(pointsRewardActions.claimPointsRewardRequestReset());
        },
      });
    }
  }, [claimPointsRewardErrorI18nKeys, t, dispatch]);

  return (
    <>
      <Frame>
        <PageHeader title={t('PointsRewardDetails')} onBackArrowClick={handleClickHeaderBackButton} />

        {shouldSkeletonLoaderShow ? (
          <SkeletonLoader />
        ) : (
          <>
            <Ticket
              orientation="vertical"
              size="large"
              showBorder={false}
              className={styles.PointsRewardDetailTicket}
              mainClassName={styles.PointsRewardDetailTicketMain}
              stubClassName={styles.PointsRewardDetailTicketStub}
              main={
                <div className={styles.PointsRewardDetailTicketMainContent}>
                  <data className={styles.PointsRewardDetailTicketDiscountValue} value={formatDiscountValue}>
                    {t('DiscountValueText', { discount: formatDiscountValue })}
                  </data>
                  <h2 className={styles.PointsRewardDetailTicketName}>{name}</h2>
                </div>
              }
              stub={
                <data className={styles.PointsRewardDetailTicketCostPoints} value={costOfPoints}>
                  {t('RewardsCostOfPointsText', { costOfPoints })}
                </data>
              }
            />

            {promoRewardDetailContentList.map((contentItem, index) => {
              const { title, titleDescription, articleContentList } = contentItem;

              return (
                <section
                  className={styles.PointsRewardDetailContentArticleSection}
                  // eslint-disable-next-line react/no-array-index-key
                  key={`pointsRewardDetail-article-${index}`}
                >
                  <Article
                    title={title}
                    titleDescription={titleDescription}
                    articleContentList={
                      articleContentList &&
                      articleContentList.map(item => {
                        const { subtitle, description, pointsRewardRedeemOnlineList } = item;
                        const articleContent = { subtitle, description, content: null };

                        if (pointsRewardRedeemOnlineList) {
                          articleContent.content = (
                            <ul className={styles.PointsRewardDetailHowToUseRedeemOnlineList}>
                              {pointsRewardRedeemOnlineList.map(redeemOnlineChannel => (
                                <li
                                  key={`pointsRewardDetail-redeemOnlineChannel-${redeemOnlineChannel}`}
                                  className={styles.PointsRewardDetailHowToUseRedeemOnlineItem}
                                >
                                  {t(REWARDS_APPLIED_SOURCE_I18KEYS[redeemOnlineChannel])}
                                </li>
                              ))}
                            </ul>
                          );
                        }

                        return articleContent;
                      })
                    }
                  />
                </section>
              );
            })}

            <PageFooter zIndex={50}>
              <div className={styles.PointsRewardDetailFooterContent}>
                <Button
                  className={styles.PointsRewardDetailFooterButton}
                  block
                  disabled={isPointsRewardGetRewardButtonDisabled}
                  data-test-id="rewards.business.points-reward-detail.get-reward-button"
                  onClick={handleClickGetRewardButton}
                >
                  {t('GetReward')}
                </Button>
              </div>
            </PageFooter>
          </>
        )}
      </Frame>
      {isClaimPointsRewardPending && (
        <PageToast icon={<Loader className="tw-m-8 sm:tw-m-8px" size={30} />}>{`${t('Processing')}...`}</PageToast>
      )}
      <CompleteProfile
        show={isProfileModalShow}
        onSave={handleClickSaveProfileButton}
        onSkip={handleClickSkipProfileButton}
        onClose={handleCloseCompleteProfile}
      />
    </>
  );
};

PointsRewardDetail.displayName = 'PointsRewardDetail';

export default PointsRewardDetail;
