.EarnedStoreCreditsPromptDrawer {
  @apply tw-p-0;
}

.EarnedStoreCreditsPromptDrawerHeaderCloseButton {
  @apply tw-flex-shrink-0 tw-text-2xl tw-text-gray;
}

.EarnedStoreCreditsPromptDrawerTitle {
  @apply tw-font-bold tw-text-lg tw-leading-relaxed;
}

.EarnedStoreCreditsPromptDrawerDescription {
  @apply tw-px-16 sm:tw-px-16px tw-my-16 sm:tw-my-16px tw-leading-loose;
}

.EarnedStoreCreditsPromptList {
  @apply tw-flex tw-flex-col tw-gap-y-16 sm:tw-gap-y-16px tw-px-16 sm:tw-px-16px tw-my-16 sm:tw-my-16px;
}

.EarnedStoreCreditsPromptItem {
  @apply tw-border tw-border-solid tw-border-gray-400 tw-rounded;
}

.EarnedStoreCreditsPromptItemTitle {
  @apply tw-px-16 sm:tw-px-16px tw-py-8 sm:tw-py-8px tw-border-0 tw-border-b tw-border-solid tw-border-gray-400 tw-text-lg tw-leading-relaxed tw-font-bold;
}

.EarnedStoreCreditsPromptItemDescription {
  @apply tw-px-16 sm:tw-px-16px tw-py-12 sm:tw-py-12px tw-leading-loose;
}
