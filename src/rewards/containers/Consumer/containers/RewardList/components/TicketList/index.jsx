import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { UNIQUE_PROMO_STATUS_I18KEYS } from '../../../../../../../common/utils/rewards/constants/index';
import { getClassName } from '../../../../../../../common/utils/ui/index';
import { getRewardList, getIsSearchBoxEmpty } from '../../redux/selectors';
import { clickRewardItem } from '../../redux/thunks';
import Ticket from '../../../../../../../common/components/Ticket';
import Tag from '../../../../../../../common/components/Tag';
import Button from '../../../../../../../common/components/Button';
import styles from './TicketList.module.scss';

const TicketList = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const rewardList = useSelector(getRewardList);
  const isSearchBoxEmpty = useSelector(getIsSearchBoxEmpty);
  const handleClickRewardItemButton = useCallback(
    selectedReward => {
      dispatch(clickRewardItem(selectedReward));
    },
    [dispatch]
  );

  return (
    <section className={styles.RewardTicketListContainer}>
      {isSearchBoxEmpty && <h3 className={styles.RewardTicketListTitle}>{t('YourVouchers')}</h3>}
      <ul className={styles.RewardTicketList}>
        {rewardList.map(reward => {
          const {
            key,
            applicableMerchants,
            value,
            name,
            isUnavailable,
            status,
            expiringDaysI18n,
            expiringDateI18n,
            minSpendI18n,
          } = reward;

          return (
            <li key={key}>
              <Button
                block
                type="text"
                theme="ghost"
                data-test-id="rewards.consumer.reward-list.reward-item"
                className={styles.RewardItemButton}
                contentClassName={styles.RewardItemButtonContent}
                onClick={() => {
                  handleClickRewardItemButton(reward);
                }}
              >
                <Ticket
                  orientation="vertical"
                  showShadow={false}
                  className={getClassName([
                    styles.RewardTicket,
                    isUnavailable ? styles.RewardTicket__Unavailable : null,
                  ])}
                  main={
                    <div className={styles.RewardTicketInfoTop}>
                      <div className={styles.RewardTicketDescription}>
                        <span className={styles.RewardTicketApplicableMerchants}>{applicableMerchants}</span>
                        <data className={styles.RewardTicketDiscount} value={value}>
                          {t('DiscountValueText', { discount: value })}
                        </data>
                        <h5 className={styles.RewardTicketDiscountName}>{name}</h5>
                      </div>
                      {isUnavailable ? (
                        <Tag className={styles.RewardTicketStatusTag}>{t(UNIQUE_PROMO_STATUS_I18KEYS[status])}</Tag>
                      ) : expiringDaysI18n ? (
                        <Tag color="red" className={styles.RewardTicketExpiringTag}>
                          {t(expiringDaysI18n.i18nKey, expiringDaysI18n.params)}
                        </Tag>
                      ) : expiringDateI18n ? (
                        <Tag className={styles.RewardTicketExpiringTag}>
                          {t(expiringDateI18n.i18nKey, expiringDateI18n.params)}
                        </Tag>
                      ) : null}
                    </div>
                  }
                  stub={
                    <div
                      className={getClassName([
                        styles.RewardTicketInfoBottom,
                        !minSpendI18n ? styles.RewardTicketInfoBottom__NoMinSpend : null,
                      ])}
                    >
                      {minSpendI18n && (
                        <span className={styles.RewardTicketDiscountLimitation}>
                          {t(minSpendI18n.i18nKey, minSpendI18n.params)}
                        </span>
                      )}

                      <span className={styles.RewardTicketViewDetail}>{t('ViewDetails')}</span>
                    </div>
                  }
                />
              </Button>
            </li>
          );
        })}
      </ul>
    </section>
  );
};

TicketList.displayName = 'TicketList';

export default TicketList;
