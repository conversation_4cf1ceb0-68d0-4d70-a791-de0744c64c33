import { createSlice } from '@reduxjs/toolkit';
import { showLoginDrawer, hideLoginDrawer, showCompleteDrawer, hideCompleteDrawer } from './thunks';

const initialState = {
  loginDrawer: {
    show: false,
  },
  completeDrawer: {
    show: false,
  },
};

export const { actions, reducer } = createSlice({
  name: 'user/membershipsPage',
  initialState,
  reducers: {
    stateReset: () => initialState,
  },
  extraReducers: {
    [showLoginDrawer.fulfilled.type]: state => {
      state.loginDrawer.show = true;
    },
    [hideLoginDrawer.fulfilled.type]: state => {
      state.loginDrawer.show = false;
    },
    [showCompleteDrawer.fulfilled.type]: state => {
      state.completeDrawer.show = true;
    },
    [hideCompleteDrawer.fulfilled.type]: state => {
      state.completeDrawer.show = false;
    },
  },
});

export default reducer;
