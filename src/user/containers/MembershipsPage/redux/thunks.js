import { createAsyncThunk } from '@reduxjs/toolkit';
import { getIsLogin } from '../../../../redux/modules/user/selectors';
import { initUserInfo, loginUserByBeepApp, loginUserByAlipayMiniProgram } from '../../../../redux/modules/user/thunks';
import { getIsWebview, getIsAlipayMiniProgram, getIsNotLoginInWeb } from '../../../redux/common/selectors';
import { updateMembershipList } from '../../../redux/memberships/thunks';

export const showLoginDrawer = createAsyncThunk('user/membershipsPage/showLoginDrawer', async () => {});

export const hideLoginDrawer = createAsyncThunk('user/membershipsPage/hideLoginDrawer', async () => {});

export const showCompleteDrawer = createAsyncThunk('user/membershipsPage/showCompleteDrawer', async () => {});

export const hideCompleteDrawer = createAsyncThunk('user/membershipsPage/hideCompleteDrawer', async () => {});

export const mounted = createAsyncThunk('user/membershipsPage/mounted', async (_, { dispatch, getState }) => {
  const state = getState();
  const isWebview = getIsWebview(state);
  const isAlipayMiniProgram = getIsAlipayMiniProgram(state);

  await dispatch(initUserInfo());

  if (isWebview) {
    await dispatch(loginUserByBeepApp());
  }

  if (isAlipayMiniProgram) {
    await dispatch(loginUserByAlipayMiniProgram());
  }

  const isNotLoginInWeb = getIsNotLoginInWeb(getState());

  if (isNotLoginInWeb) {
    dispatch(showLoginDrawer());
    return;
  }

  const isLogin = getIsLogin(getState());

  if (isLogin) {
    await dispatch(updateMembershipList());
  }
});
