import { createSelector } from '@reduxjs/toolkit';
import { getIsMobile, getIsWeb } from '../../../redux/common/selectors';

export const getLoginDrawerShow = state => state.membershipsPage.loginDrawer.show;

export const getCompleteDrawerShow = state => state.membershipsPage.completeDrawer.show;

export const getIsDownloadBannerShown = createSelector(getIsMobile, getIsWeb, (isMobile, isWeb) => isMobile && isWeb);
