import { createSlice } from '@reduxjs/toolkit';
import { TAB_TYPES } from '../../../../common/utils/constants';
import { changeActiveTab } from './thunks';

const initialState = {
  activeTab: TAB_TYPES.MEMBERSHIP,
};

export const { actions, reducer } = createSlice({
  name: 'user/nativeRewards',
  initialState,
  reducers: {
    stateReset: () => initialState,
  },
  extraReducers: {
    [changeActiveTab.fulfilled.type]: (state, { payload }) => {
      const tabKey = payload;

      if (Object.values(TAB_TYPES).includes(tabKey)) {
        state.activeTab = tabKey;
      }
    },
  },
});

export default reducer;
