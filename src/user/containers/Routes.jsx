import React, { lazy, Suspense } from 'react';
import { Switch, Route, withRouter } from 'react-router-dom';
import { ConnectedRouter } from 'connected-react-router';
import { PAGE_ROUTES } from '../utils/history/constants';
import { attemptLoad } from '../../common/utils';
import history from '../../common/utils/system/history';
import NotFound from '../../containers/NotFound';

const MembershipsPage = lazy(() => attemptLoad(() => import(/* webpackChunkName: "USR_MF_LST" */ './MembershipsPage')));

const NativeRewards = lazy(() =>
  attemptLoad(() => import(/* webpackChunkName: "USR_NATIVE_RWDS" */ './NativeRewards'))
);

const Routes = () => (
  <ConnectedRouter history={history}>
    <Suspense fallback={<div className="loader theme full-page" />}>
      <Switch>
        <Route exact path={PAGE_ROUTES.MEMBERSHIPS} component={MembershipsPage} />
        <Route exact path={PAGE_ROUTES.NATIVE_REWARDS} component={NativeRewards} />
        <Route path="*" component={NotFound} />
      </Switch>
    </Suspense>
  </ConnectedRouter>
);

Routes.displayName = 'RewardsRoutes';

export default withRouter(Routes);
