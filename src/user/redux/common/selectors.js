import { createSelector } from '@reduxjs/toolkit';
import config from '../../../config';
import { PATH_NAME_MAPPING } from '../../../common/utils/constants';
import { isWebview as getUtilIsWebview, getQueryString, isMobile as getUtilIsMobile } from '../../../common/utils';
import { isAlipayMiniProgram as getUtilIsAlipayMiniProgram } from '../../../common/utils/alipay-miniprogram-client/index';
import {
  getIsLogin,
  getIsUserProfileEmpty,
  getIsLoadUserProfileCompleted,
} from '../../../redux/modules/user/selectors';

/** Utils */
export const getIsWebview = () => getUtilIsWebview();

export const getIsAlipayMiniProgram = () => getUtilIsAlipayMiniProgram();

export const getIsMobile = () => getUtilIsMobile();

export const getIsWeb = () => !getUtilIsWebview() && !getUtilIsAlipayMiniProgram();

export const getLocationSearch = () => getQueryString();

export const getHomePageUrl = () => `${config.beepitComUrl}${PATH_NAME_MAPPING.SITE_HOME}`;

export const getIsNotLoginInWeb = createSelector(getIsWeb, getIsLogin, (isWeb, isLogin) => isWeb && !isLogin);

export const getShouldShowUserNotificationBadge = createSelector(
  getIsLogin,
  getIsUserProfileEmpty,
  getIsLoadUserProfileCompleted,
  (isLogin, isUserProfileEmpty, isLoadUserProfileCompleted) =>
    isLogin && isUserProfileEmpty && isLoadUserProfileCompleted
);
