import { combineReducers } from 'redux';
import { connectRouter } from 'connected-react-router';
import user from '../../redux/modules/user';
import rewards from '../../redux/modules/rewards';
import merchant from '../../redux/modules/merchant';
import common from './common';
import memberships from './memberships';
import promoVoucherList from './promoVoucherList';
import completeProfile from '../../common/containers/CompleteProfile/redux';
import login from '../../common/containers/Login/redux';
import membershipsPage from '../containers/MembershipsPage/redux';
import nativeRewards from '../containers/NativeRewards/redux';

const rootReducer = history =>
  combineReducers({
    router: connectRouter(history),
    user,
    rewards,
    merchant,
    common,
    memberships,
    promoVoucherList,
    completeProfile,
    login,
    membershipsPage,
    nativeRewards,
  });

export default rootReducer;
