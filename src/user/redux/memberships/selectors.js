import _get from 'lodash/get';
import { createSelector } from '@reduxjs/toolkit';
import config from '../../../config';
import { API_REQUEST_STATUS, MEMBER_LEVELS_PALETTES, PATH_NAME_MAPPING } from '../../../common/utils/constants';
import { getPrice } from '../../../common/utils';
import { getIsCheckLoginRequestCompleted, getIsLogin } from '../../../redux/modules/user/selectors';

export const getMembershipListData = state => state.memberships.memberships.data;

export const getMembershipListPagination = state => state.memberships.memberships.pagination;

export const getMembershipListInitialLoaded = state => state.memberships.memberships.initialLoaded;

export const getMembershipListPaginationOffset = createSelector(getMembershipListPagination, pagination =>
  _get(pagination, 'offset', 0)
);

export const getMembershipListPaginationLimit = createSelector(getMembershipListPagination, pagination =>
  _get(pagination, 'limit', 10)
);

export const getMembershipListPaginationEnd = createSelector(getMembershipListPagination, pagination =>
  _get(pagination, 'end', false)
);

export const getLoadMembershipListRequestData = state => state.memberships.loadMembershipListRequest.data;

export const getLoadMembershipListRequestStatus = state => state.memberships.loadMembershipListRequest.status;

export const getLoadMembershipListRequestError = state => state.memberships.loadMembershipListRequest.error;

export const getMembershipList = createSelector(getMembershipListData, membershipListData => {
  if (!membershipListData) {
    return [];
  }

  const availableMembershipItems = membershipListData.filter(item => {
    const { merchantInfo } = item;
    const { membershipEnabled, enableCashback, pointsEnabled } = merchantInfo || {};

    return membershipEnabled || enableCashback || pointsEnabled;
  });

  return availableMembershipItems.map(membershipItem => {
    const { merchantInfo, customerInfo } = membershipItem;
    const {
      name,
      displayName,
      logo,
      country,
      locale,
      currency,
      membershipEnabled,
      enableCashback,
      pointsEnabled,
    } = merchantInfo;
    const { customerId, customerTier, pointsSummary, cashbackSummary, uniquePromosSummary } = customerInfo || {};
    const { level, name: customerTierName } = customerTier || {};
    const cashbackPrice = getPrice(cashbackSummary.availableBalance, {
      locale,
      currency,
      country,
    });
    const membershipDetailUrl = `${config.beepitComUrl}${PATH_NAME_MAPPING.REWARDS_BASE}${PATH_NAME_MAPPING.REWARDS_BUSINESS}${PATH_NAME_MAPPING.REWARDS_MEMBERSHIP}${PATH_NAME_MAPPING.MEMBERSHIP_DETAIL}?business=${name}`;
    const cashbackDetailUrl = `${config.beepitComUrl}${PATH_NAME_MAPPING.REWARDS_BASE}${PATH_NAME_MAPPING.REWARDS_BUSINESS}${PATH_NAME_MAPPING.CASHBACK}${PATH_NAME_MAPPING.CASHBACK_DETAIL}?business=${name}`;
    const newMembershipItem = {
      key: `${name}-${customerId}`,
      merchantName: name,
      redirectionUrl: membershipEnabled ? membershipDetailUrl : cashbackDetailUrl,
      displayName,
      logo,
      customerId,
    };

    if (customerTier) {
      newMembershipItem.customerTierName = customerTierName;
      newMembershipItem.customerTierLevel = level;
      newMembershipItem.customerTierIconColors = MEMBER_LEVELS_PALETTES[level].icon;
      newMembershipItem.customerTierBackgroundColors = MEMBER_LEVELS_PALETTES[level].background;
      newMembershipItem.customerTierFontColors = MEMBER_LEVELS_PALETTES[level].font;
    }

    if (pointsSummary) {
      newMembershipItem.points = {
        balance: pointsSummary.availablePointsBalance || 0,
        enabled: pointsEnabled,
        isValidLessThan7Days: pointsSummary.isValidLessThan7Days,
      };
    }

    if (cashbackSummary) {
      newMembershipItem.cashback = {
        balance: cashbackPrice,
        enabled: enableCashback,
        isValidLessThan7Days: cashbackSummary.isValidLessThan7Days,
      };
    }

    if (uniquePromosSummary) {
      newMembershipItem.rewards = {
        balance: uniquePromosSummary.availableUniquePromoCount || 0,
        isValidLessThan7Days: uniquePromosSummary.isValidLessThan7Days,
      };
    }

    return newMembershipItem;
  });
});

/*
 * Selectors derived from state
 */
export const getIsLoadMembershipListRequestRejected = createSelector(
  getLoadMembershipListRequestStatus,
  status => status === API_REQUEST_STATUS.REJECTED
);

export const getIsEmptyMembershipListResultShown = createSelector(
  getIsLogin,
  getIsCheckLoginRequestCompleted,
  getMembershipListData,
  getMembershipListPaginationEnd,
  (isLogin, isCheckLoginRequestCompleted, membershipListData, isPaginationEnd) => {
    if (isCheckLoginRequestCompleted && !isLogin) {
      return true;
    }

    if (!membershipListData) {
      return true;
    }

    return membershipListData.length === 0 && isPaginationEnd;
  }
);

export const getIsSkeletonLoaderShown = createSelector(
  getMembershipListInitialLoaded,
  getIsLogin,
  (isMembershipListInitialLoaded, isLogin) => !isMembershipListInitialLoaded && isLogin
);
