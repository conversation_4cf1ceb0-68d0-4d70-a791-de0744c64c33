import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useUnmount } from 'react-use';
import { WarningCircle } from 'phosphor-react';
import RewardsIconPointsImage from '../../../images/rewards-icon-points.svg';
import RewardsIconCashbackImage from '../../../images/rewards-icon-cashback.svg';
import RewardsIconRewardsImage from '../../../images/rewards-icon-rewards.svg';
import StoreLogoPlaceholderImage from '../../../images/store-logo-placeholder.svg';
import { openWebViewURL } from '../../../common/utils/system/native-methods/index';
import { ObjectFitImage } from '../../../common/components/Image';
import MemberIcon from '../../../common/components/MemberIcon';
import { actions as membershipsActions } from '../../redux/memberships';
import {
  getMembershipList,
  getIsEmptyMembershipListResultShown,
  getIsSkeletonLoaderShown,
} from '../../redux/memberships/selectors';
import EmptyMembershipListResult from './components/EmptyMembershipListResult';
import SkeletonLoader from './components/SkeletonLoader';
import styles from './MembershipList.module.scss';

const MembershipList = () => {
  const { t } = useTranslation('User');
  const membershipList = useSelector(getMembershipList);
  const isEmptyMembershipListResultShown = useSelector(getIsEmptyMembershipListResultShown);
  const isSkeletonLoaderShown = useSelector(getIsSkeletonLoaderShown);
  const handleClickMembershipItem = redirectionUrl => {
    openWebViewURL(redirectionUrl);
  };

  useUnmount(() => {
    membershipsActions.membershipsReset();
    membershipsActions.loadMembershipListRequestReset();
  });

  return (
    <>
      {isSkeletonLoaderShown ? (
        <SkeletonLoader />
      ) : isEmptyMembershipListResultShown ? (
        <EmptyMembershipListResult />
      ) : (
        <ul className={styles.MembershipList}>
          {membershipList.map(membershipItem => {
            const {
              key,
              merchantName,
              redirectionUrl,
              logo,
              displayName,
              customerTierLevel,
              customerTierName,
              customerTierIconColors,
              customerTierBackgroundColors,
              customerTierFontColors,
              points,
              cashback,
              rewards,
            } = membershipItem;
            const { startColor: backgroundStartColor, midColor: backgroundMidColor, endColor: backgroundEndColor } =
              customerTierBackgroundColors || {};
            const { crown: iconCrownColors, background: iconBackgroundColors, strokeColor: iconStrockColor } =
              customerTierIconColors || {};

            return (
              <li key={key} className={styles.MembershipItem}>
                <button
                  className={styles.MembershipItemLink}
                  data-test-id="user.membership-list.membership-item"
                  onClick={() => handleClickMembershipItem(redirectionUrl)}
                >
                  {customerTierLevel && (
                    <div
                      className={styles.MembershipItemLinkBackground}
                      style={{
                        background: `linear-gradient(105deg, ${backgroundStartColor} 0%, ${backgroundMidColor} 50%, ${backgroundEndColor} 100%)`,
                      }}
                    />
                  )}

                  <div className={styles.MembershipItemLeftContent}>
                    <div className={styles.MembershipItemLogoContainer}>
                      <ObjectFitImage
                        noCompression={!logo}
                        src={logo || StoreLogoPlaceholderImage}
                        alt={displayName}
                        className={styles.MembershipItemLogo}
                      />
                    </div>
                    {customerTierLevel && (
                      <div
                        className={styles.MembershipItemTierTag}
                        style={{
                          background: `linear-gradient(105deg, ${backgroundStartColor} 0%, ${backgroundMidColor} 50%, ${backgroundEndColor} 100%)`,
                        }}
                      >
                        <MemberIcon
                          className={styles.MembershipItemTierIcon}
                          id={`customer-tier-icon-${key}`}
                          crownStartColor={iconCrownColors.startColor}
                          crownEndColor={iconCrownColors.endColor}
                          backgroundStartColor={iconBackgroundColors.startColor}
                          backgroundEndColor={iconBackgroundColors.endColor}
                          strokeColor={iconStrockColor}
                        />
                        <data
                          className={styles.MembershipItemTierName}
                          style={{
                            color: customerTierFontColors,
                          }}
                          value={customerTierName}
                        >
                          {customerTierName}
                        </data>
                      </div>
                    )}
                  </div>
                  <div className={styles.MembershipItemRightContent}>
                    <h4 className={styles.MembershipItemDisplayName} style={{ color: customerTierFontColors }}>
                      {displayName}
                    </h4>
                    <ul className={styles.MembershipItemRewardsList}>
                      {points.enabled && (
                        <li className={styles.MembershipItemItem}>
                          <div className={styles.MembershipItemIconContainer}>
                            <ObjectFitImage
                              noCompression
                              className={styles.MembershipItemIcon}
                              src={RewardsIconPointsImage}
                              alt={`Beep ${merchantName} Membership points icon`}
                            />
                          </div>
                          <data className={styles.MembershipItemText} value="888">
                            {t('MembershipCardPointsItemText', { points: points.balance })}
                          </data>
                          {points.isValidLessThan7Days && (
                            <WarningCircle className={styles.MembershipItemExpiring} size={12} weight="fill" />
                          )}
                        </li>
                      )}
                      {cashback.enabled && (
                        <li className={styles.MembershipItemItem}>
                          <div className={styles.MembershipItemIconContainer}>
                            <ObjectFitImage
                              noCompression
                              className={styles.MembershipItemIcon}
                              src={RewardsIconCashbackImage}
                              alt={`Beep ${merchantName} Membership cashback icon`}
                            />
                          </div>
                          <data className={styles.MembershipItemText} value={cashback.balance}>
                            {cashback.balance}
                          </data>
                          {cashback.isValidLessThan7Days && (
                            <WarningCircle className={styles.MembershipItemExpiring} size={12} weight="fill" />
                          )}
                        </li>
                      )}
                      <li className={styles.MembershipItemItem}>
                        <div className={styles.MembershipItemIconContainer}>
                          <ObjectFitImage
                            noCompression
                            className={styles.MembershipItemIcon}
                            src={RewardsIconRewardsImage}
                            alt={`Beep ${merchantName} Membership rewards icon`}
                          />
                        </div>
                        <data className={styles.MembershipItemText} value={rewards.balance}>
                          {t('MembershipCardRewardsItemText', { rewards: rewards.balance })}
                        </data>
                        {rewards.isValidLessThan7Days && (
                          <WarningCircle className={styles.MembershipItemExpiring} size={12} weight="fill" />
                        )}
                      </li>
                    </ul>
                  </div>
                </button>
              </li>
            );
          })}
        </ul>
      )}
    </>
  );
};

MembershipList.displayName = 'MembershipList';

export default MembershipList;
