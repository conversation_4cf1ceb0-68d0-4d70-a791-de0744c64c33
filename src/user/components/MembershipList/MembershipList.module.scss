@use '../../../common/styles/variables' as *;

.MembershipList {
  @apply tw-space-y-8 sm:tw-space-y-8px tw-list-none;
}

.MembershipItem {
  @apply tw-px-16 sm:tw-px-16px;
}

.MembershipItemLink {
  @apply tw-relative tw-flex tw-gap-12 sm:tw-gap-12px tw-px-12 sm:tw-px-12px tw-pt-16 sm:tw-pt-16px tw-pb-20 sm:tw-pb-20px tw-border-0 tw-w-full tw-text-gray-800 tw-bg-gray-50 tw-rounded tw-shadow tw-no-underline tw-overflow-hidden;
}

.MembershipItemLinkBackground {
  @apply tw-absolute tw-left-0 tw-top-0 tw-w-full;

  content: "";
  height: 41%;
  max-height: 68px;
}

.MembershipItemLeftContent {
  @apply tw-relative tw-flex-shrink-0 tw-flex tw-flex-col tw-items-center tw-gap-12 sm:tw-gap-12px;

  width: 30%;
}

.MembershipItemLogoContainer {
  @apply tw-rounded tw-overflow-hidden;

  width: 100%;
  max-width: 116px;
  aspect-ratio: 1 / 1;
}

.MembershipItemTierTag {
  @apply tw-inline-flex tw-items-center tw-gap-2 sm:tw-gap-2px tw-p-4 sm:tw-p-4px tw-rounded-xl;
}

.MembershipItemTierIcon {
  @apply tw-inline-block;
}

.MembershipItemTierIcon,
.MembershipItemTierIcon svg {
  width: 16px;
  height: 16px;
}

.MembershipItemRightContent {
  @apply tw-relative tw-flex-1 tw-flex tw-flex-col tw-justify-between;
}

.MembershipItemTierName {
  @apply tw-text-center tw-text-xs tw-leading-normal tw-font-bold tw-capitalize;

  width: 60px;
}

.MembershipItemDisplayName {
  @apply tw-text-left tw-py-4 sm:tw-py-4px tw-leading-normal tw-font-bold;
  @include text-line-clamp(2, 2.8em);

  height: 40px;
}

.MembershipItemRewardsList {
  @apply tw-flex tw-flex-col tw-gap-8 sm:tw-gap-8px;
}

.MembershipItemItem {
  @apply tw-flex tw-items-center tw-gap-4 sm:tw-gap-4px;
}

.MembershipItemIconContainer {
  width: 20px;
  height: 20px;
}

.MembershipItemIcon {
  @apply tw-bg-transparent;
}

.MembershipItemText {
  @apply tw-text-sm tw-leading-normal;
}

.MembershipItemExpiring {
  @apply tw-text-red;
}
