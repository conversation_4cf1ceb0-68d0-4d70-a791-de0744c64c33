import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { X } from 'phosphor-react';
import { getIsLoadRewardDetailRequestCompleted } from '../../../../../redux/modules/rewards/selectors';
import { getIsRewardDetailDrawerShow } from '../../../../redux/promoVoucherList/selectors';
import { hideRewardDetailDrawer, fetchConsumerRewardDetail } from '../../../../redux/promoVoucherList/thunks';
import Drawer from '../../../../../common/components/Drawer';
import DrawerHeader from '../../../../../common/components/Drawer/DrawerHeader';
import RewardDetailTicket from './components/RewardDetailTicket';
import RewardDetailContent from './components/RewardDetailContent';
import RewardDetailFooter from './components/RewardDetailFooter';
import SkeletonLoader from './components/SkeletonLoader';
import styles from './RewardDetailDrawer.module.scss';

const RewardDetailDrawer = () => {
  const { t } = useTranslation(['User']);
  const dispatch = useDispatch();
  const isRewardDetailDrawerShow = useSelector(getIsRewardDetailDrawerShow);
  const isLoadRewardDetailRequestCompleted = useSelector(getIsLoadRewardDetailRequestCompleted);
  const handleHideRewardDetailDrawer = useCallback(() => dispatch(hideRewardDetailDrawer()), [dispatch]);

  useEffect(() => {
    if (isRewardDetailDrawerShow) {
      dispatch(fetchConsumerRewardDetail());
    }
  }, [dispatch, isRewardDetailDrawerShow]);

  return (
    <Drawer
      fullScreen
      className={styles.RewardDetailDrawer}
      show={isRewardDetailDrawerShow}
      header={
        <DrawerHeader
          left={
            <X
              weight="light"
              className={styles.RewardDetailDrawerCloseButton}
              data-test-id="user.promo-voucher-list.reward-detail-drawer.close-button"
              onClick={handleHideRewardDetailDrawer}
            />
          }
        >
          <div className={styles.RewardDetailDrawerHeaderTitleContainer}>
            <h3 className={styles.RewardDetailDrawerHeaderTitle}>{t('VoucherPromoDetails')}</h3>
          </div>
        </DrawerHeader>
      }
      onClose={handleHideRewardDetailDrawer}
    >
      {isLoadRewardDetailRequestCompleted ? (
        <div className={styles.RewardDetailDrawerWrapper}>
          <div className={styles.RewardDetailDrawerContent}>
            <RewardDetailTicket />
            <RewardDetailContent />
          </div>
          <RewardDetailFooter />
        </div>
      ) : (
        <SkeletonLoader />
      )}
    </Drawer>
  );
};

RewardDetailDrawer.displayName = 'RewardDetailDrawer';

export default RewardDetailDrawer;
