@use "../../../../../common/styles/variables" as *;

.RewardTicketListContainer {
  @apply tw-px-12 sm:tw-px-12px tw-py-24 sm:tw-py-24px;
}

.RewardTicketListTitle {
  @apply tw-pb-8 sm:tw-pb-8px tw-text-lg tw-font-bold tw-leading-normal;
}

.RewardTicketList {
  @apply tw-space-y-12 sm:tw-space-y-12px tw-list-none;
}

.RewardItemButton:global(.type-text-ghost) {
  @apply tw-p-0;
}

.RewardItemButtonContent {
  @apply tw-w-full;
}

.RewardTicket {
  &__Unavailable {
    @apply tw-opacity-40;
  }
}

.RewardTicketInfoTop {
  @apply tw-flex-1 tw-flex tw-items-end tw-justify-between;
}

.RewardTicketDescription {
  @apply tw-text-left;
}

.RewardTicketApplicableMerchants {
  @apply tw-block tw-text-sm tw-leading-normal tw-text-gray-700;

  @include text-line-clamp(2, 2.8em);
}

.RewardTicketDiscount {
  @apply tw-leading-relaxed tw-font-bold tw-uppercase;
}

.RewardTicketDiscountName {
  @apply tw-text-sm tw-leading-loose;

  @include text-line-clamp(2, 3.2em);
}

.RewardTicketStatusTag,
.RewardTicketExpiringTag {
  @apply tw-flex-shrink-0 tw-font-bold;
}

.RewardTicketInfoBottom {
  @apply tw-flex-1 tw-flex tw-items-end tw-justify-between tw-border-l-0 tw-border-r-0;

  &__NoMinSpend {
    @apply tw-justify-end;
  }
}

.RewardTicketDiscountLimitation {
  @apply tw-text-sm tw-leading-normal tw-text-gray-700;
}

.RewardTicketViewDetail {
  @apply tw-p-8 sm:tw-p-8px tw--m-8 sm:tw--m-8px tw-text-xs tw-leading-loose tw-text-orange tw-capitalize;
}
