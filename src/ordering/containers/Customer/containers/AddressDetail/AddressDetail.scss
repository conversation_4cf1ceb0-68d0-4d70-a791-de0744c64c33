@use "../../../../../Variables" as *;

.address-detail {
  height: 100vh;

  &__container {
    height: 100%;
    overflow: auto;
    width: 100%;
  }

  &__field {
    margin-top: 0.7vw;
    margin-bottom: 0.7vw;
  }

  &__title {
    color: $gray-3;

    &.required::after {
      content: "*";
      color: $status-red;
      line-height: 1em;
      font-size: 0.8571rem;
      vertical-align: top;
      margin-left: 0.3em;
    }
  }

  &__input {
    padding-top: 0;
    padding-bottom: 0;
    height: 24px;
    line-height: 24px;

    &::placeholder {
      color: $gray-4;
    }
  }

  &__detail-button {
    max-height: none;
  }

  &__detail-content {
    min-height: 24px;
  }

  &__save-button {
    letter-spacing: 1.25px;
  }

  &__icon-next svg {
    fill: $gray-3;
  }

  .PhoneInput {
    margin: 0;
    border: none;
  }

  .PhoneInput .PhoneInputCountry {
    margin-left: 0;
  }

  .PhoneInput .PhoneInputCountryIcon,
  .PhoneInput .PhoneInputCountrySelect + .PhoneInputCountryIcon--border {
    width: calc(0.51em * 3.5);
    height: calc(0.51em * 2.5);
  }

  .PhoneInput .PhoneInputInput,
  .PhoneInput .PhoneInputInput:focus {
    padding: 0;
    height: 24px;
    line-height: 24px;
    font-size: 1.1428rem;
  }
}

@media (min-width: 420px) {
  .address-detail {
    &__field {
      margin-top: 2px;
      margin-bottom: 2px;
    }
  }
}
