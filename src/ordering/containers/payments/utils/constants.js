import { PAGE_ROUTES } from '../../../common/utils/constants/index/index';

export const VENDOR_STRIPE = 'Stripe';

export const CREATE_ORDER_ERROR_CODES = {
  TRIAL_ORDERS_REACHED_LIMITATION: '57016',
  STORE_TEMP_STOPS_RECEIVING_ORDERS: '40027',
  PROMOTION_EXCEEDED_TOTAL_CLAIM_LIMIT: '54050',
  PROMOTION_INVALID: '54051',
  NO_PERMISSION: '40003',
  NO_STORE: '40006',
  NO_STORE_LOCATION: '40007',
  NO_DELIVERY_LOCATION: '40008',
  OVER_DELIVERY_DISTANCE: '40009',
  CREATE_ORDER_ERROR: '40010',
  CONTACT_NAME_AND_PHONE_REQUIRED: '40012',
  STORE_IS_ON_VACATION: '40013',
  PRODUCT_NOT_ENOUGH_INVENTORY: '54013',
  STORE_OUT_OF_STOCK: '54012',
  INVENTORY_SYNC_ERROR: '80000',
  INVENTORY_SYNC_TIMEOUT: '80001',
};

export const CREATE_ORDER_ERROR_ORDERING_ROUTES = {
  [CREATE_ORDER_ERROR_CODES.NO_PERMISSION]: PAGE_ROUTES.LOGIN,
  [CREATE_ORDER_ERROR_CODES.NO_STORE]: PAGE_ROUTES.MENU,
  [CREATE_ORDER_ERROR_CODES.NO_STORE_LOCATION]: PAGE_ROUTES.MENU,
  [CREATE_ORDER_ERROR_CODES.NO_DELIVERY_LOCATION]: PAGE_ROUTES.LOCATION,
  [CREATE_ORDER_ERROR_CODES.OVER_DELIVERY_DISTANCE]: PAGE_ROUTES.LOCATION,
  [CREATE_ORDER_ERROR_CODES.CREATE_ORDER_ERROR]: PAGE_ROUTES.CART,
  [CREATE_ORDER_ERROR_CODES.CONTACT_NAME_AND_PHONE_REQUIRED]: PAGE_ROUTES.CUSTOMER,
  [CREATE_ORDER_ERROR_CODES.STORE_IS_ON_VACATION]: PAGE_ROUTES.MENU,
  [CREATE_ORDER_ERROR_CODES.PRODUCT_NOT_ENOUGH_INVENTORY]: PAGE_ROUTES.CART,
  [CREATE_ORDER_ERROR_CODES.STORE_OUT_OF_STOCK]: PAGE_ROUTES.CART,
};

/**
 * i18n
 */
export const CREATE_ORDER_ERROR_I18N_KEYS = {
  [CREATE_ORDER_ERROR_CODES.TRIAL_ORDERS_REACHED_LIMITATION]: {
    titleI18nKey: 'ErrorTrailOrderReachedLimitationTitle',
    descriptionI18nKey: 'ErrorTrailOrderReachedLimitationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.STORE_TEMP_STOPS_RECEIVING_ORDERS]: {
    titleI18nKey: 'ErrorStoreTempStopsReceivingOrdersTitle',
    descriptionI18nKey: 'ErrorStoreTempStopsReceivingOrdersDescription',
  },
  [CREATE_ORDER_ERROR_CODES.PROMOTION_EXCEEDED_TOTAL_CLAIM_LIMIT]: {
    titleI18nKey: 'ErrorPromotionExceededTotalClaimLimitTitle',
    descriptionI18nKey: 'ErrorPromotionExceededTotalClaimLimitDescription',
  },
  [CREATE_ORDER_ERROR_CODES.PROMOTION_INVALID]: {
    titleI18nKey: 'ErrorPromotionInvalidTitle',
    descriptionI18nKey: 'ErrorPromotionInvalidDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_PERMISSION]: {
    titleI18nKey: 'ErrorNoPermissionTitle',
    descriptionI18nKey: 'ErrorNoPermissionDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_STORE]: {
    titleI18nKey: 'ErrorNoStoreTitle',
    descriptionI18nKey: 'ErrorNoStoreDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_STORE_LOCATION]: {
    titleI18nKey: 'ErrorNoStoreLocationTitle',
    descriptionI18nKey: 'ErrorNoStoreLocationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_DELIVERY_LOCATION]: {
    titleI18nKey: 'ErrorNoDeliveryLocationTitle',
    descriptionI18nKey: 'ErrorNoDeliveryLocationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.OVER_DELIVERY_DISTANCE]: {
    titleI18nKey: 'ErrorOverDeliveryDistanceTitle',
    descriptionI18nKey: 'ErrorOverDeliveryDistanceDescription',
  },
  [CREATE_ORDER_ERROR_CODES.CREATE_ORDER_ERROR]: {
    titleI18nKey: 'ErrorCreateOrderErrorTitle',
    descriptionI18nKey: 'ErrorCreateOrderErrorDescription',
  },
  [CREATE_ORDER_ERROR_CODES.CONTACT_NAME_AND_PHONE_REQUIRED]: {
    titleI18nKey: 'ErrorContactNameAndPhoneRequiredTitle',
    descriptionI18nKey: 'ErrorContactNameAndPhoneRequiredDescription',
  },
  [CREATE_ORDER_ERROR_CODES.STORE_IS_ON_VACATION]: {
    titleI18nKey: 'ErrorStoreIsOnVacationTitle',
    descriptionI18nKey: 'ErrorStoreIsOnVacationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.PRODUCT_NOT_ENOUGH_INVENTORY]: {
    titleI18nKey: 'ErrorProductNotEnoughInventoryTitle',
    descriptionI18nKey: 'ErrorProductNotEnoughInventoryDescription',
  },
  [CREATE_ORDER_ERROR_CODES.STORE_OUT_OF_STOCK]: {
    titleI18nKey: 'ErrorStoreOutOfStockTitle',
    descriptionI18nKey: 'ErrorStoreOutOfStockDescription',
  },
  [CREATE_ORDER_ERROR_CODES.INVENTORY_SYNC_ERROR]: {
    titleI18nKey: 'ErrorInventorySyncErrorTitle',
    descriptionI18nKey: 'ErrorInventorySyncErrorDescription',
  },
  [CREATE_ORDER_ERROR_CODES.INVENTORY_SYNC_TIMEOUT]: {
    titleI18nKey: 'ErrorInventorySyncTimeoutTitle',
    descriptionI18nKey: 'ErrorInventorySyncTimeoutDescription',
  },
};
// end of i18n
