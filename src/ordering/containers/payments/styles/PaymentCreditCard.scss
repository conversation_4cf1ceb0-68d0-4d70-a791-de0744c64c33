@use "../../../../Variables" as *;

.ordering-payment {
  height: 100vh;

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
  }

  &__item-content {
    flex: 1;
  }

  &__image-container {
    display: inline-block;
    width: 16%;
  }

  &__image {
    width: 100%;
  }

  &__description {
    display: inline-block;
    width: 75%;
  }

  &__label {
    width: 100%;
  }

  &__item.disabled &__image-container {
    filter: grayscale(100%) contrast(100%) brightness(100%);
    -webkit-filter: grayscale(100%) contrast(100%) brightness(100%);
    -moz-filter: grayscale(100%) contrast(100%) brightness(100%);
    -ms-filter: grayscale(100%) contrast(100%) brightness(100%);
    -o-filter: grayscale(100%) contrast(100%) brightness(100%);
    transition: filter 0.4s;
    -webkit-transition: -webkit-filter 0.4s;
    -moz-transition: -moz-filter 0.4s;
    -ms-transition: -ms-filter 0.4s;
    -o-transition: -o-filter 0.4s;
  }

  &__item.disabled &__description,
  &__prompt {
    color: $gray-2;
  }

  &__item.disabled .radio {
    opacity: 0;
  }
}

.ordering-card-list {
  &__image-container,
  &__description {
    display: inline-block;
  }

  &__image-container {
    width: 30%;
  }

  &__image {
    width: 80%;
  }

  &__description {
    width: 70%;
  }
}

.payment-credit-card {
  height: 100vh;

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
  }

  &__group-card-number {
    position: relative;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;

    &.error {
      z-index: 1;
    }
  }

  &__title {
    color: $gray-1;
  }

  &__group-left-bottom,
  &__group-right-bottom {
    width: 50%;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    transform: translateY(-1px);
  }

  &__group-left-bottom {
    border-bottom-right-radius: 0;
  }

  &__group-right-bottom {
    border-bottom-left-radius: 0;
  }

  &__group {
    overflow: hidden;
  }

  &__icon-container {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    height: 26px;
    margin: auto;
  }

  &__icon {
    display: inline-block;
    max-width: 36px;
  }

  &__input {
    height: 54px;
  }

  &__footer {
    position: sticky;
    width: 100%;
  }

  &__card-holder-name {
    .form__group {
      overflow: hidden;
    }
  }

  &-switch {
    padding-top: 1.428rem;
  }

  &__save-switch {
    color: $gray-2;
    padding-right: 8px;
  }

  &__save-switch.active {
    color: $main-color;
  }

  &__save-text {
    color: $gray-2;

    .payment-credit-card__link {
      padding-left: 4px;
    }
  }

  &__secure {
    padding-bottom: 10px;
  }

  &__secure-image {
    width: 23%;
  }

  &__secure-text {
    padding-left: 0.571rem;
  }

  &__link {
    font-weight: bold;
  }
}

.ordering-payment__card {
  height: 88px;

  &:first-child {
    border-top: 1px solid $gray-4;
  }

  &-add img {
    width: 50px;
  }
}
