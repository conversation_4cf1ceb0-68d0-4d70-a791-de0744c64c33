export const mockGeocodingResponse = [
  {
    address_components: [
      {
        long_name: 'Unnamed Road',
        short_name: 'Unnamed Road',
        types: ['route'],
      }, // street2 #1
      {
        long_name: '<PERSON><PERSON>',
        short_name: '<PERSON><PERSON>',
        types: ['political', 'sublocality', 'sublocality_level_1'],
      }, // street2 #2
      {
        long_name: 'Lang<PERSON><PERSON> Shi',
        short_name: 'Langfang Shi',
        types: ['locality', 'political'],
      }, // city
      {
        long_name: 'Hebei Sheng',
        short_name: 'Hebei Sheng',
        types: ['administrative_area_level_1', 'political'],
      }, // state
      {
        long_name: 'China',
        short_name: 'CN',
        types: ['country', 'political'],
      },
    ],
    formatted_address: 'Unnamed Road, Sanhe Shi, Langfang Shi, Hebei Sheng, China',
    geometry: {
      bounds: {
        northeast: {
          lat: 39.9624438,
          lng: 116.7906494,
        },
        southwest: {
          lat: 39.96201900000001,
          lng: 116.7897092,
        },
      },
      location: {
        lat: 39.9623723,
        lng: 116.7899861,
      },
      location_type: 'GEOMETRIC_CENTER',
      viewport: {
        northeast: {
          lat: 39.9635803802915,
          lng: 116.7915282802915,
        },
        southwest: {
          lat: 39.9608824197085,
          lng: 116.7888303197085,
        },
      },
    },
    place_id: 'ChIJ44GupLYK8TURzmULCPXrb2Y',
    types: ['route'],
  },
  {
    address_components: [
      {
        long_name: 'Sanhe',
        short_name: 'Sanhe',
        types: ['political', 'sublocality', 'sublocality_level_1'],
      },
      {
        long_name: 'Langfang',
        short_name: 'Langfang',
        types: ['locality', 'political'],
      },
      {
        long_name: 'Hebei',
        short_name: 'Hebei',
        types: ['administrative_area_level_1', 'political'],
      },
      {
        long_name: 'China',
        short_name: 'CN',
        types: ['country', 'political'],
      },
    ],
    formatted_address: 'Sanhe, Langfang, Hebei, China',
    geometry: {
      bounds: {
        northeast: {
          lat: 40.0850108,
          lng: 117.2599866,
        },
        southwest: {
          lat: 39.8081543,
          lng: 116.757202,
        },
      },
      location: {
        lat: 39.982718,
        lng: 117.078294,
      },
      location_type: 'APPROXIMATE',
      viewport: {
        northeast: {
          lat: 40.0850108,
          lng: 117.2599866,
        },
        southwest: {
          lat: 39.8081543,
          lng: 116.757202,
        },
      },
    },
    place_id: 'ChIJG-e9hsB58TURH0rVxbv6AxI',
    types: ['political', 'sublocality', 'sublocality_level_1'],
  },
  {
    address_components: [
      {
        long_name: 'Langfang',
        short_name: 'Langfang',
        types: ['locality', 'political'],
      },
      {
        long_name: 'Hebei',
        short_name: 'Hebei',
        types: ['administrative_area_level_1', 'political'],
      },
      {
        long_name: 'China',
        short_name: 'CN',
        types: ['country', 'political'],
      },
    ],
    formatted_address: 'Langfang, Hebei, China',
    geometry: {
      bounds: {
        northeast: {
          lat: 40.0850108,
          lng: 117.2599866,
        },
        southwest: {
          lat: 38.4703166,
          lng: 116.1166762,
        },
      },
      location: {
        lat: 39.53804700000001,
        lng: 116.683752,
      },
      location_type: 'APPROXIMATE',
      viewport: {
        northeast: {
          lat: 40.0850108,
          lng: 117.2599866,
        },
        southwest: {
          lat: 38.4703166,
          lng: 116.1166762,
        },
      },
    },
    place_id: 'ChIJQc3AP6gm7jURKFI_nBcTheA',
    types: ['locality', 'political'],
  },
  {
    address_components: [
      {
        long_name: 'Hebei',
        short_name: 'Hebei',
        types: ['administrative_area_level_1', 'political'],
      },
      {
        long_name: 'China',
        short_name: 'CN',
        types: ['country', 'political'],
      },
    ],
    formatted_address: 'Hebei, China',
    geometry: {
      bounds: {
        northeast: {
          lat: 42.6197176,
          lng: 119.8725768,
        },
        southwest: {
          lat: 36.0482063,
          lng: 113.4653687,
        },
      },
      location: {
        lat: 37.8956594,
        lng: 114.9042208,
      },
      location_type: 'APPROXIMATE',
      viewport: {
        northeast: {
          lat: 42.6197176,
          lng: 119.8725768,
        },
        southwest: {
          lat: 36.0482063,
          lng: 113.4653687,
        },
      },
    },
    place_id: 'ChIJ1YZMyjCpjDURIfse5yIKRWA',
    types: ['administrative_area_level_1', 'political'],
  },
  {
    address_components: [
      {
        long_name: 'China',
        short_name: 'CN',
        types: ['country', 'political'],
      },
    ],
    formatted_address: 'China',
    geometry: {
      bounds: {
        northeast: {
          lat: 53.5609739,
          lng: 134.7754563,
        },
        southwest: {
          lat: 17.9996,
          lng: 73.4994136,
        },
      },
      location: {
        lat: 35.86166,
        lng: 104.195397,
      },
      location_type: 'APPROXIMATE',
      viewport: {
        northeast: {
          lat: 53.5609739,
          lng: 134.7754563,
        },
        southwest: {
          lat: 17.9996,
          lng: 73.4994136,
        },
      },
    },
    place_id: 'ChIJwULG5WSOUDERbzafNHyqHZU',
    types: ['country', 'political'],
  },
];
