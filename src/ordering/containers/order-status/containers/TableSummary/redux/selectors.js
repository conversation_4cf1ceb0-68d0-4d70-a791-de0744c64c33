import _get from 'lodash/get';
import { createSelector } from 'reselect';
import { ORDER_STATUS, API_REQUEST_STATUS } from '../../../../../../common/utils/constants';
import {
  getUserIsLogin,
  getBusinessInfo,
  getCashbackRate,
  getEnableCashback,
  getMerchantCountry,
  getShippingType,
  getIsAlipayMiniProgram,
  getIsMaybankMiniProgram,
} from '../../../../../redux/modules/app';
import {
  getIsPayLaterOrderStorePayByCashOnly,
  getPayLaterOrderSubtotal,
  getPayLaterOrderStatus,
  getPayLaterOrderSubOrders,
  getPayLaterOrderItems,
  getPayLaterOrderDisplayPromotions,
  getPayLaterOrderLoyaltyDiscounts,
  getPayLaterOrderAppliedVoucherId,
} from '../../../../../redux/modules/order/selectors';
import { getIsLoadUniquePromosAvailableCountCompleted } from '../../../../../redux/modules/common/selectors';
import { getPayLaterSubmitOrderRequest as getSubmitOrderRequest } from '../../../redux/selector';

export const getOrderCashback = createSelector(
  getPayLaterOrderLoyaltyDiscounts,
  payLaterOrderLoyaltyDiscounts => Number(payLaterOrderLoyaltyDiscounts.map(item => item.displayDiscount)) || 0
);

export const getIsOrderPlaced = createSelector(
  getPayLaterOrderStatus,
  payLaterOrderStatus => payLaterOrderStatus === ORDER_STATUS.CREATED
);

export const getIsOrderPendingPayment = createSelector(
  getPayLaterOrderStatus,
  payLaterOrderStatus => payLaterOrderStatus === ORDER_STATUS.PENDING_PAYMENT
);

export const getSubOrdersMapping = createSelector(
  [getPayLaterOrderSubOrders, getPayLaterOrderItems],
  (payLaterOrderSubOrders, payLaterOrderItems) => {
    const subOrdersMapping = {};
    payLaterOrderItems.forEach(item => {
      const { submitId } = item;
      if (subOrdersMapping[submitId]) {
        subOrdersMapping[submitId].items.push(item);
      } else {
        const { submittedTime, comments } =
          payLaterOrderSubOrders.find(subOrder => subOrder.submitId === submitId) || {};
        const subOrder = {
          submitId,
          submittedTime,
          comments,
          items: [item],
        };

        subOrdersMapping[submitId] = subOrder;
      }
    });
    return subOrdersMapping;
  }
);

export const getOrderSubmissionRequestingStatus = createSelector(
  getSubmitOrderRequest,
  request => request.status === API_REQUEST_STATUS.PENDING
);

export const getOrderBillingPromoIfExist = createSelector(
  getPayLaterOrderDisplayPromotions,
  displayPromotions => displayPromotions?.length || ''
);

export const getOrderPromoDiscountType = createSelector(
  getPayLaterOrderDisplayPromotions,
  displayPromotions => displayPromotions[0]?.discountType
);

export const getOrderPromoDiscount = createSelector(
  getPayLaterOrderDisplayPromotions,
  displayPromotions => displayPromotions[0]?.discount
);

export const getOrderPromotionCode = createSelector(
  getPayLaterOrderDisplayPromotions,
  displayPromotions => displayPromotions[0]?.promotionCode
);

export const getOrderPromotionId = createSelector(
  getPayLaterOrderDisplayPromotions,
  displayPromotions => displayPromotions[0]?.promotionId
);

export const getPromoOrVoucherExist = createSelector(
  getOrderBillingPromoIfExist,
  getPayLaterOrderAppliedVoucherId,
  (orderBillingPromoIfExist, payLaterOrderAppliedVoucherId) =>
    !!(orderBillingPromoIfExist || payLaterOrderAppliedVoucherId)
);

export const getShouldShowRedirectLoader = state => state.orderStatus.tableSummary.redirectLoaderVisible;

export const getShouldShowProcessingLoader = state => state.orderStatus.tableSummary.processingLoaderVisible;

export const getShouldShowPayNowButton = createSelector(
  getIsAlipayMiniProgram,
  getIsMaybankMiniProgram,
  getIsOrderPendingPayment,
  (isAlipayMiniProgram, isMaybankMiniProgram, isOrderPendingPayment) =>
    isAlipayMiniProgram || isMaybankMiniProgram || !isOrderPendingPayment
);

export const getShouldShowSwitchButton = createSelector(
  getOrderCashback,
  getIsOrderPendingPayment,
  (cashback, isOrderPendingPayment) => !isOrderPendingPayment && cashback > 0
);

export const getReloadBillingByCashbackRequest = state => state.orderStatus.tableSummary.reloadBillingByCashbackRequest;

export const getIsReloadBillingByCashbackRequestPending = createSelector(
  getReloadBillingByCashbackRequest,
  request => request.status === API_REQUEST_STATUS.PENDING
);

export const getIsReloadBillingByCashbackRequestRejected = createSelector(
  getReloadBillingByCashbackRequest,
  request => request.status === API_REQUEST_STATUS.REJECTED
);

export const getPayByCouponsRequest = state => state.orderStatus.tableSummary.payByCouponsRequest;

export const getIsPayByCouponsRequestPending = createSelector(
  getPayByCouponsRequest,
  request => request.status === API_REQUEST_STATUS.PENDING
);

export const getIsPayByCouponsRequestFulfilled = createSelector(
  getPayByCouponsRequest,
  request => request.status === API_REQUEST_STATUS.FULFILLED
);

export const getShouldRemoveFooter = createSelector(
  getIsAlipayMiniProgram,
  getIsMaybankMiniProgram,
  getIsPayLaterOrderStorePayByCashOnly,
  (isAlipayMiniProgram, isMaybankMiniProgram, isPayLaterOrderStorePayByCashOnly) =>
    (isAlipayMiniProgram || isMaybankMiniProgram) && isPayLaterOrderStorePayByCashOnly
);

export const getShouldDisablePayButton = createSelector(
  getIsPayByCouponsRequestPending,
  getIsPayByCouponsRequestFulfilled,
  getUserIsLogin,
  getIsLoadUniquePromosAvailableCountCompleted,
  (isRequestPending, isRequestFulfilled, isLogin, isLoadUniquePromosAvailableCountCompleted) =>
    // WB-4761: window location redirection will take some time, if we only consider pending status then the button will be activated accidentally.
    // Therefore, we should also need to take fulfilled status into consideration.
    isRequestPending || isRequestFulfilled || (isLogin && !isLoadUniquePromosAvailableCountCompleted)
);

export const getCartItemsQuantityCleverTap = createSelector(getPayLaterOrderItems, payLaterOrderItems => {
  let count = 0;

  (payLaterOrderItems || []).forEach(item => {
    const { quantity } = item || {};
    count += quantity;
  });
  return count;
});

export const getCleverTapAttributes = createSelector(
  getBusinessInfo,
  getShippingType,
  getMerchantCountry,
  getEnableCashback,
  getCashbackRate,
  getCartItemsQuantityCleverTap,
  getPayLaterOrderSubtotal,
  (businessInfo, shippingType, country, enableCashback, cashbackRate, cartItemsQuantity, payLaterOrderSubtotal) => ({
    'store name': _get(businessInfo, 'stores.0.name', ''),
    'store id': _get(businessInfo, 'stores.0.id', ''),
    'shipping type': shippingType,
    country,
    cashback: enableCashback ? cashbackRate : undefined,
    'cart items quantity': cartItemsQuantity,
    'cart amount': payLaterOrderSubtotal,
  })
);
