@use "../../../../../../Variables" as *;
@use "../../../../../../Animates" as *;

.logistics-processing {
  &__step {
    position: relative;
    padding-left: 11px;
    padding-bottom: 0;
    margin-left: 11px;
    height: 0;
    color: $new-grey-1-1;
    opacity: 0;

    transition: padding-bottom 0.2s linear 0.2s, height 0.2s linear 0.2s, opacity 0.2s;
    -webkit-transition: padding-bottom 0.2s linear 0.2s, height 0.2s linear 0.2s, opacity 0.2s;
    -moz-transition: padding-bottom 0.2s linear 0.2s, height 0.2s linear 0.2s, opacity 0.2s;
    -ms-transition: padding-bottom 0.2s linear 0.2s, height 0.2s linear 0.2s, opacity 0.2s;
  }

  &__step--active {
    height: auto;
    opacity: 1;
  }

  &__step:last-child {
    padding-bottom: 0;
  }

  &__step::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    margin: 5px 0 0 -1px;
    width: 1px;
    height: 100%;
    background-color: $gray-4;
    opacity: 0;
  }

  &__step--complete::before {
    background-color: $theme-color;
  }

  &__step:last-child::before {
    background-color: transparent;
  }

  &__icon {
    position: absolute;
    left: 0;
    top: 0;
    display: inline-block;
    margin: 5px;
    width: 11px;
    height: 11px;
    border-radius: 50%;
    background-color: $gray-4;

    transform: translateX(-100%);
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
  }

  &__step--active &__icon,
  &__step--complete &__icon {
    background-color: $theme-color;
  }

  &__icon-background {
    content: "";
    position: absolute;
    left: -5px;
    top: -5px;
    display: inline-block;
    width: 21px;
    height: 21px;
    border-radius: 50%;
    opacity: 0.3;
  }

  &__icon::after {
    @extend .logistics-processing__icon-background;
  }

  &__step--active &__icon::after {
    background-color: $theme-color;
    animation-duration: 3s;

    @extend .beep-animated;
    @extend .beep-animated--infinite;
    @extend .beep-scale-to-disappearance;
  }

  &__step-title {
    line-height: 21px;
  }

  &__step--active &__step-title {
    color: $main-color;
  }

  &__step-description {
    .icon__smaller {
      margin-left: -1.4vw;
    }
  }

  &__icon-expand-more {
    transition: transform 0.4s;
    -webkit-transition: 0.4s;
    -moz-transition: 0.4s;
    -ms-transition: 0.4s;
  }

  &__list--expand &__step {
    padding-left: 11px;
    height: 25px;
    opacity: 1;

    transition: padding-bottom 0.4s, height 0.4s, opacity 0.2s linear 0.4s;
    -webkit-transition: padding-bottom 0.4s, height 0.4s, opacity 0.2s linear 0.4s;
    -moz-transition: padding-bottom 0.4s, height 0.4s, opacity 0.2s linear 0.4s;
    -ms-transition: padding-bottom 0.4s, height 0.4s, opacity 0.2s linear 0.4s;
  }

  &__list--expand &__step--active {
    height: auto;
  }

  &__list--expand &__step:not(:last-child) {
    padding-bottom: 2.4vw;
  }

  &__list--expand &__step--active:not(:last-child) {
    padding-bottom: 3.8vw;
  }

  &__list--expand &__step::before {
    opacity: 1;

    transition: opacity 0.2s linear 0.4s;
    -webkit-transition: opacity 0.2s linear 0.4s;
    -moz-transition: opacity 0.2s linear 0.4s;
    -ms-transition: opacity 0.2s linear 0.4s;
  }

  &__list--expand + &__icon-expand-more {
    transform-origin: center;
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -ms-transform-origin: center;

    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
  }
}

@media (min-width: 420px) {
  .logistics-processing {
    &__step-description {
      color: $new-grey-1-1;

      .icon__smaller {
        margin-left: -4px;
      }
    }

    &__list--expand &__step:not(:last-child) {
      padding-bottom: 8px;
    }

    &__list--expand &__step--active:not(:last-child) {
      padding-bottom: 16px;
    }
  }
}
