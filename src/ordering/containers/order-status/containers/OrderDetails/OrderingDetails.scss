@use "../../../../../Variables" as *;

.ordering-details {
  height: 100vh;
  background-color: $gray-6;

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    padding-bottom: 120px;
  }

  &__button-contact-us {
    color: $status-blue;
  }

  &__items {
    display: block;
  }

  &__billing-container {
    margin-top: 3.8vw;
  }

  &__subtitle {
    color: $gray-3;
  }

  &__shipping-type-tag {
    min-width: 82px;
    padding: 6px;
    font-weight: normal;
  }

  &__receipt-info {
    width: 100%;
  }

  &__copy-button {
    max-height: 30px;
    line-height: 28px;

    &:active {
      background-color: $gray-5;
    }
  }

  &__after-sales-buttons > button + button {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: 16px;
      right: 16px;
      display: block;
      height: 2px;
      background-color: $gray-6;
    }
  }

  &__footer {
    background-color: white;
    box-shadow: 0 -4px 16px 0 rgba(0, 0, 0, 0.06);
  }
}

@media (min-width: 420px) {
  .ordering-details {
    &__list {
      margin-bottom: 12px;
    }

    &__billing-container {
      margin-top: 12px;
    }
  }
}
