import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import { createSelector } from 'reselect';
import Constants, { API_REQUEST_STATUS } from '../../../../common/utils/constants';
import { AVAILABLE_REPORT_DRIVER_ORDER_STATUSES, E_INVOICE_TYPES } from '../constants';
import { getQueryString } from '../../../../common/utils';
import { getUserProfile, getTableId } from '../../../redux/modules/app';
import {
  getIsOrderUseStorehubLogistics,
  getIsPreOrder,
  getOrderEInvoiceLinkType,
  getOrderShippingType,
  getOrderStatus,
  getOrderTableId,
  getLoadOrderRequestData,
  getLoadOrderRequestStatus,
} from '../../../redux/modules/order/selectors';

const { PROMO_TYPE, DELIVERY_METHOD, ORDER_STATUS } = Constants;

export const getURLSearchReceiptNumber = () => getQueryString('receiptNumber');

export const getReceiptNumber = state => state.orderStatus.common.receiptNumber;

export const getIsShowReorderButton = createSelector(
  getOrderStatus,
  getOrderShippingType,
  (orderStatus, shippingType) =>
    (shippingType === DELIVERY_METHOD.DELIVERY && orderStatus === ORDER_STATUS.DELIVERED) ||
    (shippingType === DELIVERY_METHOD.PICKUP && orderStatus === ORDER_STATUS.PICKED_UP)
);

export const getIsOnDemandOrder = createSelector(getIsPreOrder, isPreOrder => !isPreOrder);

export const getCancelOrderStatus = state => state.orderStatus.common.cancelOrderStatus;

export const getPromotion = createSelector(getLoadOrderRequestData, order => {
  if (order && order.appliedVoucher) {
    return {
      promoCode: order.appliedVoucher.voucherCode,
      discount: order.appliedVoucher.value,
      promoType: PROMO_TYPE.VOUCHER_FOR_PAY_LATER,
    };
  }

  if (order && order.displayPromotions && order.displayPromotions.length) {
    const appliedPromo = order.displayPromotions[0];
    return {
      promoCode: appliedPromo.promotionCode,
      discount: appliedPromo.displayDiscount,
      promoType: PROMO_TYPE.PROMOTION_FOR_PAY_LATER,
    };
  }

  return null;
});

export const getDisplayDiscount = createSelector(getLoadOrderRequestData, order => {
  const { loyaltyDiscounts } = order || {};
  const { displayDiscount } = loyaltyDiscounts && loyaltyDiscounts.length > 0 ? loyaltyDiscounts[0] : '';

  return displayDiscount;
});

export const getLiveChatUserProfile = createSelector(getUserProfile, profile => ({
  phone: profile.phone || '',
  name: profile.name || '',
  email: profile.email || '',
}));

export const getHasOrderTableIdChanged = createSelector(getTableId, getOrderTableId, (prevTableId, currTableId) => {
  const shouldSkipDiffCheck = !prevTableId || !currTableId;

  return !(shouldSkipDiffCheck || _isEqual(prevTableId, currTableId));
});

// Pay Later Order
export const getPayLaterOrderInfo = state => state.orderStatus.common.payLaterOrderInfo;

export const getPayLaterSubmitOrderRequest = createSelector(
  getPayLaterOrderInfo,
  payLaterOrderInfo => payLaterOrderInfo.submitOrderRequest
);

// Store Review
export const getStoreReviewInfo = state => state.orderStatus.common.storeReviewInfo;

export const getIsStoreWarningModalVisible = createSelector(
  getStoreReviewInfo,
  storeReviewInfo => storeReviewInfo.warningModalVisible
);

export const getIsStoreLoadingIndicatorVisible = createSelector(
  getStoreReviewInfo,
  storeReviewInfo => storeReviewInfo.loadingIndicatorVisible
);

export const getStoreReviewInfoData = createSelector(getStoreReviewInfo, storeReviewInfo => storeReviewInfo.data);

export const getStoreReviewLoadDataRequest = createSelector(
  getStoreReviewInfo,
  storeReviewInfo => storeReviewInfo.loadDataRequest
);

export const getStoreReviewSaveDataRequest = createSelector(
  getStoreReviewInfo,
  storeReviewInfo => storeReviewInfo.saveDataRequest
);

export const getIfStoreReviewInfoExists = createSelector(
  getStoreReviewInfoData,
  storeReviewInfoData => !_isEmpty(storeReviewInfoData)
);

export const getStoreRating = createSelector(
  getStoreReviewInfoData,
  storeReviewInfoData => _get(storeReviewInfoData, 'rating', 0) || 0
);

export const getStoreComment = createSelector(
  getStoreReviewInfoData,
  storeReviewInfoData => _get(storeReviewInfoData, 'comments', '') || ''
);

export const getStoreTableId = createSelector(
  getStoreReviewInfoData,
  storeReviewInfoData => storeReviewInfoData.tableId
);

export const getStoreFullDisplayName = createSelector(
  getStoreReviewInfoData,
  storeReviewInfoData => _get(storeReviewInfoData, 'storeDisplayName', '') || _get(storeReviewInfoData, 'storeName', '')
);

export const getStoreShippingType = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'shippingType', '')
);

export const getIsMerchantContactAllowable = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'isMerchantContactAllowable', false)
);

export const getHasStoreReviewed = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'hasReviewed', false)
);

export const getIsStoreReviewable = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'isReviewable', false)
);

export const getStoreGoogleReviewURL = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'googleReviewURL', '')
);

export const getIsStoreReviewExpired = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'isExpired', false)
);

export const getIsStoreReviewSupportable = createSelector(getStoreReviewInfoData, storeReviewInfoData =>
  _get(storeReviewInfoData, 'isSupportable', false)
);

export const getOffline = state => state.orderStatus.storeReview.offline;

/*
 * Selectors derived from state
 */
export const getIsLoadOrderCompleted = createSelector(getLoadOrderRequestStatus, loadOrderRequestStatus =>
  [API_REQUEST_STATUS.FULFILLED, API_REQUEST_STATUS.REJECTED].includes(loadOrderRequestStatus)
);

export const getIsReportUnsafeDriverButtonDisabled = createSelector(
  getOrderStatus,
  orderStatus => !AVAILABLE_REPORT_DRIVER_ORDER_STATUSES.includes(orderStatus)
);

export const getIsEInvoiceEntryButtonShow = createSelector(getOrderEInvoiceLinkType, orderEInvoiceLinkType =>
  [E_INVOICE_TYPES.REQUEST, E_INVOICE_TYPES.VIEW].includes(orderEInvoiceLinkType)
);

export const getIsAfterSalesCardShow = createSelector(
  getIsOrderUseStorehubLogistics,
  getIsEInvoiceEntryButtonShow,
  (isOrderUseStorehubLogistics, isEInvoiceEntryButtonShow) => isOrderUseStorehubLogistics || isEInvoiceEntryButtonShow
);
