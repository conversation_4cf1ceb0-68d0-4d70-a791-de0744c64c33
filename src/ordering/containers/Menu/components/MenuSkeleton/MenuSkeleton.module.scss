.menuSkeletonBusinessLogo {
  @apply tw-mr-12 sm:tw-mr-12px tw-relative tw-flex tw-flex-shrink-0;

  width: 22.35%;
  max-width: 80px;
}

.menuSkeletonBusinessName {
  @apply tw-flex tw-items-center tw-py-8 sm:tw-py-8px tw-my-0 tw-w-3/4;
}

.menuSkeletonStoreName {
  @apply tw-w-1/3 sm:tw-py-4px tw-py-4;
}

.menuSkeletonSearchBox {
  @apply tw-px-16 sm:tw-px-16px tw-py-4 sm:tw-py-4px tw-rounded-2xl;

  height: 40px;
}

.menuSkeletonSearchIcon {
  @apply tw-rounded;

  height: 40px;
  width: 40px;
}

.menuSkeletonBestSellerCategoryProductList {
  @apply tw-relative tw-grid tw-grid-cols-2 sm:tw-grid-cols-2 md:tw-grid-cols-2 tw-gap-4 sm:tw-gap-4px tw-px-8 sm:tw-px-8px tw-pt-12 sm:tw-pt-12px tw-pb-2 sm:tw-pb-2px tw-mx-2 sm:tw-mx-2px;

  &::before {
    @apply tw-absolute tw-top-0 tw-left-8 sm:tw-left-8px tw-right-8 sm:tw-right-8px tw-block tw-bg-gray-200 tw-mx-6 sm:tw-mx-6px;

    content: "";
    height: 1px;
  }
}

.menuSkeletonBestSellerCategoryProductItemTitle {
  @apply tw-mr-12 tw-mx-2 sm:tw-mx-2px tw-py-4 sm:tw-py-4px tw-mt-8 sm:tw-mt-8px sm:tw-mr-12px tw-w-2/3;
}

.menuSkeletonBestSellerCategoryProductItemPrice {
  @apply tw-px-2 sm:tw-px-2px tw-w-1/4;
}

.menuSkeletonCategoryProductList .menuSkeletonProductItem {
  &::before {
    @apply tw-block tw-bg-gray-200 tw-mx-6 sm:tw-mx-6px;

    content: "";
    height: 1px;
  }
}

.menuSkeletonCategoryProductTitle {
  @apply tw-mx-16 sm:tw-mx-16px tw-my-8 sm:tw-my-8px tw-py-8 sm:tw-py-8px tw-w-1/2;
}

.menuSkeletonProductItem {
  @apply tw-cursor-pointer;
}

.menuSkeletonProductItemTitle {
  @apply tw-py-4 sm:tw-py-4px tw-my-4 sm:tw-my-4px tw-w-2/3;
}

.menuSkeletonProductItemTitle,
.menuSkeletonProductItemDescription {
  @apply tw-mr-12 sm:tw-mr-12px;
}

.menuSkeletonProductItemImageContainer {
  @apply tw-m-2 sm:tw-m-2px;
}
