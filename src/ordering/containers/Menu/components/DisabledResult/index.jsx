import React from 'react';
import { useSelector } from 'react-redux';
import AlertWarningImage from '../../../../../images/alert-warning-icon.svg';
import { DISABLED_RESULT_KEYS } from '../../constants';
import { getDisabledResultInfo } from '../../redux/common/selectors';
import Result from '../../../../../common/components/Result';
import ResultContent from '../../../../../common/components/Result/ResultContent';

const ICONS = {
  [DISABLED_RESULT_KEYS.TNGD_MINI_PROGRAM_NOT_SUPPORTED]: AlertWarningImage,
};

const DisabledResult = () => {
  const disabledResultInfo = useSelector(getDisabledResultInfo);
  const { key, title, description } = disabledResultInfo || {};

  return (
    <Result mountAtRoot show disableBackButtonSupport isCloseButtonShow={false}>
      <ResultContent imageSrc={ICONS[key]} content={description} title={title} />
    </Result>
  );
};

DisabledResult.displayName = 'DisabledResult';

export default DisabledResult;
