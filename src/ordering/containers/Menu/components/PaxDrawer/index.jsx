import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMount } from 'react-use';
import { useTranslation } from 'react-i18next';
import { CaretLeft } from 'phosphor-react';
import CleverTap from '../../../../../utils/clevertap';
import { getTableId } from '../../redux/common/selectors';
import { getPaymentInfoForCleverTap, getStoreInfoForCleverTap } from '../../../../redux/modules/app';
import { fetchCartPax } from '../../redux/cart/thunks';
import { getIsPaxDrawerVisible, getIsEnablePayLater, getIsPaxDrawerDisplay } from '../../redux/cart/selectors';
import Drawer from '../../../../../common/components/Drawer';
import DrawerHeader from '../../../../../common/components/Drawer/DrawerHeader';
import Button from '../../../../../common/components/Button';
import PaxNumberButtons from './components/PaxNumberButtons';
import PaxInputNumber from './components/PaxInputNumber';
import styles from './PaxDrawer.module.scss';

const PaxDrawer = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const tableId = useSelector(getTableId);
  const isEnabledPayLater = useSelector(getIsEnablePayLater);
  const isPaxDrawerVisible = useSelector(getIsPaxDrawerVisible);
  const isPaxDrawerDisplay = useSelector(getIsPaxDrawerDisplay);
  const paymentInfoForCleverTap = useSelector(getPaymentInfoForCleverTap);
  const storeInfoForCleverTap = useSelector(getStoreInfoForCleverTap);
  const [selectedOtherPax, setSelectedOtherPax] = React.useState(false);
  const handleBackButtonClick = useCallback(() => {
    setSelectedOtherPax(false);
  }, []);
  const handleOtherPaxButtonClick = useCallback(() => {
    setSelectedOtherPax(true);

    CleverTap.pushEvent('Input Pax Count (Others) - Click', { ...storeInfoForCleverTap, ...paymentInfoForCleverTap });
  }, [storeInfoForCleverTap, paymentInfoForCleverTap]);

  useMount(async () => {
    await dispatch(fetchCartPax());
  });

  useEffect(() => {
    if (isPaxDrawerVisible) {
      CleverTap.pushEvent('Input Pax Count - View Page', { ...storeInfoForCleverTap, ...paymentInfoForCleverTap });
    }
  }, [paymentInfoForCleverTap, storeInfoForCleverTap, isPaxDrawerVisible]);

  if (!isPaxDrawerDisplay) {
    return null;
  }

  return (
    <Drawer
      className={styles.PaxDrawer}
      disableBackButtonSupport={false}
      show={isPaxDrawerVisible}
      header={
        <DrawerHeader
          left={
            selectedOtherPax ? (
              <Button
                type="text"
                className={styles.PaxDrawerBackButton}
                contentClassName={styles.PaxDrawerBackButtonContent}
                data-test-id="ordering.menu.pax-drawer.back-button"
                onClick={handleBackButtonClick}
              >
                <CaretLeft size={24} />
              </Button>
            ) : null
          }
          right={tableId ? <span className={styles.PaxDrawerTableId}>{t('TableIdText', { tableId })}</span> : null}
        >
          <h3 className={styles.PaxDrawerTitle}>
            {isEnabledPayLater ? t('PaxDrawerPayLaterTitle') : t('PaxDrawerPayFirstTitle')}
          </h3>
        </DrawerHeader>
      }
    >
      {selectedOtherPax ? <PaxInputNumber /> : <PaxNumberButtons onClickOthersButton={handleOtherPaxButtonClick} />}
    </Drawer>
  );
};

PaxDrawer.displayName = 'PaxDrawer';

export default PaxDrawer;
