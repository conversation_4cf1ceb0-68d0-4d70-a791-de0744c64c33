import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMount } from 'react-use';
import { useTranslation } from 'react-i18next';
import CleverTap from '../../../../../../../utils/clevertap';
import { getPaymentInfoForCleverTap, getStoreInfoForCleverTap } from '../../../../../../redux/modules/app';
import { getIsSaveCartPaxRequestPending } from '../../../../redux/cart/selectors';
import { saveCartPax } from '../../../../redux/cart/thunks';
import InputNumber from '../../../../../../../common/components/Input/Number';
import PaxDrawerFooter from '../PaxDrawerFooter';
import styles from './PaxInputNumber.module.scss';

const MAX_PAX_NUMBER = 99;

const PaxInputNumber = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const isSaveCartPaxRequestPending = useSelector(getIsSaveCartPaxRequestPending);
  const paymentInfoForCleverTap = useSelector(getPaymentInfoForCleverTap);
  const storeInfoForCleverTap = useSelector(getStoreInfoForCleverTap);
  const [pax, setPax] = React.useState('');
  const [isInvalidPax, setIsInvalidPax] = React.useState(true);
  const isDisabledStartOrderingButton = useMemo(() => !pax || isSaveCartPaxRequestPending, [
    pax,
    isSaveCartPaxRequestPending,
  ]);
  const handlePaxNumberInputChange = useCallback(paxValue => {
    setPax(paxValue);
  }, []);
  const handleValidation = useCallback(({ isInvalid }) => {
    setIsInvalidPax(isInvalid);
  }, []);
  const handleStartOrderingButtonClick = useCallback(() => {
    if (!isInvalidPax) {
      dispatch(saveCartPax(Number(pax)));
    }
  }, [dispatch, isInvalidPax, pax]);

  useMount(() => {
    CleverTap.pushEvent('Input Pax Count (Others) - View Page', {
      ...storeInfoForCleverTap,
      ...paymentInfoForCleverTap,
    });
  });

  return (
    <>
      <section className={styles.PaxInputNumberSection}>
        <InputNumber
          containerClassName={styles.PaxInputNumberContainer}
          className={styles.PaxInputNumber}
          autoFocus
          data-test-id="ordering.menu.pax-drawer.input-number"
          label={t('PaxDrawerInputNumberLabel')}
          name="pax"
          rules={{ required: true }}
          step={1}
          min={1}
          max={MAX_PAX_NUMBER}
          value={pax}
          onChange={handlePaxNumberInputChange}
          onValidation={handleValidation}
        />
      </section>

      <PaxDrawerFooter
        disabled={isDisabledStartOrderingButton}
        onClickStartOrderingButton={handleStartOrderingButtonClick}
      />
    </>
  );
};

PaxInputNumber.displayName = 'PaxInputNumber';

export default PaxInputNumber;
