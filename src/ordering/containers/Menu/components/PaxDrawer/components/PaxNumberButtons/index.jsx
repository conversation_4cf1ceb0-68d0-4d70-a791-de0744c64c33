import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import CleverTap from '../../../../../../../utils/clevertap';
import { getPaymentInfoForCleverTap, getStoreInfoForCleverTap } from '../../../../../../redux/modules/app';
import { getIsSaveCartPaxRequestPending } from '../../../../redux/cart/selectors';
import { saveCartPax } from '../../../../redux/cart/thunks';
import Button from '../../../../../../../common/components/Button';
import PaxDrawerFooter from '../PaxDrawerFooter';
import styles from './PaxNumberButtons.module.scss';

const MAX_SELECT_PAX_NUMBER = 9;

const PaxNumberButtons = ({ onClickOthersButton }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const isSaveCartPaxRequestPending = useSelector(getIsSaveCartPaxRequestPending);
  const paymentInfoForCleverTap = useSelector(getPaymentInfoForCleverTap);
  const storeInfoForCleverTap = useSelector(getStoreInfoForCleverTap);
  const [pax, setPax] = React.useState(0);
  const paxNumberList = [...Array(MAX_SELECT_PAX_NUMBER).keys()].map(i => i + 1);
  const isDisabledStartOrderingButton = useMemo(() => !pax || isSaveCartPaxRequestPending, [
    pax,
    isSaveCartPaxRequestPending,
  ]);
  const handlePaxNumberButtonClick = useCallback(
    paxNumber => {
      setPax(paxNumber);

      CleverTap.pushEvent('Input Pax Count - Click Pax Number', {
        ...storeInfoForCleverTap,
        ...paymentInfoForCleverTap,
      });
    },
    [storeInfoForCleverTap, paymentInfoForCleverTap]
  );
  const handleStartOrderingButtonClick = useCallback(() => {
    dispatch(saveCartPax(pax));
  }, [dispatch, pax]);

  return (
    <>
      <section className={styles.PaxDrawerButtonGroup}>
        {paxNumberList.map(paxNumber => (
          <div className={styles.PaxDrawerButtonContainer} key={`ordering-menu-pax-button-${paxNumber}`}>
            <Button
              className={styles.PaxDrawerButton}
              type={pax === paxNumber ? 'primary' : 'secondary'}
              data-test-id={`ordering.menu.pax-drawer.button-${paxNumber}`}
              onClick={() => {
                handlePaxNumberButtonClick(paxNumber);
              }}
            >
              {paxNumber}
            </Button>
          </div>
        ))}

        <div className={styles.PaxDrawerButtonContainer}>
          <Button
            className={styles.PaxDrawerButton}
            type="secondary"
            data-test-id="ordering.menu.pax-drawer.button-others"
            onClick={onClickOthersButton}
          >
            {t('Others')}
          </Button>
        </div>
      </section>
      <PaxDrawerFooter
        disabled={isDisabledStartOrderingButton}
        onClickStartOrderingButton={handleStartOrderingButtonClick}
      />
    </>
  );
};

PaxNumberButtons.displayName = 'PaxNumberButtons';

PaxNumberButtons.propTypes = {
  onClickOthersButton: PropTypes.func,
};

PaxNumberButtons.defaultProps = {
  onClickOthersButton: () => {},
};

export default PaxNumberButtons;
