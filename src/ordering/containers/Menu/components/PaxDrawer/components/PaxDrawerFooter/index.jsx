import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { getIsSaveCartPaxRequestPending } from '../../../../redux/cart/selectors';
import Button from '../../../../../../../common/components/Button';
import styles from './PaxDrawerFooter.module.scss';

const PaxDrawerFooter = ({ disabled, onClickStartOrderingButton }) => {
  const { t } = useTranslation();
  const isSaveCartPaxRequestPending = useSelector(getIsSaveCartPaxRequestPending);

  return (
    <footer className={styles.PaxDrawerFooter}>
      <Button
        block
        className={styles.PaxDrawerStartOrderingButton}
        data-test-id="ordering.menu.pax-drawer.pax-number-buttons.start-ordering-button"
        disabled={disabled}
        loading={isSaveCartPaxRequestPending}
        onClick={onClickStartOrderingButton}
      >
        {t('StartOrdering')}
      </Button>
    </footer>
  );
};

PaxDrawerFooter.displayName = 'PaxDrawerFooter';

PaxDrawerFooter.propTypes = {
  disabled: PropTypes.bool,
  onClickStartOrderingButton: PropTypes.func,
};

PaxDrawerFooter.defaultProps = {
  disabled: true,
  onClickStartOrderingButton: () => {},
};

export default PaxDrawerFooter;
