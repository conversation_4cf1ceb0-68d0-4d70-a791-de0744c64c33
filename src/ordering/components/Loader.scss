@use "../../Variables" as *;

@keyframes circle-loader-animation {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

.circle-loader {
  display: inline-block;
  width: 1.8em;
  height: 1.8em;
  border-radius: 50%;
  border: 2px solid $gray-3;
  animation: circle-loader-animation 1s linear infinite;
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.prompt-loader {
  position: relative;
  min-width: 80px;
  color: $gray-2;
  background-color: $gray-6;
  z-index: $z-index-base * 2;

  &::before {
    content: "";
    display: block;
    width: 100%;
    padding: 50% 0;
  }

  &__content {
    position: absolute;
  }

  &__text {
    display: block;
  }
}

.page-loader {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: $z-index-base;

  .prompt-loader {
    transform: translateY(-90%);
  }
}
