.loader {
  @apply tw-text-2xl tw-text-gray-600;
}

.addressLocationContentCommonStyle {
  @apply tw-flex-1 tw-px-16 sm:tw-px-16px tw-py-16 sm:tw-py-16px;
}

.addressLocationContent {
  @extend .addressLocationContentCommonStyle;
  @apply tw-overflow-x-auto;
}

.addressLocationContentInitializing {
  @extend .addressLocationContentCommonStyle;
  @apply tw-flex tw-items-center tw-justify-center;
}

.addressLocationDrawerEmptyContent {
  @apply tw-relative tw-flex tw-flex-col tw-items-center tw-justify-center;

  top: 10vh;
}

.addressLocationDrawerEmptyImage {
  @apply tw-my-8 sm:tw-my-8px;

  width: 30%;
  max-width: 120px;
  aspect-ratio: 1 / 1;
}

.addressDropdownDrawerEmptyDescription {
  @apply tw-p-8 sm:tw-p-8px tw-leading-relaxed tw-font-bold tw-text-gray-700;
}

.addressLocationItem:nth-child(n + 2) .addressLocationItemButton {
  @apply tw-border-t tw-border-gray-300;
}
