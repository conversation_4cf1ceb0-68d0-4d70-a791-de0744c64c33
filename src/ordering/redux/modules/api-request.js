import config from '../../../config';
import { AUTH_INFO, API_REQUEST_OMIT_CREDENTIALS } from '../../../common/utils/constants';
import { get, post } from '../../../common/utils/api/request';

export const getProfileInfo = consumerId => get(`/api/v3/consumers/${consumerId}/profile`);

// shippingType field: https://storehub.atlassian.net/browse/WB-8827
export const getVoucherConsumerList = (consumerId, shippingType) =>
  get(`/api/consumers/${consumerId}/vouchers`, {
    queryParams: { shippingType },
  });

export const getSearchPromotionInfo = ({ consumerId, business, promoCode }) =>
  get(`/api/consumers/${consumerId}/vouchers`, {
    queryParams: {
      business,
      search: promoCode,
    },
  });

export const postLoginGuest = () => post('/api/login/guest');

export const getMseLoginCode = () => get('/api/v3/mae/login-code');

export const postGetAuthTokens = ({ business, code }) =>
  post(
    config.authApiUrl,
    {
      client: AUTH_INFO.CLIENT,
      grant_type: AUTH_INFO.MAE_GRANT_TYPE,
      business_name: business,
      code,
    },
    {
      credentials: API_REQUEST_OMIT_CREDENTIALS,
    }
  );

export const getUrlsValidation = h =>
  get('/api/v3/urls/validation', {
    queryParams: { h },
  });

export const getCustomerInfo = ({ consumerId, business, pointsTotalEarned = false, rewardsTotal = false }) =>
  get(`/api/v3/consumers/${consumerId}/customer`, {
    queryParams: {
      business,
      pointsTotalEarned,
      rewardsTotal,
    },
  });
