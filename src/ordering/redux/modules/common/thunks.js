import { createAsyncThunk } from '@reduxjs/toolkit';
import { SOURCE_TYPE } from '../../../../common/utils/constants';
import { getQueryString, isURL, saveSourceUrlToSessionStorage, setSessionVariable } from '../../../../common/utils';
import logger from '../../../../utils/monitoring/logger';
import { getUserConsumerId, getBusiness, getApiRequestShippingType } from '../app';
import { getUniquePromosAvailableCount } from './api-request';

export const fetchUniquePromosAvailableCount = createAsyncThunk(
  'ordering/common/fetchUniquePromosAvailableCount',
  async (_, { getState }) => {
    const state = getState();
    const consumerId = getUserConsumerId(state);
    const business = getBusiness(state);
    const shippingType = getApiRequestShippingType(state);

    const result = await getUniquePromosAvailableCount(consumerId, {
      business,
      shippingType,
    });

    return result;
  }
);

export const setQuerySourceToSessionStorage = createAsyncThunk('ordering/common/setQuerySource', async () => {
  const source = getQueryString('source');

  if (source) {
    switch (source) {
      case SOURCE_TYPE.SHARED_LINK:
        setSessionVariable('BeepOrderingSource', source);
        break;
      case SOURCE_TYPE.SMS:
      case SOURCE_TYPE.PUSH_NOTIFICATION:
        setSessionVariable('__sr_source', source);
        break;
      case SOURCE_TYPE.SHOPPING_CART:
        // no need to do anything in here, it will be used on the menu page.
        break;
      default:
        if (isURL(source)) {
          saveSourceUrlToSessionStorage(source);
        } else {
          logger.error('Ordering_App_InvalidSource', { source });
        }
    }
  }
});
