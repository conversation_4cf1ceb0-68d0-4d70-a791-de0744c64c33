import { get, post } from '../../../../common/utils/api/request';

export const getOrder = receiptNumber => post('/api/gql/Order', { orderId: receiptNumber });

export const getOrderStatus = receiptNumber => post(`/api/transactions/${receiptNumber}/status`);

export const getPayLaterOrder = receiptNumber => get(`/api/v3/transactions/${receiptNumber}/calculation`);

export const getPayLaterOrderStatus = receiptNumber => get(`/api/v3/transactions/${receiptNumber}/status`);
