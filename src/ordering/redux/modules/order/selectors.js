import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import { createSelector } from 'reselect';
import { getTableId } from '../app';

export const getLoadOrderRequestData = state => state.order.loadOrderRequest.data || {};

export const getLoadOrderRequestStatus = state => state.order.loadOrderRequest.status;

export const getLoadOrderRequestError = state => state.order.loadOrderRequest.error;

export const getOrderStatus = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'status', null)
);

export const getOrderRiderLocations = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'riderLocations', null)
);

export const getOrderDelayReason = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'delayReason', null)
);

export const getOrderShippingType = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'shippingType', null)
);

export const getOrderOriginalShippingType = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'originalShippingType', null)
);

export const getOrderRefundShippingFee = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'refundShippingFee', null)
);

export const getOrderPaymentMethod = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'paymentMethod', '')
);

export const getOrderCancelOperator = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'cancelOperator', null)
);

export const getOrderStoreId = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'storeId', null)
);

export const getOrderTableId = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'tableId', null)
);

export const getOrderCustomerId = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'customerId', null)
);

export const getOrderServiceCharge = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'serviceCharge', 0)
);

export const getOrderProductsManualDiscount = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'productsManualDiscount', 0)
);

export const getIsPayLaterOrder = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'isPayLater', false)
);

export const getOrderTimeoutLookingForRider = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'timeoutLookingForRider', false)
);

export const getIsOrderUseStorehubLogistics = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'deliveryInformation.0.useStorehubLogistics', false)
);

export const getIsPreOrder = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'isPreOrder', false)
);

export const getIsOrderCancellable = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'isCancellable', false)
);

export const getOrderStoreInfo = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'storeInfo', null)
);

export const getOrderItems = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'items', [])
);

export const getOrderFoodCourtId = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'foodCourtId', null)
);

export const getOrderFoodCourtMerchantName = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'foodCourtMerchantName', null)
);

export const getOrderEInvoiceRelatedInfo = createSelector(getLoadOrderRequestData, loadOrderRequestData =>
  _get(loadOrderRequestData, 'eInvoiceRelatedInfo', {})
);

export const getOrderEInvoiceLinkType = createSelector(getOrderEInvoiceRelatedInfo, orderEInvoiceRelatedInfo =>
  _get(orderEInvoiceRelatedInfo, 'linkType', null)
);

export const getOrderEInvoiceEntryLink = createSelector(getOrderEInvoiceRelatedInfo, orderEInvoiceRelatedInfo =>
  _get(orderEInvoiceRelatedInfo, 'link', null)
);

export const getLoadPayLaterOrderRequestData = state => state.order.loadPayLaterOrderRequest.data;

export const getLoadPayLaterOrderRequestStatus = state => state.order.loadPayLaterOrderRequest.status;

export const getLoadPayLaterOrderRequestError = state => state.order.loadPayLaterOrderRequest.error;

export const getPayLaterOrderReceiptNumber = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'receiptNumber', null)
);

export const getPayLaterOrderStatus = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'status', null)
);

export const getPayLaterOrderPickUpCode = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'pickUpCode', null)
);

export const getPayLaterOrderTax = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'tax', 0)
);

export const getPayLaterOrderServiceCharge = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'serviceCharge', 0)
);

export const getPayLaterOrderServiceChargeRate = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'serviceChargeInfo.serviceChargeRate', 0)
);

export const getPayLaterOrderShippingFee = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'shippingFee', 0)
);

export const getPayLaterOrderProductsManualDiscount = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'productsManualDiscount', 0)
);

export const getPayLaterOrderSubtotal = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'subtotal', 0)
);

export const getPayLaterOrderTotal = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'total', 0)
);

export const getIsPayLaterOrderStorePayByCashOnly = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'isStorePayByCashOnly', false)
);

export const getIsPayLaterOrderApplyCashback = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'applyCashback', false)
);

export const getPayLaterOrderModifiedTime = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'modifiedTime', null)
);

export const getPayLaterOrderTableId = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'tableId', null)
);

export const getPayLaterOrderRedirectUrl = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'redirectUrl', null)
);

export const getPayLaterOrderSubOrders = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'subOrders', [])
);

export const getPayLaterOrderItems = createSelector(getLoadPayLaterOrderRequestData, loadPayLaterOrderRequestData =>
  _get(loadPayLaterOrderRequestData, 'items', [])
);

export const getPayLaterOrderDisplayPromotions = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'displayPromotions', [])
);

export const getPayLaterOrderAppliedVoucher = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'appliedVoucher', {})
);

export const getPayLaterOrderAppliedVoucherId = createSelector(getPayLaterOrderAppliedVoucher, appliedVoucher =>
  _get(appliedVoucher, 'voucherId', '')
);

export const getPayLaterOrderAppliedVoucherCode = createSelector(getPayLaterOrderAppliedVoucher, appliedVoucher =>
  _get(appliedVoucher, 'voucherCode', null)
);

export const getPayLaterOrderAppliedVoucherValue = createSelector(getPayLaterOrderAppliedVoucher, appliedVoucher =>
  _get(appliedVoucher, 'value', null)
);

export const getPayLaterOrderLoyaltyDiscounts = createSelector(
  getLoadPayLaterOrderRequestData,
  loadPayLaterOrderRequestData => _get(loadPayLaterOrderRequestData, 'loyaltyDiscounts', [])
);

export const getLoadPayLaterOrderStatusRequestData = state => state.order.loadPayLaterOrderStatusRequest.data;

export const getLoadPayLaterOrderStatusRequestStatus = state => state.order.loadPayLaterOrderStatusRequest.status;

export const getLoadPayLaterOrderStatusRequestError = state => state.order.loadPayLaterOrderStatusRequest.error;

export const getPayLaterOrderStatusInfoData = state => state.order.payLaterOrderStatusInfo.data;

export const getPayLaterOrderStatusTableId = createSelector(
  getPayLaterOrderStatusInfoData,
  payLaterOrderStatusInfoData => payLaterOrderStatusInfoData.tableId
);

export const getHasPayLaterOrderTableIdChanged = createSelector(
  getTableId,
  getPayLaterOrderStatusTableId,
  (prevTableId, currTableId) => {
    const shouldSkipDiffCheck = !prevTableId || !currTableId;

    return !(shouldSkipDiffCheck || _isEqual(prevTableId, currTableId));
  }
);

export const getPayLaterStoreHash = createSelector(
  getPayLaterOrderStatusInfoData,
  payLaterOrderStatusInfoData => payLaterOrderStatusInfoData.storeHash
);
