import dayjs from 'dayjs';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { SHIPPING_TYPES } from '../../../../common/utils/constants';
import logger from '../../../../utils/monitoring/logger';
import { getOrder, getOrderStatus, getPayLaterOrderStatus, getPayLaterOrder } from './api-request';
import { getPayLaterOrderModifiedTime } from './selectors';
import { getIsPayLaterSSEEnabled } from '../app';

export const fetchOrder = createAsyncThunk('ordering/order/fetchOrder', async receiptNumber => {
  try {
    const result = await getOrder(receiptNumber);
    const { data } = result || {};
    const { order } = data || {};

    if (!result || !data || !order) {
      return null;
    }

    const { shippingType } = order;

    if (shippingType === 'dineIn') {
      data.order.shippingType = SHIPPING_TYPES.DINE_IN;
    }

    return data.order;
  } catch (e) {
    logger.error('Ordering_Order_fetchOrderFailed', {
      message: e?.message,
    });

    throw e;
  }
});

export const fetchOrderStatus = createAsyncThunk('ordering/order/fetchOrderStatus', async receiptNumber => {
  try {
    const result = await getOrderStatus(receiptNumber);

    return result;
  } catch (error) {
    logger.error('Ordering_Order_fetchOrderStatusFailed', { message: error?.message || '' });

    throw error;
  }
});

export const fetchPayLaterOrder = createAsyncThunk('ordering/order/fetchPayLaterOrder', async receiptNumber => {
  try {
    const result = await getPayLaterOrder(receiptNumber);

    return result;
  } catch (error) {
    logger.error('Ordering_Order_fetchPayLaterOrderFailed', { message: error?.message || '' });

    throw error;
  }
});

export const fetchPayLaterOrderStatus = createAsyncThunk(
  'ordering/order/fetchPayLaterOrderStatus',
  async receiptNumber => {
    try {
      const result = await getPayLaterOrderStatus(receiptNumber);

      return result;
    } catch (error) {
      logger.error('Ordering_Order_fetchPayLaterOrderStatusFailed', { message: error?.message || '' });

      throw error;
    }
  }
);

export const syncPayLaterOrderAndStatus = createAsyncThunk(
  'ordering/order/syncPayLaterOrderAndStatus',
  async (receiptNumber, { dispatch, getState }) => {
    try {
      const state = getState();
      const isPayLaterSSEEnabled = getIsPayLaterSSEEnabled(state);
      const prevModifiedTime = getPayLaterOrderModifiedTime(state);
      const result = await getPayLaterOrderStatus(receiptNumber);
      const prevModifiedTimeDate = dayjs(prevModifiedTime);
      const modifiedTimeDate = dayjs(result.modifiedTime);

      if (isPayLaterSSEEnabled) {
        await dispatch(fetchPayLaterOrder(receiptNumber));
      } else if (!prevModifiedTime || dayjs(modifiedTimeDate).isAfter(prevModifiedTimeDate, 'second')) {
        await dispatch(fetchPayLaterOrder(receiptNumber));
      }

      return result;
    } catch (error) {
      logger.error('Ordering_Order_loadPayLaterOrderStatusFailed', { message: error?.message || '' });

      throw error;
    }
  }
);

export const updatePayLaterOrderRedirectUrl = createAsyncThunk(
  'ordering/order/updatePayLaterOrderRedirectUrl',
  async redirectUrl => redirectUrl
);
