import { PATH_NAME_MAPPING, PAGE_ROUTES } from '../../../../common/utils/constants';

export const CART_SUBMISSION_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
};

export const CART_SUBMISSION_ERROR_CODES = {
  ORDER_CREATION_LIMITED: '393765',
  STORE_ID_IS_REQUIRED: '393474',
  STORE_OUT_OF_STOCK: '393478',
  CART_HAS_INVALID_BIR_ITEM: '393479',
};

export const CART_SUBMISSION_ERROR_ROUTES = {
  [CART_SUBMISSION_ERROR_CODES.STORE_OUT_OF_STOCK]: PAGE_ROUTES.CART,
  [CART_SUBMISSION_ERROR_CODES.CART_HAS_INVALID_BIR_ITEM]: PAGE_ROUTES.CART,
};

export const CART_STATUS = {
  CREATED: 0,
  FAILED: 1,
  COMPLETED: 2,
};

export const AVAILABLE_QUERY_CART_STATUS_ROUTES = [
  PATH_NAME_MAPPING.ORDERING_BASE,
  `${PATH_NAME_MAPPING.ORDERING_BASE}/`,
  `${PATH_NAME_MAPPING.ORDERING_BASE}${PATH_NAME_MAPPING.ORDERING_CART}`,
  `${PATH_NAME_MAPPING.ORDERING_BASE}${PATH_NAME_MAPPING.ORDERING_CART}/`,
];

export const AVAILABLE_QUERY_CART_PAGES = {
  MENU: 'menu',
  CART: 'cart',
};

/**
 * i18n
 */
export const CART_SUBMISSION_ERROR_I18N_KEYS = {
  [CART_SUBMISSION_ERROR_CODES.ORDER_CREATION_LIMITED]: {
    titleI18nKey: 'ErrorOrderCreationLimitedTitle',
    descriptionI18nKey: 'ErrorOrderCreationLimitedDescription',
  },
  [CART_SUBMISSION_ERROR_CODES.STORE_ID_IS_REQUIRED]: {
    titleI18nKey: 'ErrorStoreIdIsRequiredTitle',
    descriptionI18nKey: 'ErrorStoreIdIsRequiredDescription',
  },
  [CART_SUBMISSION_ERROR_CODES.STORE_OUT_OF_STOCK]: {
    titleI18nKey: 'ErrorStoreOutOfStockTitle',
    descriptionI18nKey: 'ErrorStoreOutOfStockDescription',
  },
  [CART_SUBMISSION_ERROR_CODES.CART_HAS_INVALID_BIR_ITEM]: {
    titleI18nKey: 'ErrorCartHasInvalidBirItemTitle',
    descriptionI18nKey: 'ErrorCartHasInvalidBirItemDescription',
  },
};
// end of i18n
