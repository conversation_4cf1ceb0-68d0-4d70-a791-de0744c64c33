import { store as orderingStore } from '../../ordering/redux/store';
import { store as rewardsStore } from '../../rewards/redux/store';
import { initGrowthBookInstance, updateFeatureFlagResults } from '../../redux/modules/growthbook/thunks';
import GrowthBook from '.';

const stores = [orderingStore, rewardsStore];

const dispatchInitGrowthBookInstanceThunk = () => {
  stores.forEach(store => store.dispatch(initGrowthBookInstance()));
};

const dispatchUpdateFeatureFlagResultsThunk = () => {
  stores.forEach(store => store.dispatch(updateFeatureFlagResults()));
};

const onFeatureChange = () => {
  dispatchUpdateFeatureFlagResultsThunk();
};

GrowthBook.getInstance();

// Load features on page load
dispatchInitGrowthBookInstanceThunk();

// Re-render when features change
GrowthBook.setRenderer(onFeatureChange);
