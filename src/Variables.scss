$font-size-base: 14px;
$mobile-font-size-base: 12px;

$theme-color: #ff9419;
$theme-color-dark: #de8115;
$theme-color-translucent: rgba(255, 148, 25, 0.5);
$theme-color-translucent-light: rgba(255, 148, 25, 0.25);
$theme-blue: #31728d;
$main-color: #303030;
$white: #ffffff;
$black: #000000;
$half-black: rgba(0, 0, 0, 0.45);
$gray-0: #1d1d1d;
$gray-1: #25272c;
$gray-2: #8d90a1;
$gray-3: #9e9e9e;
$gray-4: #dededf;
$gray-5: #efefef;
$gray-6: #fbfbfb;

$new-grey-1-1: #8f9092;

/* status color */
$status-gray: #808fa4;
$status-blue: #00b0ff;
$status-green: #4caf50;
$status-red: #fa4133;
$status-orange: #ff9c20;
$status-yellow: #ffd100;
$status-yellow-1: #fff2e3;

/* special colors */
$add-button-active: #de8115;
$minus-button-active: #4f515f;
$logo-default-title: #ab8d3f;

$line-height-base: 1.4em;
$line-height-higher: 1.6em;
$font-weight-light: 300;
$font-weight-bold: 500;
$font-weight-bolder: 600;
$border-radius-base: 4px;
$border-radius-large: 8px;
$z-index-base: 100;
$z-index-high: 200;
$z-index-higher: 300;

/* new theme color */
$basic-theme-basic: #ff9419;
$basic-theme-dark: #fc7118;
$basic-theme-light: #fec788;
$basic-text-basic: #303030;
$basic-text-light: #9e9e9e;
$basic-text-lighter: #d1d1d1;
$basic-white: #ffffff;
$basic-grey-1: #f9fafb; //(Background colour)
$basic-grey-2: #eff2f3; //(search fill + divider)
$basic-grey-3: #dededf; //(bigger divider)
$basic-grey-4: #d1d1d1; //(New Placeholder Text Grey)
$basic-grey-5: #c6c6c6; //(disabled background)
$basic-grey-6: #9e9e9e;
$basic-grey-7: #303030; //(Font Black)
$basic-grey-8: #1d1d1d; //(Black)
$basic-link: #00b0ff;
$basic-dark-link: #0089c7; //link hover
$basic-light-link: #ecf9ff; //link active / press down
$basic-lighter-link: #9ee1ff; //link visited / press up

// error colors
$status-error-basic: #e15343;
$status-error-dark: #c04537;
$status-error-light: #e0aaa4;

// success colors
$status-success-basic: #36a93f;
$status-success-dark: #02814e;
$status-success-light: #dffde2;

// info colors
$status-info-basic: #57b5e3;
$status-info-dark: #0295dc;
$status-info-light: #9ee1ff;

// warning colors
$status-warning-basic: #ffcc00;
$status-warning-dark: #f0b917;
$status-warning-light: #ffd880;

// primary colors
$status-primary-basic: #fc7118;
$status-primary-dark: #d15607;
$status-primary-light: #ff9d5e;
