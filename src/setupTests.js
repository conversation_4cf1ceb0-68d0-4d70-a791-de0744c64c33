/**
 * enzyme document: https://airbnb.io/enzyme/
 * Adapter configuration: https://airbnb.io/enzyme/docs/installation/react-16.html
 */
import { TextEncoder, TextDecoder } from 'util';
// eslint-disable-next-line import/first
import { configure } from 'enzyme';
// eslint-disable-next-line import/first
import Adapter from 'enzyme-adapter-react-16';
// eslint-disable-next-line import/first
import '@testing-library/jest-dom/extend-expect';
require('jest-fetch-mock').enableMocks();

global.fetch = require('jest-fetch-mock');
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

if (!AbortSignal.prototype.throwIfAborted) {
  AbortSignal.prototype.throwIfAborted = function() {
    if (this.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
  };
}

// yarn test:coverage will response `ReferenceError: React is not defined`
// solutions refer: https://github.com/aelbore/esbuild-jest/issues/61#issuecomment-1061011148
configure({ adapter: new Adapter() });
