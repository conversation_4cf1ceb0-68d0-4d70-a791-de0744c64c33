@use "../Variables" as *;

.location-picker {
  &__container {
    border-top: 8px solid $gray-5;
  }

  &__detected-position {
    overflow: hidden;
  }

  &__button-detected-position {
    height: 50px;
  }

  &__detected-position-address,
  &__historical-container {
    width: 85%;
  }

  &__address-summary {
    list-style: none;
  }

  &__address-summary,
  &__address-title,
  &__address-detail,
  &__address-detail-text,
  &__result-item {
    width: 100%;
  }

  &__result-item > &__address-summary {
    padding-left: 3.8vw;
    padding-right: 3.8vw;
    border-bottom: 1px solid $gray-4;
  }

  &__detected-position-address {
    overflow: hidden;
  }

  &__historical-address {
    overflow: hidden;
  }
}

@media (min-width: 420px) {
  .location-picker {
    &__result-item > &__address-summary {
      padding-left: 8px * 2;
      padding-right: 8px * 2;
    }
  }
}
