@use "../Variables" as *;

.switch-button {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 16px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  input:checked + .switch-button__slider {
    background-color: $theme-color;
  }

  &__slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $status-gray;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  &__slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: -2px;
    bottom: -2px;
    background-color: $white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  }

  input:checked + &__slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
  }

  /* Rounded sliders */
  &__slider {
    border-radius: 16px;
  }

  &__slider:before {
    border-radius: 50%;
  }
}
