@use "../Variables" as *;

.otp-modal {
  top: 0;
  height: 100vh;
  background-color: $white;
  z-index: $z-index-base;

  &__header {
    background-color: $white;
    border-bottom-color: $white;
  }

  &__content {
    display: inline-block;
  }

  &__image-container {
    margin-left: auto;
    margin-right: auto;
    max-width: 50%;
  }

  &__input-group {
    margin: 2rem 0;
  }

  &__form-group {
    display: inline-block;
    overflow: hidden;
  }

  &__form-group--error {
    border: 1px solid $status-error-basic;
  }

  &__input {
    height: 56px;
    width: 7.6em;
    line-height: 42px;
    letter-spacing: 0.8em;
    text-indent: 0.8em;
  }

  &__failed-otp {
    color: $status-error-basic;
  }

  &__resend-tip {
    color: $gray-3;
  }

  &__button-resend-sms {
    border: 1px solid $black;
    width: 220px;
  }

  &__button-resend-whats {
    color: $status-blue;
  }

  &__loading {
    background-color: $gray-3;
  }
}

@keyframes circle-loader-animation {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

.circle-loader {
  display: inline-block;
  width: 1.8em;
  height: 1.8em;
  border-radius: 50%;
  border: 2px solid $gray-3;
  animation: circle-loader-animation 1s linear infinite;
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.prompt-loader {
  position: relative;
  min-width: 80px;
  color: $gray-2;
  background-color: $gray-6;
  z-index: $z-index-base * 2;

  &::before {
    content: "";
    display: block;
    width: 100%;
    padding: 50% 0;
  }

  &__content {
    position: absolute;
  }

  &__text {
    display: block;
  }
}

.page-loader {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;

  .prompt-loader {
    transform: translateY(-90%);
  }
}
