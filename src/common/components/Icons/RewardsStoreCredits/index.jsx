import React from 'react';
import PropTypes from 'prop-types';

export const RewardsStoreCredits = ({ className, color }) => (
  <i className={className}>
    <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M34.7202 3.4436L35.2645 6.07282C35.3779 6.6215 35.7972 7.05564 36.3415 7.18872L38.4384 7.70104L36.3415 8.21335C35.7972 8.34643 35.3783 8.78057 35.2645 9.32925L34.7202 11.9585L34.1758 9.32925C34.0624 8.78057 33.6432 8.34643 33.0989 8.21335L31.002 7.70104L33.0989 7.18872C33.6432 7.05564 34.062 6.6215 34.1758 6.07282L34.7202 3.4436Z"
        fill={color}
      />
      <path
        d="M7.18586 29.1677L7.63528 31.3392C7.72909 31.7922 8.07524 32.1507 8.52465 32.2605L10.2565 32.6838L8.52465 33.107C8.07524 33.2168 7.72909 33.5753 7.63528 34.0284L7.18586 36.1998L6.73645 34.0284C6.64264 33.5753 6.29649 33.2168 5.84708 33.107L4.11523 32.6838L5.84708 32.2605C6.29649 32.1507 6.64264 31.7922 6.73645 31.3392L7.18586 29.1677Z"
        fill={color}
      />
      <path
        d="M10.1451 5.80811C9.58369 5.80811 9.5826 6.68075 10.1451 6.68075C10.7076 6.68075 10.7076 5.80811 10.1451 5.80811Z"
        fill={color}
      />
      <path
        d="M15.3453 34.8262C14.7839 34.8262 14.7828 35.6988 15.3453 35.6988C15.9078 35.6988 15.9078 34.8262 15.3453 34.8262Z"
        fill={color}
      />
      <path
        d="M27.2468 3.7478C26.8447 3.7478 26.4596 4.10123 26.4782 4.51646C26.4967 4.93279 26.816 5.28512 27.2468 5.28512C27.649 5.28512 28.034 4.9317 28.0155 4.51646C27.997 4.10013 27.6777 3.7478 27.2468 3.7478Z"
        fill={color}
      />
      <path
        d="M33.3954 31.4841C33.0816 31.4841 32.7809 31.7601 32.7954 32.0841C32.81 32.4091 33.059 32.684 33.3954 32.684C33.7092 32.684 34.0099 32.408 33.9953 32.0841C33.9808 31.759 33.7317 31.4841 33.3954 31.4841Z"
        fill={color}
      />
      <path
        d="M4.16026 15.1353C3.67449 15.1353 3.20871 15.5625 3.23126 16.0643C3.2538 16.5675 3.63922 16.9933 4.16026 16.9933C4.64604 16.9933 5.11145 16.566 5.08927 16.0643C5.06672 15.561 4.68094 15.1353 4.16026 15.1353Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.9217 28.7453L32.1762 27.3525C33.4008 27.2754 34.3255 26.2619 34.2451 25.0913L33.7805 18.2675L7.09721 19.9364L7.56203 26.7628C7.64244 27.9333 8.69702 28.8224 9.9217 28.7453ZM6.87792 16.7159L6.75486 14.9087C6.67445 13.7351 7.60223 12.7246 8.82382 12.6475L31.0783 11.2547C32.303 11.1776 33.3575 12.0667 33.438 13.2372L33.5612 15.047L6.87792 16.7159ZM31.5941 25.6571L28.7891 25.832C28.4149 25.8557 28.0902 25.583 28.0655 25.2245L28.0562 25.0704C28.0314 24.7118 28.316 24.4006 28.6902 24.3769L31.4952 24.202C31.8694 24.1783 32.1941 24.451 32.2188 24.8096L32.2281 24.9637C32.2529 25.3223 31.9683 25.6334 31.5941 25.6571Z"
        fill={color}
      />
    </svg>
  </i>
);

RewardsStoreCredits.propTypes = {
  className: PropTypes.string,
  color: PropTypes.string,
};
RewardsStoreCredits.defaultProps = {
  className: null,
  color: 'white',
};

RewardsStoreCredits.displayName = 'RewardsStoreCredits';
