import React from 'react';
import PropTypes from 'prop-types';

export const RewardsPointDiscount = ({ className, color }) => (
  <i className={className}>
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M33.9542 21.8662C34.6506 21.2817 35.3561 20.6897 35.3561 20.0001C35.3561 19.3105 34.6506 18.7185 33.9542 18.134C33.365 17.6397 32.7824 17.1508 32.6386 16.6128C32.4871 16.0462 32.7548 15.3058 33.0211 14.569C33.3258 13.7259 33.6288 12.8876 33.3003 12.3201C32.9651 11.7407 32.0746 11.5832 31.1861 11.4262C30.4197 11.2907 29.6549 11.1555 29.2494 10.75C28.8437 10.3445 28.7084 9.5794 28.5729 8.81286C28.4159 7.92455 28.2585 7.03425 27.6794 6.69912C27.1119 6.37063 26.2736 6.67363 25.4305 6.97836C24.6937 7.24468 23.9532 7.51231 23.3867 7.36088C22.8487 7.21709 22.3598 6.63442 21.8654 6.0453C21.281 5.34882 20.689 4.64331 19.9994 4.64331C19.3097 4.64331 18.7177 5.34882 18.1333 6.0453C17.639 6.63442 17.1501 7.21709 16.612 7.36088C16.0455 7.51231 15.305 7.24468 14.5682 6.97836C13.7252 6.67363 12.8869 6.37063 12.3193 6.69912C11.7399 7.03432 11.5825 7.92484 11.4254 8.81331C11.29 9.57962 11.1548 10.3444 10.7493 10.75C10.3437 11.1558 9.57867 11.291 8.81213 11.4265C7.92382 11.5836 7.03351 11.741 6.69839 12.3201C6.3699 12.8876 6.6729 13.7259 6.97763 14.569C7.24394 15.3058 7.51158 16.0462 7.36015 16.6128C7.21636 17.1508 6.63369 17.6397 6.04457 18.134C5.34808 18.7185 4.64258 19.3105 4.64258 20.0001C4.64258 20.6841 5.33339 21.2602 6.02304 21.8354C6.61691 22.3308 7.20991 22.8253 7.36015 23.3874C7.51106 23.952 7.2439 24.6859 6.97736 25.4182C6.67039 26.2615 6.36424 27.1025 6.69839 27.6801C7.03359 28.2595 7.92411 28.417 8.81258 28.574C9.57888 28.7095 10.3437 28.8447 10.7493 29.2502C11.155 29.6557 11.2903 30.4208 11.4258 31.1873C11.5828 32.0756 11.7402 32.9659 12.3193 33.3011C12.8916 33.6323 13.751 33.3246 14.6058 33.0186C15.3395 32.7558 16.0697 32.4944 16.612 32.6393C17.1501 32.7831 17.639 33.3658 18.1333 33.9549C18.7177 34.6514 19.3097 35.3569 19.9994 35.3569C20.689 35.3569 21.281 34.6514 21.8654 33.9549C22.3598 33.3658 22.8487 32.7831 23.3867 32.6393C23.9532 32.4879 24.6937 32.7555 25.4305 33.0218C26.2736 33.3266 27.1119 33.6296 27.6794 33.3011C28.2588 32.9659 28.4162 32.0753 28.5733 31.1868C28.7088 30.4204 28.844 29.6556 29.2494 29.2502C29.655 28.8444 30.4201 28.7092 31.1866 28.5737C32.0749 28.4166 32.9652 28.2592 33.3003 27.6801C33.6288 27.1126 33.3258 26.2743 33.0211 25.4312C32.7548 24.6944 32.4871 23.954 32.6386 23.3874C32.7824 22.8494 33.365 22.3605 33.9542 21.8662ZM12.5573 17.6264C12.6202 18.3798 12.9246 18.9797 13.4711 19.4255H13.4704C14.0168 19.8713 14.7295 20.0942 15.6087 20.0942C16.4879 20.0942 17.2006 19.8713 17.7471 19.4255C18.2936 18.9797 18.5979 18.3798 18.6608 17.6264C18.6859 17.2497 18.6986 16.9796 18.6986 16.8163C18.6986 16.6407 18.6859 16.3014 18.6608 15.7989C18.6106 15.0328 18.3186 14.4267 17.7849 13.9809C17.2511 13.5355 16.5261 13.3123 15.6091 13.3123C14.6921 13.3123 13.967 13.5351 13.4333 13.9809C12.8995 14.4271 12.6075 15.0332 12.5573 15.7989C12.5319 16.3014 12.5195 16.6407 12.5195 16.8163C12.5195 16.9796 12.5319 17.2497 12.5573 17.6264ZM13.7252 26.2169C13.7252 26.33 13.766 26.4212 13.8478 26.49C13.9296 26.5591 14.039 26.5936 14.1776 26.5936H16.1367C16.2996 26.5936 16.4286 26.5652 16.5228 26.5089C16.617 26.4525 16.7141 26.3616 16.8148 26.2358L26.1212 13.9907C26.171 13.9278 26.1965 13.8588 26.1965 13.7835C26.1965 13.6704 26.1554 13.5795 26.0739 13.5104C25.9921 13.4417 25.8823 13.4068 25.7441 13.4068H23.7851C23.6218 13.4068 23.4931 13.4352 23.3989 13.4915C23.3047 13.5479 23.2073 13.6391 23.1069 13.7646L13.8005 26.0096C13.7503 26.0726 13.7252 26.1416 13.7252 26.2169ZM16.1181 16.7032C16.1181 16.8792 16.1054 17.1428 16.0803 17.4944C16.0552 17.8711 15.8985 18.0595 15.6094 18.0595C15.3204 18.0595 15.1637 17.8711 15.1386 17.4944C15.1258 17.3188 15.1197 17.0672 15.1197 16.7032C15.1197 16.3393 15.1258 16.0815 15.1386 15.9309C15.1637 15.5542 15.3207 15.3659 15.6094 15.3659C15.8981 15.3659 16.0548 15.5542 16.0803 15.9309C16.1054 16.2324 16.1181 16.4898 16.1181 16.7032ZM21.3362 24.22C21.3991 24.9734 21.7038 25.5733 22.2499 26.0191H22.2492C22.7957 26.4649 23.5084 26.6878 24.3875 26.6878C25.2667 26.6878 25.9794 26.4649 26.5259 26.0191C27.0724 25.5733 27.3767 24.9734 27.4396 24.22C27.4647 23.8433 27.4774 23.5731 27.4774 23.4099C27.4774 23.2339 27.4647 22.895 27.4396 22.3925C27.3895 21.6264 27.0975 21.0203 26.5637 20.5745C26.0299 20.1291 25.3049 19.9058 24.3879 19.9058C23.4709 19.9058 22.7459 20.1287 22.2121 20.5745C21.6783 21.0206 21.3864 21.6268 21.3362 22.3925C21.3107 22.895 21.2984 23.2343 21.2984 23.4099C21.2984 23.5731 21.3107 23.8433 21.3362 24.22ZM24.897 23.2968C24.897 23.4728 24.8842 23.7364 24.8591 24.088C24.834 24.4647 24.6773 24.653 24.3883 24.653C24.0992 24.653 23.9425 24.4647 23.9174 24.088C23.9047 23.9124 23.8985 23.6608 23.8985 23.2968C23.8985 22.9328 23.9047 22.6754 23.9174 22.5245C23.9421 22.1478 24.0996 21.9595 24.3883 21.9595C24.677 21.9595 24.8337 22.1478 24.8591 22.5245C24.8842 22.8259 24.897 23.0834 24.897 23.2968Z"
        fill={color}
      />
      <path
        d="M34.2191 3.44366L34.7634 6.07288C34.8768 6.62156 35.2961 7.0557 35.8404 7.18878L37.9373 7.7011L35.8404 8.21341C35.2961 8.34649 34.8772 8.78063 34.7634 9.32931L34.2191 11.9585L33.6747 9.32931C33.5613 8.78063 33.1421 8.34649 32.5978 8.21341L30.5009 7.7011L32.5978 7.18878C33.1421 7.0557 33.5609 6.62156 33.6747 6.07288L34.2191 3.44366Z"
        fill={color}
      />
      <path
        d="M6.68586 29.168L7.13528 31.3394C7.22909 31.7925 7.57524 32.151 8.02465 32.2608L9.75649 32.684L8.02465 33.1072C7.57524 33.2171 7.22909 33.5756 7.13528 34.0286L6.68586 36.2001L6.23645 34.0286C6.14264 33.5756 5.79649 33.2171 5.34708 33.1072L3.61523 32.684L5.34708 32.2608C5.79649 32.151 6.14264 31.7925 6.23645 31.3394L6.68586 29.168Z"
        fill={color}
      />
      <path
        d="M9.64412 5.80811C9.08271 5.80811 9.08162 6.68075 9.64412 6.68075C10.2066 6.68075 10.2066 5.80811 9.64412 5.80811Z"
        fill={color}
      />
      <path
        d="M14.8453 34.8262C14.2839 34.8262 14.2828 35.6988 14.8453 35.6988C15.4078 35.6988 15.4078 34.8262 14.8453 34.8262Z"
        fill={color}
      />
      <path
        d="M26.7459 3.74805C26.3437 3.74805 25.9587 4.10147 25.9772 4.5167C25.9958 4.93303 26.315 5.28536 26.7459 5.28536C27.148 5.28536 27.5331 4.93194 27.5145 4.5167C27.496 4.10038 27.1767 3.74805 26.7459 3.74805Z"
        fill={color}
      />
      <path
        d="M32.8945 31.4841C32.5807 31.4841 32.28 31.76 32.2946 32.084C32.3091 32.4091 32.5582 32.684 32.8945 32.684C33.2083 32.684 33.509 32.408 33.4945 32.084C33.4799 31.759 33.2309 31.4841 32.8945 31.4841Z"
        fill={color}
      />
      <path
        d="M3.66002 15.1354C3.17424 15.1354 2.70847 15.5626 2.73101 16.0644C2.75355 16.5676 3.13897 16.9934 3.66002 16.9934C4.14579 16.9934 4.6112 16.5662 4.58902 16.0644C4.56648 15.5612 4.1807 15.1354 3.66002 15.1354Z"
        fill={color}
      />
    </svg>
  </i>
);

RewardsPointDiscount.propTypes = {
  className: PropTypes.string,
  color: PropTypes.string,
};
RewardsPointDiscount.defaultProps = {
  className: null,
  color: 'white',
};

RewardsPointDiscount.displayName = 'RewardsPointDiscount';
