import React from 'react';
import PropTypes from 'prop-types';

export const LocationAndAddressIcon = ({ className }) => (
  <i className={className}>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z"
        fill="#F2F2F3"
      />
      <path
        d="M9.52283 5.66112C11.079 4.76517 13.006 4.78087 14.5471 5.70207C16.0785 6.64239 17.0085 8.32112 16.9999 10.1248C16.9642 11.9179 15.9663 13.6044 14.7192 14.9076C14.0013 15.6617 13.1963 16.3299 12.3207 16.8983C12.2307 16.9498 12.1319 16.9843 12.0292 17C11.9307 16.9956 11.8349 16.9667 11.7507 16.916C10.4122 16.0606 9.24127 14.9732 8.29427 13.7061C7.49713 12.6402 7.04584 11.3605 7 10.0359C6.99929 8.22723 7.96213 6.55697 9.52283 5.66112ZM10.4242 10.7849C10.6807 11.4204 11.3019 11.8386 11.9942 11.8417C12.4462 11.8442 12.8799 11.6657 13.1964 11.3468C13.5149 11.0263 13.6935 10.5914 13.6921 10.1375C13.6974 9.4503 13.2861 8.82663 12.6478 8.55408C12.0126 8.28777 11.2772 8.43348 10.795 8.92117C10.3073 9.41422 10.1617 10.1461 10.4242 10.7849Z"
        fill="#FF9419"
      />
      <path
        d="M8.45713 18.68C8.45713 18.9372 9.15144 19.1749 10.2785 19.3035C11.4056 19.4322 12.7943 19.4322 13.9213 19.3035C15.0484 19.1749 15.7428 18.9372 15.7428 18.68C15.7428 18.4227 15.0484 18.185 13.9213 18.0564C12.7943 17.9278 11.4056 17.9278 10.2785 18.0564C9.15144 18.185 8.45713 18.4227 8.45713 18.68Z"
        fill="#303030"
      />
    </svg>
  </i>
);

LocationAndAddressIcon.propTypes = {
  className: PropTypes.string,
};
LocationAndAddressIcon.defaultProps = {
  className: null,
};

LocationAndAddressIcon.displayName = 'LocationAndAddressIcon';
