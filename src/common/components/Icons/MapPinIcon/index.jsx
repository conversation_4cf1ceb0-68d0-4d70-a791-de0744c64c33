import React from 'react';
import PropTypes from 'prop-types';

export const MapPinIcon = ({ className }) => (
  <i className={className}>
    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
      <path d="M2 14c0 6.627 5.373 12 12 12s12-5.373 12-12S20.627 2 14 2 2 7.373 2 14z" fill="#f2f2f3" />
      <path
        d="M11.573 7.674a5.07 5.07 0 0 1 5.125.042c1.562.959 2.511 2.671 2.502 4.511-.036 1.829-1.054 3.549-2.326 4.878-.732.769-1.553 1.451-2.447 2.03a.86.86 0 0 1-.297.104c-.101-.004-.198-.034-.284-.086-1.365-.872-2.56-1.982-3.526-3.274A6.63 6.63 0 0 1 9 12.136a5.14 5.14 0 0 1 2.573-4.462zm.919 5.226c.262.648.895 1.075 1.601 1.078.461.003.903-.18 1.226-.505a1.74 1.74 0 0 0 .506-1.233c.005-.701-.414-1.337-1.065-1.615-.648-.272-1.398-.123-1.89.374-.497.503-.646 1.249-.378 1.901h0z"
        fill="#ff9419"
      />
      <path
        d="M10.457 20.68c0 .257.694.495 1.821.624s2.516.129 3.643 0 1.821-.366 1.821-.624-.694-.495-1.821-.624-2.516-.129-3.643 0-1.821.366-1.821.624h0z"
        fill="#303030"
      />
    </svg>
  </i>
);

MapPinIcon.propTypes = {
  className: PropTypes.string,
};
MapPinIcon.defaultProps = {
  className: null,
};

MapPinIcon.displayName = 'MapPinIcon';
