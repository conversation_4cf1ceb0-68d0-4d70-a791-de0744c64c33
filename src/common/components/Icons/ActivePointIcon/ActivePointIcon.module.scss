@use "../../../styles/animates" as *;

.ActivePointIcon {
  @apply tw-relative tw-inline-block tw-bg-yellow-dark tw-rounded-full;

  margin: 3px;
  width: 10px;
  height: 10px;

  transform: translateX(0%);
  -webkit-transform: translateX(0%);
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);

  &::after {
    @apply tw-inline-block tw-bg-yellow-dark tw-rounded-full;

    content: "";
    position: absolute;
    left: -3px;
    top: -3px;
    width: 16px;
    height: 16px;
    opacity: 0.3;
    animation-duration: 3s;

    @extend .beep-animated;
    @extend .beep-animated--infinite;
    @extend .beep-scale-to-disappearance;
  }
}
