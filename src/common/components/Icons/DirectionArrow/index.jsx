import React from 'react';
import PropTypes from 'prop-types';

export const DirectionArrow = ({ className }) => (
  <i className={className}>
    <svg width="25" height="30" viewBox="0 0 25 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0.300781 0.530075C1.53066 -0.274784 2.76054 -0.0206187 3.86319 0.36063C5.38993 0.911323 6.87426 1.63146
8.23136 2.47868C14.3807 6.54534 18.0704 12.3488 19.5971 19.5078C19.9788 21.3293 20.106 23.2356 20.3605 25.3113C22.0569 24.337 22.9475 22.4307 24.6438 21.3293C24.8559 22.1765 24.4318 22.7272 24.0925 23.1932C22.693 25.0995 21.2511 27.0057 19.7668 28.8696C19.0458 29.7592 18.4945 29.8439 17.7311 29.0814C16.1195 27.514 15.1441 25.6078 14.8472 23.405C14.8472 23.3203 14.9745 23.1932 15.1441 22.9814C16.9677 23.4474 16.5436 25.7772 18.1128 26.6244C18.7913 23.7015 18.2824 20.9057 17.519 18.1946C16.7133 15.3564 15.653 12.6453 13.9566 10.2307C12.3027 7.85853 10.2246 5.95228 7.93449 4.17312C5.68679 2.3516 3.14222 1.20785 0.300781 0.530075Z"
        fill="white"
      />
    </svg>
  </i>
);

DirectionArrow.propTypes = {
  className: PropTypes.string,
};
DirectionArrow.defaultProps = {
  className: null,
};

DirectionArrow.displayName = 'DirectionArrow';
