import React from 'react';
import PropTypes from 'prop-types';

export const MotorcycleIcon = ({ className }) => (
  <i className={`${className} tw-flex tw-items-center`}>
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.311 5.174a.376.376 0 0 1 .503.119l.01.015 3.5 5.982a.373.373 0 0 1-.135.51.376.376 0 0 1-.503-.118l-.01-.016-3.5-5.98a.373.373 0 0 1 .135-.512z"
        fill="#9E9E9E"
      />
      <path
        d="M14 8.612a2.87 2.87 0 0 0-2.875 2.866A2.87 2.87 0 0 0 14 14.344a2.87 2.87 0 0 0 2.875-2.866A2.87 2.87 0 0 0 14 8.612zm0 .748c1.174 0 2.125.948 2.125 2.118s-.951 2.118-2.125 2.118a2.122 2.122 0 0 1-2.125-2.118c0-1.17.951-2.118 2.125-2.118zM4 8.612a2.87 2.87 0 0 0-2.875 2.866A2.87 2.87 0 0 0 4 14.344a2.87 2.87 0 0 0 2.875-2.866A2.87 2.87 0 0 0 4 8.612zm0 .748c1.174 0 2.125.948 2.125 2.118S5.174 13.596 4 13.596a2.122 2.122 0 0 1-2.125-2.118c0-1.17.951-2.118 2.125-2.118zM4.413 6.848c.376 0 .637.023.99.117l.019.005.04.012.045.013.047.015.05.016.052.019.056.02.09.032.063.024.136.053.148.06.12.049.17.071a.373.373 0 1 1-.294.688l-.12-.05-.15-.062-.104-.042-.128-.05-.088-.034-.08-.03-.05-.018-.068-.024-.061-.02-.037-.011-.033-.01a2.784 2.784 0 0 0-.812-.096c-.203 0-.419.028-.68.094l-.028.007-.048.014-.037.01-.06.02-.045.015-.072.024-.08.028-.116.042-.13.048-.068.026a.373.373 0 1 1-.266-.699l.114-.043.107-.04.1-.035.092-.033.058-.02.08-.026.05-.016.046-.014.044-.013.04-.011.037-.01a3.5 3.5 0 0 1 .861-.115z"
        fill="#9E9E9E"
      />
      <path
        d="M8.042 11.146c.207 0 .375.251.375.562 0 .302-.159.548-.358.562H3.958c-.207 0-.375-.251-.375-.562 0-.302.159-.548.358-.562h4.101z"
        fill="#9E9E9E"
      />
      <path
        d="M11.446 7.637a.376.376 0 0 1 .53.003.373.373 0 0 1 .011.514l-.013.014-3.669 3.617a.376.376 0 0 1-.53-.003.373.373 0 0 1-.01-.514l.013-.014 3.668-3.617zM6.009 7.432a.376.376 0 0 1 .529-.037.373.373 0 0 1 .049.512l-.012.015-1.25 1.434a.376.376 0 0 1-.53.036.373.373 0 0 1-.049-.512l.013-.015 1.25-1.433z"
        fill="#9E9E9E"
      />
      <path
        d="m6.161 7.303 1.334.017c.207.003.377.244.38.539.003.286-.154.518-.353.528h-.017L6.17 8.37c-.207-.002-.377-.243-.38-.538-.003-.286.154-.518.353-.529h.017zM10.5 5.123c.207 0 .375.252.375.563 0 .301-.158.548-.357.561l-.018.001H9.333c-.207 0-.375-.252-.375-.562 0-.302.159-.549.358-.562l.017-.001H10.5z"
        fill="#9E9E9E"
      />
      <path
        d="M10.833 4.5h.834c.092 0 .166.074.166.166v.997a.166.166 0 0 1-.166.166h-.834a.666.666 0 1 1 0-1.329zM11.083 6.394a.374.374 0 0 1 .018.747H9.118c-.445 0-.886.248-1.331.776a.376.376 0 0 1-.528.046.373.373 0 0 1-.047-.527c.567-.672 1.192-1.03 1.869-1.042H11.083z"
        fill="#9E9E9E"
      />
    </svg>
  </i>
);

MotorcycleIcon.propTypes = {
  className: PropTypes.string,
};
MotorcycleIcon.defaultProps = {
  className: null,
};

MotorcycleIcon.displayName = 'MotorcycleIcon';
