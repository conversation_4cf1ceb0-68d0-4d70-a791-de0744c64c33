import React from 'react';
import PropTypes from 'prop-types';

export const StoreIcon = ({ className }) => (
  <i className={className}>
    <svg width="24" height="25" xmlns="http://www.w3.org/2000/svg">
      <g fillRule="nonzero" fill="none">
        <path
          d="M0 12.02c0 6.639 5.373 12.02 12 12.02s12-5.381 12-12.02C24 5.384 18.627.003 12 .003S0 5.382 0 12.02z"
          fill="#F2F2F3"
        />
        <path
          d="M10.236 10.369c.532 0 1.009-.553 1.333-1.055.19-.296.672-.296.862 0 .324.502.8 1.055 1.333 1.055.531 0 1.009-.553 1.333-1.055.19-.296.672-.296.862 0 .324.502.8 1.055 1.333 1.055.71 0 1.352-.448 1.627-1.137.178-.448.035-.946-.162-1.385l-.633-1.417c-.388-.867-1.218-1.42-2.129-1.42h-7.99c-.911 0-1.74.553-2.129 1.42l-.633 1.416c-.197.44-.34.937-.162 1.385.275.69.916 1.138 1.627 1.138.532 0 1.01-.553 1.333-1.055.19-.296.673-.296.862 0 .324.502.8 1.055 1.333 1.055z"
          fill="#303030"
        />
        <path
          d="M6.12 12.842v-1.705c.66.09 1.326-.104 1.847-.539a.507.507 0 0 1 .645 0c.42.35.954.56 1.532.56.58 0 1.113-.21 1.534-.56a.507.507 0 0 1 .644 0c.421.35.954.56 1.534.56.578 0 1.111-.21 1.532-.56a.507.507 0 0 1 .645 0c.521.435 1.187.63 1.847.54v1.704c0 2.711 0 4.066-.712 4.979-.13.167-.276.32-.434.457-.613.528-1.436.684-2.774.73v-3.28c0-1.138-.878-2.061-1.96-2.061-1.082 0-1.96.923-1.96 2.061v3.28c-1.338-.046-2.16-.202-2.774-.73a3.223 3.223 0 0 1-.434-.457c-.712-.914-.712-2.268-.712-4.979zm5.642 6.225h.476-.476z"
          fill="#FF9419"
        />
      </g>
    </svg>
  </i>
);

StoreIcon.propTypes = {
  className: PropTypes.string,
};
StoreIcon.defaultProps = {
  className: null,
};

StoreIcon.displayName = 'StoreIcon';
