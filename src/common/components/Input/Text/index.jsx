import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { formRules, getInvalidFieldMessage } from '../utils';
import { getClassName } from '../../../utils/ui';
import Input from '../Input';
import styles from '../InputValidation.module.scss';

const getErrorMessage = (fieldName, value, rules, customMessages) => {
  const validationRules = {};

  if (rules.required) {
    validationRules.required = formRules.required;
  }

  if (!_isEmpty(value) && rules.pattern) {
    validationRules.pattern = {
      ...formRules.pattern,
      pattern: rules.pattern,
    };
  }

  return getInvalidFieldMessage(fieldName, value, validationRules, customMessages);
};

const InputText = ({
  validationContainerClassName,
  containerClassName,
  label,
  disabled,
  ready,
  className,
  style,
  name,
  inputmode,
  maxlength,
  minlength,
  placeholder,
  value,
  messages,
  rules,
  error,
  onChange,
  onBlur,
  onValidation,
}) => {
  const [internalErrorMessage, setInternalErrorMessage] = useState();
  const { required, pattern } = rules;
  const errorMessage = internalErrorMessage || error;
  const handleChangeText = useCallback(
    currentValue => {
      const changedErrorMessage = getErrorMessage(label, currentValue, { required, pattern }, messages);

      if (!changedErrorMessage) {
        setInternalErrorMessage(null);
        onValidation && onValidation({ name, isInvalid: false });
      } else if (internalErrorMessage && internalErrorMessage !== changedErrorMessage) {
        // Only display if there was a previous error and the current error is not equal to the subsequent error.
        setInternalErrorMessage(changedErrorMessage);
        onValidation && onValidation({ name, isInvalid: !!changedErrorMessage });
      }

      onChange && onChange(currentValue);
    },
    [onChange, onValidation, name, label, messages, pattern, required, internalErrorMessage]
  );
  const handleBlurInputText = useCallback(
    currentValue => {
      const blurErrorMessage = getErrorMessage(label, currentValue, { required, pattern }, messages);

      setInternalErrorMessage(blurErrorMessage);
      onValidation && onValidation({ name, isInvalid: !!blurErrorMessage });
      onBlur && onBlur(currentValue);
    },
    [onBlur, onValidation, name, label, messages, pattern, required]
  );

  return (
    <div className={getClassName([styles.InputValidationContainer, validationContainerClassName])}>
      <Input
        containerClassName={containerClassName}
        label={label}
        required={required}
        isInvalid={!!errorMessage}
        disabled={disabled}
        ready={ready}
        className={className}
        style={style}
        type="text"
        maxlength={maxlength}
        minlength={minlength}
        name={name}
        inputmode={inputmode}
        pattern={pattern}
        placeholder={placeholder}
        value={value}
        onChangeValue={handleChangeText}
        onBlurValue={handleBlurInputText}
      />
      <span className={styles.InputValidationErrorMessage}>{errorMessage}</span>
    </div>
  );
};

InputText.displayName = 'InputText';

InputText.propTypes = {
  validationContainerClassName: PropTypes.string,
  containerClassName: PropTypes.string,
  label: PropTypes.string,
  disabled: PropTypes.bool,
  ready: PropTypes.bool,
  className: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  style: PropTypes.object,
  name: PropTypes.string,
  inputmode: PropTypes.string,
  maxlength: PropTypes.number,
  minlength: PropTypes.number,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  messages: PropTypes.shape({
    required: PropTypes.string,
    pattern: PropTypes.string,
  }),
  rules: PropTypes.shape({
    required: PropTypes.bool,
    pattern: PropTypes.string,
  }),
  error: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onValidation: PropTypes.func,
};

InputText.defaultProps = {
  validationContainerClassName: null,
  containerClassName: null,
  label: 'Text',
  disabled: false,
  ready: true,
  className: null,
  style: null,
  name: null,
  inputmode: 'text',
  maxlength: undefined,
  minlength: 0,
  value: '',
  placeholder: '',
  messages: {
    required: null,
    pattern: null,
  },
  rules: {
    required: false,
    pattern: null,
  },
  error: null,
  onChange: () => {},
  onBlur: () => {},
  onValidation: () => {},
};

export default InputText;
