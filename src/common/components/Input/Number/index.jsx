import _isEmpty from 'lodash/isEmpty';
import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { formRules, getInvalidFieldMessage } from '../utils';
import Input from '../Input';
import styles from '../InputValidation.module.scss';

const getErrorMessage = (fieldName, value, rules, customMessages, { min, max, step }) => {
  const validationRules = {};
  const options = {
    min,
    max,
    step,
  };

  if (step === 1) {
    options.integer = true;
  }

  if (rules.required) {
    validationRules.required = formRules.required;
  }

  if (!_isEmpty(value)) {
    validationRules.number = formRules.number;
  }

  if (!_isEmpty(value) && rules.pattern) {
    validationRules.pattern = {
      ...formRules.pattern,
      pattern: rules.pattern,
    };
  }

  return getInvalidFieldMessage(fieldName, value, validationRules, customMessages, options);
};

const InputNumber = ({
  containerClassName,
  label,
  disabled,
  autoFocus,
  className,
  style,
  name,
  inputmode,
  step,
  max,
  min,
  value,
  messages,
  rules,
  onChange,
  onBlur,
  onValidation,
}) => {
  const [errorMessage, setErrorMessage] = useState();
  const { required, pattern } = rules;
  const handleChangeNumber = useCallback(
    currentValue => {
      const changedErrorMessage = getErrorMessage(label, currentValue, { required, pattern }, messages, {
        min,
        max,
        step,
      });

      // onChange only clears the error message if the value is valid
      // We don’t want the error message to be constantly shown and cleared, as it harms the user experience.
      if (!changedErrorMessage) {
        setErrorMessage(null);
        // onValidation only clears the error message if the value is valid
        onValidation && onValidation({ name, isInvalid: false });
      } else if (errorMessage && errorMessage !== changedErrorMessage) {
        // Only display if there was a previous error and the current error is not equal to the subsequent error.
        setErrorMessage(changedErrorMessage);
        onValidation && onValidation({ name, isInvalid: !!changedErrorMessage });
      }

      onChange && onChange(currentValue);
    },
    [onChange, onValidation, name, label, messages, pattern, required, min, max, step, errorMessage]
  );
  const handleBlurInputNumber = useCallback(
    currentValue => {
      const blurErrorMessage = getErrorMessage(label, currentValue, { required, pattern }, messages, {
        min,
        max,
        step,
      });

      // onBlur always sets the error message
      setErrorMessage(blurErrorMessage);
      // onValidation always sets the error message
      onValidation && onValidation({ name, isInvalid: !!blurErrorMessage });
      onBlur && onBlur(currentValue);
    },
    [onBlur, onValidation, name, label, messages, pattern, required, min, max, step]
  );

  return (
    <div className={styles.InputValidationContainer}>
      <Input
        containerClassName={containerClassName}
        label={label}
        required={required}
        isInvalid={!!errorMessage}
        disabled={disabled}
        autoFocus={autoFocus}
        className={className}
        style={style}
        type="number"
        inputmode={inputmode}
        pattern={pattern}
        min={min}
        max={max}
        step={step}
        name={name}
        value={value}
        onChangeValue={handleChangeNumber}
        onBlurValue={handleBlurInputNumber}
      />
      <span className={styles.InputValidationErrorMessage}>{errorMessage}</span>
    </div>
  );
};

InputNumber.displayName = 'InputNumber';

InputNumber.propTypes = {
  containerClassName: PropTypes.string,
  label: PropTypes.string,
  disabled: PropTypes.bool,
  autoFocus: PropTypes.bool,
  className: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  style: PropTypes.object,
  name: PropTypes.string,
  inputmode: PropTypes.oneOf(['numeric', 'decimal']),
  step: PropTypes.number,
  max: PropTypes.oneOfType([PropTypes.number, PropTypes.oneOf([Infinity])]),
  min: PropTypes.oneOfType([PropTypes.number, PropTypes.oneOf([-Infinity])]),
  value: PropTypes.string,
  messages: PropTypes.shape({
    required: PropTypes.string,
    pattern: PropTypes.string,
  }),
  rules: PropTypes.shape({
    required: PropTypes.bool,
    pattern: PropTypes.instanceOf(RegExp),
  }),
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onValidation: PropTypes.func,
};

InputNumber.defaultProps = {
  containerClassName: null,
  label: 'Number',
  disabled: false,
  autoFocus: false,
  className: null,
  style: null,
  name: null,
  inputmode: 'numeric',
  step: null,
  max: Infinity,
  min: -Infinity,
  value: '',
  messages: {
    required: null,
    pattern: null,
  },
  rules: {
    required: false,
    pattern: null,
  },
  onChange: () => {},
  onBlur: () => {},
  onValidation: () => {},
};

export default InputNumber;
