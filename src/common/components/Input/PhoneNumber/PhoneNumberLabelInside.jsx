import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { formRules, getInvalidFieldMessage } from '../utils';
import PhoneNumber from './PhoneNumber';
import styles from './PhoneNumberLabelInside.module.scss';

const getErrorMessage = (fieldName, value, rules, customMessages, isValidPhone) => {
  const validationRules = {};

  if (rules.required) {
    validationRules.required = formRules.required;
  }

  const errorMessage = getInvalidFieldMessage(fieldName, value, validationRules, customMessages);

  if (errorMessage) {
    return errorMessage;
  }

  if (!isValidPhone) {
    return formRules.phone.message(fieldName, customMessages.valid);
  }

  return null;
};
const PhoneNumberLabelInside = ({
  isLabelShown,
  label,
  messages,
  rules,
  className,
  placeholder,
  countries,
  ready,
  name,
  defaultCountry,
  defaultPhone,
  onChange,
  onChangeCountry,
  onBlur,
  onValidation,
}) => {
  const [errorMessage, setErrorMessage] = useState();
  const { required } = rules;
  const handleChangePhoneNumber = useCallback(
    currentPhone => {
      const changedErrorMessage = getErrorMessage(label, currentPhone, { required }, messages, true);

      if (!changedErrorMessage) {
        setErrorMessage(null);
        onValidation && onValidation({ name, isInvalid: false });
      } else if (errorMessage && errorMessage !== changedErrorMessage) {
        // Only display if there was a previous error and the current error is not equal to the subsequent error.
        setErrorMessage(changedErrorMessage);
        onValidation && onValidation({ name, isInvalid: !!changedErrorMessage });
      }

      onChange && onChange({ phone: currentPhone });
    },
    [onChange, onValidation, name, label, messages, required, errorMessage]
  );
  const handleChangeCountry = useCallback(
    currentCountry => {
      onChangeCountry && onChangeCountry(currentCountry);
    },
    [onChangeCountry]
  );
  const handleBlurPhoneInput = useCallback(
    ({ phone: currentPhone, country: currentCountry, invalid }) => {
      const blurErrorMessage = getErrorMessage(label, currentPhone, required, messages, !invalid);

      setErrorMessage(blurErrorMessage);
      onValidation && onValidation({ name, isInvalid: !!blurErrorMessage });
      onBlur &&
        onBlur({
          phone: currentPhone,
          country: currentCountry,
          invalid,
        });
    },
    [onBlur, onValidation, name, label, messages, required]
  );

  return (
    <div className={styles.PhoneNumberLabelInsideContainer}>
      <div className={styles.PhoneNumberLabelInsideInputContainer}>
        {isLabelShown && (
          <label className={styles.PhoneNumberLabelInsideLabel} htmlFor={name}>
            <span className={styles.PhoneNumberLabelInsideLabelText}>{label}</span>
            {required && <sup className={styles.PhoneNumberLabelInsideLabelRequiredIcon}>*</sup>}
          </label>
        )}

        <PhoneNumber
          className={className}
          placeholder={placeholder}
          countries={countries}
          ready={ready}
          name={name}
          defaultCountry={defaultCountry}
          defaultPhone={defaultPhone}
          onChangePhoneNumber={handleChangePhoneNumber}
          onChangeCountry={handleChangeCountry}
          onBlurPhoneInput={handleBlurPhoneInput}
        />
      </div>
      <span className={styles.PhoneNumberLabelInsideErrorMessage}>{errorMessage}</span>
    </div>
  );
};

PhoneNumberLabelInside.displayName = 'PhoneNumberLabelInside';

PhoneNumberLabelInside.propTypes = {
  isLabelShown: PropTypes.bool,
  label: PropTypes.string,
  ready: PropTypes.bool,
  className: PropTypes.string,
  placeholder: PropTypes.string,
  countries: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string)]),
  name: PropTypes.string,
  defaultCountry: PropTypes.string,
  defaultPhone: PropTypes.string,
  messages: PropTypes.shape({
    required: PropTypes.string,
    phone: PropTypes.string,
  }),
  rules: PropTypes.shape({
    required: PropTypes.bool,
  }),
  onChange: PropTypes.func,
  onChangeCountry: PropTypes.func,
  onBlur: PropTypes.func,
  onValidation: PropTypes.func,
};

PhoneNumberLabelInside.defaultProps = {
  isLabelShown: true,
  label: 'Mobile Number',
  ready: true,
  className: null,
  placeholder: '',
  countries: undefined,
  name: null,
  defaultCountry: '',
  defaultPhone: '',
  messages: {
    required: null,
  },
  rules: {
    required: false,
  },
  onChange: () => {},
  onChangeCountry: () => {},
  onBlur: () => {},
  onValidation: () => {},
};

export default PhoneNumberLabelInside;
