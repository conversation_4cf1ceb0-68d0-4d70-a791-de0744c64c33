.InputContainer {
  @apply tw-flex tw-flex-col tw-gap-2 sm:tw-gap-2px tw-px-12 sm:tw-px-12px tw-py-8 sm:tw-py-8px tw-border tw-border-solid tw-border-gray-500 tw-bg-gray-50 tw-rounded;
}

.InputContainerDisabled {
  @apply tw-bg-gray-300;
}

.InputContainerError {
  @apply tw-border-red;
}

.InputLabel {
  @apply tw-flex tw-gap-2 sm:tw-gap-2px;
}

.InputLabelText {
  @apply tw-text-sm tw-leading-normal;
}

.InputLabelRequiredIcon {
  @apply tw-static tw-top-0 tw-text-red;

  font-size: unset;
  line-height: normal;
}

.Input {
  @apply tw-border-0 tw-w-full tw-bg-transparent tw-leading-normal tw-shadow-none tw-outline-none;

  height: 20px;

  &:disabled {
    @apply tw-bg-gray-300;
  }

  &:invalid {
    @apply tw-text-red;
  }

  &::placeholder,
  &::-webkit-input-placeholder,
  &::-moz-placeholder {
    @apply tw-text-gray-500;
  }

  &[type="number"] {
    -moz-appearance: textfield; /* Firefox */
    appearance: textfield;
  }

  &[type="number"]::-webkit-inner-spin-button,
  &[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.InputLabel .InputError {
  @apply tw-text-red;
}
