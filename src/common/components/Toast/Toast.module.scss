@use "../../../common/styles/variables" as *;

.ToastContainer {
  @apply tw-fixed tw-top-16 sm:tw-top-16px tw-z-200;

  left: 50%;
  margin-left: auto;
  margin-right: auto;
  max-width: 90%;
  height: auto;
  transform: translateX(-50%);
}

.ToastContent {
  @apply tw-flex tw-items-start tw-justify-start tw-py-4 sm:tw-py-4px tw-px-8 sm:tw-px-8px tw-text-sm tw-text-white tw-rounded-xl;

  height: auto;

  &:global(.success) {
    @apply tw-bg-green;
  }

  &:global(.error) {
    @apply tw-bg-red;
  }

  &:global(.warning) {
    @apply tw-bg-yellow;
  }

  &:global(.info) {
    @apply tw-bg-blue;
  }
}

.ToastIcon {
  @apply tw-flex-shrink-0 tw-my-2 sm:tw-my-2px tw-text-lg;
}

.ToastText {
  @apply tw-text-sm tw-leading-loose;

  @include text-line-clamp(1);
}

.ToastIcon + .ToastText {
  @apply tw-ml-4 sm:tw-ml-4px;
}

@media (min-width: 770px) {
  .ToastContainer {
    max-width: 350px;
  }
}
