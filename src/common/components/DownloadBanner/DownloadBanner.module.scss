@use "../../styles/variables" as *;

.DownloadBanner {
  @apply tw-flex tw-items-center tw-p-12 sm:tw-p-12px tw-gap-12 sm:tw-gap-12px tw-text-black tw-no-underline tw-rounded-lg tw-bg-no-repeat tw-bg-cover;

  background-image: url("../../../images/beep-app-download-background.jpg");
}

.DownloadBannerCloseButton:global(.type-text-default) {
  @apply tw-p-4 sm:tw-p-4px tw-flex-shrink-0 tw-text-black;

  min-width: auto;

  &:hover {
    @apply tw-text-gray;
  }
}

.DownloadBannerText {
  @apply tw-flex-1 tw-text-base tw-font-bold;
  @include text-line-clamp(2, null);
}

.DownloadBannerAppStoreIcon {
  @apply tw-flex-shrink-0 tw-rounded-lg tw-overflow-hidden;

  width: 50px;
  height: 50px;
}
