import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { X } from 'phosphor-react';
import beepAppAppleStore from '../../../images/beep-app-apple-store.svg';
import beepAppGooglePlay from '../../../images/beep-app-google-play.svg';
import { judgeClient, getIsDesktopClients } from '../../utils';
import { getClassName } from '../../utils/ui';
import { ObjectFitImage } from '../Image';
import Button from '../Button';
import styles from './DownloadBanner.module.scss';

const DownloadBanner = ({ link, text, backgroundImage, closable, onClose, className }) => {
  const [isVisible, setIsVisible] = useState(true);
  const isDesktopClient = getIsDesktopClients(judgeClient());
  const bannerStyle = backgroundImage ? { backgroundImage: `url(${backgroundImage})` } : {};
  const handleCloseClick = event => {
    event.preventDefault();
    event.stopPropagation();

    setIsVisible(false);

    if (onClose) {
      onClose();
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <a
      className={getClassName([styles.DownloadBanner, className])}
      href={link}
      rel="noreferrer"
      target={isDesktopClient ? '_blank' : ''}
      style={bannerStyle}
    >
      {closable && (
        <Button
          type="text"
          className={styles.DownloadBannerCloseButton}
          onClick={handleCloseClick}
          data-test-id="common.download-banner.close-button"
        >
          <X size={16} />
        </Button>
      )}
      <span className={styles.DownloadBannerText}>{text}</span>
      <div className={styles.DownloadBannerAppStoreIcon}>
        <ObjectFitImage noCompression src={beepAppAppleStore} alt="Beep Apple Store Download" />
      </div>
      <div className={styles.DownloadBannerAppStoreIcon}>
        <ObjectFitImage noCompression src={beepAppGooglePlay} alt="Beep Google Play Download" />
      </div>
    </a>
  );
};

DownloadBanner.displayName = 'DownloadBanner';

DownloadBanner.propTypes = {
  link: PropTypes.string,
  text: PropTypes.string,
  backgroundImage: PropTypes.string,
  closable: PropTypes.bool,
  onClose: PropTypes.func,
  className: PropTypes.string,
};

DownloadBanner.defaultProps = {
  link: '',
  text: '',
  backgroundImage: '',
  closable: false,
  onClose: null,
  className: '',
};

export default DownloadBanner;
