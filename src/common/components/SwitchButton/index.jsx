import React from 'react';
import PropTypes from 'prop-types';
import { getClassName } from '../../utils/ui';
import styles from './SwitchButton.module.scss';

const SwitchButton = ({ className, 'data-test-id': dataTestId, disabled, options, activeKey, onChange }) => {
  const handleOptionClick = optionKey => {
    if (disabled) return;

    const option = options.find(opt => opt.key === optionKey);
    if (option && !option.disabled) {
      onChange(optionKey);
    }
  };

  return (
    <div className={getClassName([styles.SwitchButtonContainer, className])} data-test-id={dataTestId}>
      {options.map(option => (
        <button
          key={option.key}
          className={`${styles.SwitchButtonOption}${activeKey === option.key ? ' active' : ''}`}
          data-test-id={`${dataTestId}.${option.key}`}
          disabled={disabled || option.disabled}
          onClick={() => handleOptionClick(option.key)}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
};

SwitchButton.propTypes = {
  className: PropTypes.string,
  'data-test-id': PropTypes.string,
  disabled: PropTypes.bool,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      disabled: PropTypes.bool,
    })
  ).isRequired,
  activeKey: PropTypes.string,
  onChange: PropTypes.func,
};

SwitchButton.defaultProps = {
  className: '',
  'data-test-id': '',
  disabled: false,
  activeKey: '',
  onChange: () => {},
};

SwitchButton.displayName = 'SwitchButton';

export default SwitchButton;
