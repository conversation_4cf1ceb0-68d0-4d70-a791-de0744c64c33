// It is an array of several UI attributes of button, each button item contains $type $theme $color
$attributes: (
  "primary" "default" "orange",
  "primary" "danger" "red",
  "primary" "info" "blue",
  "secondary" "default" "orange",
  "secondary" "danger" "red",
  "secondary" "info" "blue",
  "text" "default" "orange",
  "text" "ghost" "gray",
  "text" "danger" "red",
  "text" "info" "blue"
);

@mixin buttonHeight($size, $type) {
  @if ($type == "text") {
    height: auto;
  } @else if $size == "small" {
    height: 40px;
  } @else if $size == "normal" {
    height: 50px;
  }
}

@mixin buttonPseudoClasses($type, $color, $color-dark, $color-light) {
  @if ($type == "primary") {
    @apply hover:tw-border-#{$color-dark} hover:tw-bg-#{$color-dark} /* hover style */
    focus:tw-border-#{$color} focus:tw-bg-#{$color}
    focus:hover:tw-border-#{$color-dark} focus:hover:tw-bg-#{$color-dark}
    focus:active:tw-border-#{$color-light} focus:active:tw-bg-#{$color-light} /* focus style */
    active:tw-border-#{$color-light} active:tw-bg-#{$color-light} /* active style */
    disabled:tw-border-gray-400 disabled:tw-bg-gray-400; /* disabled style */
  } @else if ($type == "secondary") {
    @apply hover:tw-border-#{$color-dark} hover:tw-text-#{$color-dark} /* hover style */
    focus:tw-border-#{$color} focus:tw-text-#{$color}
    focus:hover:tw-border-#{$color-dark} focus:hover:tw-text-#{$color-dark}
    focus:active:tw-border-#{$color-light} focus:active:tw-text-#{$color-light} /* focus style */
    active:tw-border-#{$color-light} active:tw-text-#{$color-light} /* active style */
    disabled:tw-border-gray-400 disabled:tw-text-gray-400; /* disabled style */
  } @else if ($type == "text") {
    @apply hover:tw-text-#{$color-dark} /* hover style */
    focus:tw-text-#{$color}
    focus:hover:tw-text-#{$color-dark}
    focus:active:tw-text-#{$color-light} /* focus style */
    active:tw-text-#{$color-light} /* active style */
    disabled:tw-text-gray-400; /* disabled style */
  }
}

.button {
  @apply tw-flex tw-items-center tw-justify-center tw-p-8 sm:tw-p-8px
  tw-border tw-border-transparent tw-border-solid tw-rounded/* border */
  tw-cursor-pointer disabled:tw-cursor-default;

  &:global(.size-normal) {
    @include buttonHeight("normal", null);
  }

  &:global(.size-small) {
    @include buttonHeight("small", null);
  }

  .iconWrapper {
    @apply tw-inline-block tw-h-0 tw-align-middle tw-relative tw-mr-6 sm:tw-mr-6px;
  }

  .iconInnerWrapper {
    display: inline-flex;
    transform: translateY(-50%);

    & > * {
      vertical-align: top;
    }
  }

  @each $type, $theme, $color in $attributes {
    &:global(.type-#{$type}-#{$theme}) {
      @apply tw-transition; /* https://tailwindcss.com/docs/transition-property */

      .buttonContent {
        @apply tw-font-bold;
      }

      /* tw-tracking-wide: letter spacing */
      @if ($type == "text") {
        @apply tw-p-8 sm:tw-p-8px tw-tracking-wide;
        @include buttonHeight(null, "text");
      } @else {
        @apply tw-py-12 sm:tw-py-12px tw-px-16 sm:tw-px-16px tw-tracking-wider;
      }

      // Initialize UI for different types of buttons
      @if ($type == "primary") {
        @apply tw-text-white tw-bg-#{$color} tw-border-#{$color};
      } @else if ($type == "secondary") {
        @apply tw-text-#{$color} tw-bg-white tw-border-#{$color};
      } @else if ($type == "text") {
        @apply tw-text-#{$color} tw-bg-transparent;
      }

      // Can't swap the order with the styles above, it will change the style priority
      // buttonPseudoClasses: buttonPseudoStyle is Pseudo-classes for setting different types of buttons, Pseudo-classes: https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes
      @if ($theme == "ghost" and $type == "text") {
        @include buttonPseudoClasses("text", $color, #{$color}-900, #{$color}-700);
      } @else {
        @include buttonPseudoClasses($type, $color, #{$color}-dark, #{$color}-light);
      }
    }
  }
}

.buttonContent {
  @apply tw-leading-normal;

  font-size: inherit;
}
