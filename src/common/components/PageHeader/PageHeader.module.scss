@use "../../styles/variables" as *;

.PageHeaderContainer {
  @apply tw-flex tw-justify-between tw-items-center tw-border-0 tw-border-b tw-border-solid tw-border-gray-200 tw-text-gray;

  height: calc($headerHeight - 1px);
}

.PageHeaderLeftContainer {
  @apply tw-flex tw-flex-shrink-0 tw-items-center tw-w-3/10 tw-whitespace-nowrap;
}

.PageHeaderTitle {
  @apply tw-font-bold tw-text-lg tw-leading-relaxed;
}

.PageHeaderIconWrapper {
  @apply tw-flex tw-flex-shrink-0 tw-items-stretch sm:tw-p-12px tw-p-12
    tw-border-none tw-outline-none tw-bg-transparent tw-cursor-pointer;

  color: inherit;
}
