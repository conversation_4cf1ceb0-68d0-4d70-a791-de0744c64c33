$contentWidthInBigScreen: 412px; // not including the border
$headerHeight: 50px;

@mixin fixed-width {
  left: 0;
  right: 0;
  @media (min-width: 770px) {
    left: calc(50% - ((#{$contentWidthInBigScreen}) / 2));
    right: calc(50% - ((#{$contentWidthInBigScreen}) / 2));
  }
}

@mixin text-line-clamp($lines, $maxHeight: null, $display: inline-block) {
  @if $lines == 1 {
    @apply tw-overflow-hidden tw-whitespace-nowrap;

    -webkit-box-orient: vertical;
    display: $display;
  }

  @if $lines == 2 {
    @apply tw-line-clamp-2;
  }

  @if $lines >= 2 {
    word-break: break-word;
    hyphens: none;
  }

  @if $maxHeight {
    max-height: $maxHeight;
  }

  text-overflow: ellipsis;
}
