@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  @apply tw-m-0 tw-p-0 tw-font-normal tw-outline-0;

  font-size: 14px;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  touch-action: manipulate;
}

html {
  font-size: 14px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

#modal-mount-point {
  @apply tw-font-sans;
}

// If display want to use flex of line-clamp container, please add this class for container
.beep-line-clamp-flex-container {
  @apply tw-flex tw-flex-1;

  min-width: 0;
}

.beep-text-reset {
  font-size: 0;
}
