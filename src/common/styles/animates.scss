@keyframes scaleToDisappearance {
  0% {
    transform: scale(1);
    -webkit-transform: scale(1);
    opacity: 0.5;
  }
  25% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.5);
    -webkit-transform: scale(1.5);
    opacity: 0;
  }
}

@-webkit-keyframes scaleToDisappearance {
  0% {
    transform: scale(1);
    -webkit-transform: scale(1);
    opacity: 0.5;
  }
  25% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.5);
    -webkit-transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes tada {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-3deg);
    transform: scale(0.9) rotate(-3deg);
  }

  30%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.1) rotate(3deg);
    transform: scale(1.1) rotate(3deg);
  }

  40%,
  60%,
  80% {
    -webkit-transform: scale(1.1) rotate(-3deg);
    transform: scale(1.1) rotate(-3deg);
  }

  to {
    -webkit-transform: scale(1) rotate(0);
    transform: scale(1) rotate(0);
  }
}

.beep-animated {
  animation-fill-mode: both;
}

.beep-animated--infinite {
  animation-iteration-count: infinite;
}

.beep-animated--default-duration {
  animation-duration: 2s;
}

.beep-scale-to-disappearance {
  animation-name: scaleToDisappearance;
}

.beep-tada {
  animation-name: tada;
}
