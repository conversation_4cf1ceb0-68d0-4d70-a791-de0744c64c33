export const OTP_MAX_LENGTH = 5;

export const OTP_RESEND_INTERVAL = 60;

export const OTP_VALUE_PATTERN = '[0-9]*';

export const OTP_REQUEST_TYPES = {
  OTP: 'otp',
  RE_SEND_OTP: 'reSendotp',
  WHATSAPP: 'WhatsApp',
};

export const OTP_REQUEST_PLATFORM = 'BeepWeb';

export const OTP_COMMON_ERROR_TYPES = {
  NETWORK_ERROR: 'Network Error',
  UNKNOWN_ERROR: 'Unknown Error',
};

export const BEEP_OTP_ERROR_CODES = {
  PHONE_INVALID: '41001',
  TYPE_INVALID: '41002',
};

export const OTP_SERVICE_ERROR_CODES = {
  PHONE_INVALID: '394761',
  REQUEST_TOO_FAST: '394757',
  MEET_DAY_LIMIT: '394755',
  HIGH_RISK: '394756',
};

export const SMS_API_ERROR_CODES = {
  PHONE_INVALID: '395011',
  NO_AVAILABLE_PROVIDER: '395012',
};

export const OTP_ERROR_POPUP_I18N_KEYS = {
  [BEEP_OTP_ERROR_CODES.PHONE_INVALID]: {
    title: 'Common:ErrorOtpPhoneInvalidTitle',
    description: 'Common:ErrorOtpPhoneInvalidDescription',
  },
  [OTP_SERVICE_ERROR_CODES.PHONE_INVALID]: {
    title: 'Common:ErrorOtpPhoneInvalidTitle',
    description: 'Common:ErrorOtpPhoneInvalidDescription',
  },
  [OTP_SERVICE_ERROR_CODES.MEET_DAY_LIMIT]: {
    title: 'Common:ErrorOtpMeetDayLimitTitle',
    description: 'Common:ErrorOtpMeetDayLimitDescription',
  },
  [OTP_SERVICE_ERROR_CODES.REQUEST_TOO_FAST]: {
    title: 'Common:ErrorOtpRequestTooFastTitle',
    description: 'Common:ErrorOtpRequestTooFastDescription',
  },
  [OTP_SERVICE_ERROR_CODES.HIGH_RISK]: {
    title: 'Common:ErrorOtpHighRiskTitle',
    description: 'Common:ErrorOtpHighRiskDescription',
  },
  [OTP_COMMON_ERROR_TYPES.NETWORK_ERROR]: {
    title: 'NetworkErrorTitle',
    description: 'NetworkErrorDescription',
  },
  [OTP_COMMON_ERROR_TYPES.UNKNOWN_ERROR]: {
    title: 'UnknownErrorTitle',
    description: 'UnknownErrorDescription',
  },
  [SMS_API_ERROR_CODES.PHONE_INVALID]: {
    title: 'Common:ErrorOtpPhoneInvalidTitle',
    description: 'Common:ErrorOtpPhoneInvalidDescription',
  },
  [SMS_API_ERROR_CODES.NO_AVAILABLE_PROVIDER]: {
    title: 'Common:ErrorOtpNoAvailableProviderTitle',
    description: 'Common:ErrorOtpNoAvailableProviderDescription',
  },
};
