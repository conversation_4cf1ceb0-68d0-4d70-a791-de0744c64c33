import { PHONE_NUMBER_COUNTRIES, COUNTRIES } from '../../../utils/phone-number-constants';

/**
 * Get country key based on phone number string
 * @param {string} phoneNumber - Phone number string to match
 * @returns {string} - Country key (e.g., 'MY', 'TH') or 'MY' as default if no match found
 */
export const getCountryKeyByPhone = phoneNumber => {
  if (!phoneNumber || typeof phoneNumber !== 'string') {
    return COUNTRIES.MY;
  }

  const phoneNumberCountryKeys = Object.keys(PHONE_NUMBER_COUNTRIES);

  const matchedCountryKey = phoneNumberCountryKeys.find(key => {
    const countryCode = PHONE_NUMBER_COUNTRIES[key];
    return phoneNumber.startsWith(countryCode) || phoneNumber.startsWith(`+${countryCode}`);
  });

  return matchedCountryKey || COUNTRIES.MY;
};
