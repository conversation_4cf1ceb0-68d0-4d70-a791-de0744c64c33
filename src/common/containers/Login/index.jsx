import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles } from 'react-use';
import { useTranslation } from 'react-i18next';
import { X, CaretLeft } from 'phosphor-react';
import { KEY_EVENTS_FLOWS, KEY_EVENTS_STEPS } from '../../../utils/monitoring/constants';
import logger from '../../../utils/monitoring/logger';
import { getIsLogin } from '../../../redux/modules/user/selectors';
import {
  getSendOtpAlertErrorI18nKey,
  getIsSendOtpToUserRequestFulfilled,
  getIsLoginCompleteProcessing,
} from './redux/selectors';
import { actions as loginDrawerActions } from './redux';
import { mounted } from './redux/thunks';
import { globalName as RECAPTCHA_GLOBAL_NAME } from '../../components/ReCAPTCHA';
import Drawer from '../../components/Drawer';
import DrawerHeader from '../../components/Drawer/DrawerHeader';
import TermsAndPrivacy from '../../components/TermsAndPrivacy';
import PageToast from '../../components/PageToast';
import Loader from '../../components/Loader';
import { alert } from '../../utils/feedback';
import PhoneForm from './components/PhoneForm';
import OtpForm from './components/OtpForm';
import LoginReCaptcha from './components/LoginReCaptcha';
import styles from './Login.module.scss';

const Login = ({ show, showCloseButton, onClose }) => {
  const { t } = useTranslation();
  const captchaRef = useRef(null);
  const dispatch = useDispatch();
  const [isOtpFormShown, setIsOtpFormShown] = useState(false);
  const isLogin = useSelector(getIsLogin);
  const sendOtpAlertErrorI18nKey = useSelector(getSendOtpAlertErrorI18nKey);
  const isSendOtpToUserRequestFulfilled = useSelector(getIsSendOtpToUserRequestFulfilled);
  const isLoginCompleteProcessing = useSelector(getIsLoginCompleteProcessing);
  const isLoginDrawerShown = useMemo(() => show && !isLogin, [show, isLogin]);
  const handleClickCloseButton = useCallback(() => {
    onClose();
  }, [onClose]);
  const handleClickBackButton = useCallback(() => {
    setIsOtpFormShown(false);
  }, []);
  const handleCompleteReCAPTCHA = async () => {
    try {
      if (!window[RECAPTCHA_GLOBAL_NAME]) {
        throw new Error('ReCaptcha failed to load');
      }

      const token = await captchaRef.current.executeAsync();
      captchaRef.current.reset();

      if (!token) {
        throw new Error('ReCaptcha response is expired');
      }

      logger.log('Login_CompleteCaptchaSucceed');

      return token;
    } catch (error) {
      alert(t('NetworkErrorDescription'), {
        title: t('NetworkErrorTitle'),
      });

      logger.error(
        'Login_CompleteCaptchaFailed',
        { message: error?.message },
        {
          bizFlow: {
            flow: KEY_EVENTS_FLOWS.LOGIN,
            step: KEY_EVENTS_STEPS[KEY_EVENTS_FLOWS.LOGIN].RECEIVE_OTP,
          },
        }
      );

      throw error;
    }
  };

  useLifecycles(
    () => {
      dispatch(mounted());
    },
    () => {
      dispatch(loginDrawerActions.stateReset());
      setIsOtpFormShown(false);
    }
  );

  useEffect(() => {
    if (isSendOtpToUserRequestFulfilled) {
      setIsOtpFormShown(true);
    }
  }, [isSendOtpToUserRequestFulfilled]);

  useEffect(() => {
    if (sendOtpAlertErrorI18nKey) {
      alert(t(sendOtpAlertErrorI18nKey.description), {
        title: t(sendOtpAlertErrorI18nKey.title),
      });
    }
  }, [sendOtpAlertErrorI18nKey, t]);

  return (
    <Drawer
      className={styles.LoginDrawer}
      childrenClassName={styles.LoginDrawerContent}
      fullScreen
      show={isLoginDrawerShown}
      onClose={handleClickCloseButton}
      header={
        <DrawerHeader
          className={styles.LoginDrawerHeader}
          left={
            isOtpFormShown ? (
              <CaretLeft
                weight="light"
                className="tw-flex-shrink-0 tw-text-2xl tw-text-gray"
                onClick={handleClickBackButton}
                data-test-id="user.login.drawer.close-btn"
              />
            ) : (
              showCloseButton && (
                <X
                  weight="light"
                  className="tw-flex-shrink-0 tw-text-2xl tw-text-gray"
                  onClick={handleClickCloseButton}
                  data-test-id="user.login.drawer.close-btn"
                />
              )
            )
          }
        >
          <h2 className={styles.LoginDrawerHeaderTitle}>{t('LoginHeaderTitle')}</h2>
        </DrawerHeader>
      }
    >
      {isOtpFormShown ? (
        <section className={styles.LoginDrawerOtpFormContent}>
          <OtpForm onCompleteReCAPTCHA={handleCompleteReCAPTCHA} onCloseLoginDrawer={onClose} />
        </section>
      ) : (
        <section className={styles.LoginDrawerCheckingPhoneContent}>
          <PhoneForm onCompleteReCAPTCHA={handleCompleteReCAPTCHA} />
        </section>
      )}

      <p className={styles.LoginDrawerTermsAndPrivacy}>
        <TermsAndPrivacy buttonLinkClassName={styles.LoginDrawerTermsAndPrivacyButton} />
      </p>

      <LoginReCaptcha ref={captchaRef} />

      {isLoginCompleteProcessing ? (
        <PageToast icon={<Loader className="tw-m-8 sm:tw-m-8px" size={30} />}>{`${t('Processing')}...`}</PageToast>
      ) : null}
    </Drawer>
  );
};

Login.displayName = 'Login';

Login.propTypes = {
  show: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  onClose: PropTypes.func,
};

Login.defaultProps = {
  show: false,
  showCloseButton: true,
  onClose: () => {},
};

export default Login;
