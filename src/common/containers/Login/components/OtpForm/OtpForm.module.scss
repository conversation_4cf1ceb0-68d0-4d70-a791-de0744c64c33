.OtpFormImageContainer {
  @apply tw-mx-auto tw-my-24 sm:tw-my-24px;

  width: 44%;
  max-width: 160px;
  aspect-ratio: 1 / 1;

  figure {
    @apply tw-mx-0 tw-bg-transparent;
  }
}

@mixin widthValue {
  @apply tw-text-5xl;

  width: 7.85em;
}

.OtpFormTitleContainer {
  @apply tw-mx-auto tw-box-border;
  @include widthValue;

  padding-left: 0.4em;
  padding-right: 0.4em;
}

.OtpFormTitle {
  @apply tw-my-0 tw-text-xl tw-font-normal;
}

.OtpFormDescriptionContainer {
  @apply tw-mx-auto tw-box-border;
  @include widthValue;

  padding-left: 0.4em;
  padding-right: 0.4em;
}

.OtpFormDescription {
  @apply tw-mt-8 sm:tw-mt-8px tw-mb-8 sm:tw-mb-8px tw-text-base tw-leading-relaxed;
}

.OtpFormInputWrapper {
  @apply tw-relative tw-mx-auto;
  @include widthValue;

  padding-bottom: 28px;
}

.OtpFormInputValidationContainer {
  @apply tw-absolute tw-bottom-0 tw-left-0 tw-right-0;

  padding-bottom: 28px;

  > span {
    @apply tw-text-center tw-w-full tw-text-sm;
  }
}

@mixin otpInputItemFilled($length) {
  &:global(.otp-filled-#{$length}) .OtpFormInputItem:nth-child(-n + #{$length})::after {
    @apply tw-bg-orange;
  }
}

.OtpFormInputItems {
  @apply tw-flex tw-justify-center tw-text-5xl;

  gap: 0.25em;
  padding-left: 0.4em;
  padding-right: 0.4em;

  @include otpInputItemFilled(1);
  @include otpInputItemFilled(2);
  @include otpInputItemFilled(3);
  @include otpInputItemFilled(4);
  @include otpInputItemFilled(5);
}

.OtpFormInputItem {
  @apply tw-relative tw-p-8 sm:tw-p-8px tw-border-2 tw-border-0 tw-text-5xl;

  width: 0.75em;
  height: 42px;

  &::after {
    @apply tw-absolute tw-bottom-0 tw-left-0 tw-right-0 tw-bg-gray-700;

    content: "";
    height: 2px;
  }
}

.OtpFormInputContainer {
  @apply tw-p-0 tw-border-0 tw-bg-transparent;
  @include widthValue;

  label {
    @apply tw-hidden;
  }
}

.OtpFormInput {
  @include widthValue;

  height: 60px;
  letter-spacing: 0.85em;
  text-indent: 0.85em;
  caret-color: transparent;
}

.OtpFormCountDown {
  @apply tw-flex tw-justify-center tw-items-center tw-my-12 sm:tw-my-12px tw-text-base tw-text-gray-700;

  height: 50px;
}

.OtpFormButtons {
  @apply tw-flex tw-flex-col tw-justify-center tw-items-center tw-gap-12 sm:tw-gap-12px tw-my-12 sm:tw-my-12px;
}

.OtpFormSMSResendButton {
  @apply tw-uppercase;

  &:global(.type-secondary-default) {
    @apply tw-border-gray-800 tw-text-gray-800;
  }
}

.OtpFormWhatsappResendButton {
  @apply tw-uppercase;
}
