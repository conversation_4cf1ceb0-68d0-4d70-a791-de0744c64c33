import React, { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles } from 'react-use';
import { useTranslation } from 'react-i18next';
import beepOtpLockImage from '../../../../../images/beep-otp-lock.svg';
import beepOtpErrorImage from '../../../../../images/beep-otp-error.svg';
import { OTP_MAX_LENGTH, OTP_RESEND_INTERVAL, OTP_VALUE_PATTERN } from '../../../../utils/constants';
import { getClassName } from '../../../../utils/ui';
import {
  getIsCompleteLoginFailed,
  getLoginPhoneNumber,
  getIsRecaptchaEnabled,
  getVerifyOtpRequestError,
} from '../../redux/selectors';
import { completeLogin, clickResendOtpButton, clickResendWhatsappButton } from '../../redux/thunks';
import { ObjectFitImage } from '../../../../components/Image';
import InputText from '../../../../components/Input/Text';
import Button from '../../../../components/Button';
import styles from './OtpForm.module.scss';

let interval = null;
const OtpForm = ({ onCompleteReCAPTCHA }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const formRef = useRef(null);
  const phoneNumber = useSelector(getLoginPhoneNumber);
  const [otp, setOtp] = useState('');
  const [isResendOtpEnabled, setIsResendOtpEnabled] = useState(false);
  const [resendOtpCountDownTime, setResendOtpCountDownTime] = useState(OTP_RESEND_INTERVAL);
  const isCompleteLoginFailed = useSelector(getIsCompleteLoginFailed);
  const isRecaptchaEnabled = useSelector(getIsRecaptchaEnabled);
  const verifyOtpRequestError = useSelector(getVerifyOtpRequestError);
  const handleChangeOtp = useCallback(
    value => {
      const OtpRegex = new RegExp(`^${OTP_VALUE_PATTERN}$`);
      const isValid = OtpRegex.test(value);

      if (isValid) {
        setOtp(value);

        if (value.length === OTP_MAX_LENGTH) {
          dispatch(completeLogin(value));

          const otpInput = formRef.current?.querySelector('input[name="otp"]');

          if (otpInput) {
            otpInput.blur();
          }
        }
      }
    },
    [dispatch]
  );
  const handleClickResendOtp = useCallback(async () => {
    setIsResendOtpEnabled(false);

    const payload = {};

    if (isRecaptchaEnabled) {
      payload.captchaToken = await onCompleteReCAPTCHA();
    }

    dispatch(clickResendOtpButton(payload));
  }, [dispatch, onCompleteReCAPTCHA, isRecaptchaEnabled]);
  const handleClickResendWhatsapp = useCallback(async () => {
    dispatch(clickResendWhatsappButton());
  }, [dispatch]);

  useLifecycles(
    () => {
      const otpInput = formRef.current?.querySelector('input[name="otp"]');

      if (otpInput) {
        otpInput.focus();
      }
    },
    () => {
      setOtp('');
      setIsResendOtpEnabled(false);
      setResendOtpCountDownTime(OTP_RESEND_INTERVAL);
      clearInterval(interval);
    }
  );

  useEffect(() => {
    if (!isResendOtpEnabled && resendOtpCountDownTime === OTP_RESEND_INTERVAL) {
      interval = setInterval(() => {
        setResendOtpCountDownTime(prevResendOtpCountDownTime => prevResendOtpCountDownTime - 1);
      }, 1000);
    } else if (!isResendOtpEnabled && resendOtpCountDownTime <= 0) {
      clearInterval(interval);
      setIsResendOtpEnabled(true);
      setResendOtpCountDownTime(OTP_RESEND_INTERVAL);
    }
  }, [isResendOtpEnabled, resendOtpCountDownTime]);

  useEffect(() => {
    if (verifyOtpRequestError) {
      setOtp('');
    }
  }, [verifyOtpRequestError]);

  return (
    <>
      <div className={styles.OtpFormImageContainer}>
        <ObjectFitImage
          noCompression
          src={isCompleteLoginFailed ? beepOtpErrorImage : beepOtpLockImage}
          alt="Beep Otp Image"
        />
      </div>
      <div className={styles.OtpFormTitleContainer}>
        <h2 className={styles.OtpFormTitle}>{t('LoginOtpFormTitle')}</h2>
      </div>
      <div className={styles.OtpFormDescriptionContainer}>
        <p className={styles.OtpFormDescription}>{t('LoginOtpInputDescription', { phone: phoneNumber })}</p>
      </div>
      <form ref={formRef}>
        <div className={styles.OtpFormInputWrapper}>
          <div className={getClassName([styles.OtpFormInputItems, otp.length > 0 && `otp-filled-${otp.length}`])}>
            <div className={styles.OtpFormInputItem} />
            <div className={styles.OtpFormInputItem} />
            <div className={styles.OtpFormInputItem} />
            <div className={styles.OtpFormInputItem} />
            <div className={styles.OtpFormInputItem} />
          </div>
          <InputText
            validationContainerClassName={styles.OtpFormInputValidationContainer}
            containerClassName={styles.OtpFormInputContainer}
            className={styles.OtpFormInput}
            data-test-id="user.login.drawer.otp"
            name="otp"
            label="OTP"
            inputmode="numeric"
            rules={{ required: true, pattern: OTP_VALUE_PATTERN }}
            maxlength={OTP_MAX_LENGTH}
            placeholder="00000"
            value={otp}
            error={verifyOtpRequestError && t('ErrorOtpCodeVerificationFailed')}
            onChange={handleChangeOtp}
            onBlur={handleChangeOtp}
          />
        </div>
      </form>
      {isResendOtpEnabled ? (
        <div className={styles.OtpFormButtons}>
          <Button
            type="secondary"
            className={styles.OtpFormSMSResendButton}
            data-test-id="user.login.drawer.resend-sms-button"
            onClick={handleClickResendOtp}
          >
            {t('ResendSMSButtonText')}
          </Button>
          <Button
            type="text"
            theme="info"
            className={styles.OtpFormWhatsappResendButton}
            data-test-id="user.login.drawer.resend-whatsapp-button"
            onClick={handleClickResendWhatsapp}
          >
            {t('ResendWhatsappButtonText')}
          </Button>
        </div>
      ) : (
        <div className={styles.OtpFormCountDown}>
          <span>{t('OTPResendCountDownText', { currentOtpTime: `${resendOtpCountDownTime}s` })}</span>
        </div>
      )}
    </>
  );
};

OtpForm.displayName = 'OtpForm';

OtpForm.propTypes = {
  onCompleteReCAPTCHA: PropTypes.func,
};

OtpForm.defaultProps = {
  onCompleteReCAPTCHA: () => {},
};

export default OtpForm;
