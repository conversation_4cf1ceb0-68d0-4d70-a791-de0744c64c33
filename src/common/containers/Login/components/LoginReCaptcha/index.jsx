import React, { forwardRef } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { getIsRecaptchaEnabled, getGoogleRecaptchaSiteKey } from '../../redux/selectors';
import ReCAPTCHA from '../../../../components/ReCAPTCHA';
import { alert } from '../../../../utils/feedback';

const LoginReCaptcha = forwardRef((_, ref) => {
  const { t } = useTranslation();
  const isRecaptchaEnabled = useSelector(getIsRecaptchaEnabled);
  const googleRecaptchaSiteKey = useSelector(getGoogleRecaptchaSiteKey);

  if (!isRecaptchaEnabled) {
    return null;
  }

  const handleCaptchaLoad = () => {
    const hasLoadSuccess = !!window.grecaptcha;
    const scriptName = 'google-recaptcha';

    window.newrelic?.addPageAction(`third-party-lib.load-script-${hasLoadSuccess ? 'succeeded' : 'failed'}`, {
      scriptName,
    });

    if (!hasLoadSuccess) {
      alert(t('NetworkErrorDescription'), {
        title: t('NetworkErrorTitle'),
      });
    }
  };

  return (
    <ReCAPTCHA sitekey={googleRecaptchaSiteKey} size="invisible" ref={ref} asyncScriptOnLoad={handleCaptchaLoad} />
  );
});

LoginReCaptcha.displayName = 'LoginReCaptcha';

export default LoginReCaptcha;
