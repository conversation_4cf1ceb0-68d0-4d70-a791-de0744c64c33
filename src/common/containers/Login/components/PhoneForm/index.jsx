import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import beepLoginActiveImage from '../../../../../images/beep-login-active.svg';
import beepLoginDisabledImage from '../../../../../images/beep-login-disabled.png';
import {
  getCountries,
  getLoginPhoneNumber,
  getLoginPhoneCountry,
  getIsPhoneFormButtonLoading,
  getIsRecaptchaEnabled,
} from '../../redux/selectors';
import { actions as loginDrawerActions } from '../../redux';
import { clickContinueButton } from '../../redux/thunks';
import { ObjectFitImage } from '../../../../components/Image';
import Button from '../../../../components/Button';
import { PhoneNumberLabelInside } from '../../../../components/Input/PhoneNumber';
import styles from './PhoneForm.module.scss';

const PhoneForm = ({ onCompleteReCAPTCHA }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [isInvalid, setIsInvalid] = useState(false);
  const countries = useSelector(getCountries);
  const loginPhoneNumber = useSelector(getLoginPhoneNumber);
  const loginPhoneCountry = useSelector(getLoginPhoneCountry);
  const isPhoneFormButtonLoading = useSelector(getIsPhoneFormButtonLoading);
  const isRecaptchaEnabled = useSelector(getIsRecaptchaEnabled);
  const handleChangePhone = ({ phone }) => {
    dispatch(loginDrawerActions.phoneChanged(phone));
  };
  const handleChangeCountry = country => {
    dispatch(loginDrawerActions.countryChanged(country));
  };
  const handleValidation = useCallback(({ isInvalid: newIsInvalid }) => {
    setIsInvalid(newIsInvalid);
  }, []);
  const handleSubmitPhoneForm = useCallback(
    async event => {
      event.preventDefault();

      if (isInvalid) {
        return;
      }

      const payload = { phone: loginPhoneNumber, country: loginPhoneCountry };

      if (isRecaptchaEnabled) {
        payload.captchaToken = await onCompleteReCAPTCHA();
      }

      await dispatch(clickContinueButton(payload));
    },
    [isInvalid, dispatch, loginPhoneNumber, loginPhoneCountry, onCompleteReCAPTCHA, isRecaptchaEnabled]
  );

  return (
    <>
      <div className={styles.PhoneFormImageContainer}>
        <ObjectFitImage
          noCompression
          src={isInvalid ? beepLoginDisabledImage : beepLoginActiveImage}
          alt={`Beep user phone number ${isInvalid ? 'invalid' : 'valid'}`}
        />
      </div>
      <form className={styles.PhoneForm} onSubmit={handleSubmitPhoneForm}>
        <h4 className={styles.PhoneFormTitle}>{t('LoginPhoneNumberInputTitle')}</h4>
        <PhoneNumberLabelInside
          isLabelShown={false}
          label="phone number"
          className={styles.PhoneFormPhoneNumber}
          data-test-id="user.login.drawer.phone"
          name="phone"
          rules={{ required: true }}
          defaultPhone={loginPhoneNumber}
          defaultCountry={loginPhoneCountry}
          countries={countries}
          onChange={handleChangePhone}
          onBlur={handleChangePhone}
          onChangeCountry={handleChangeCountry}
          onValidation={handleValidation}
        />
        <Button
          block
          className="tw-uppercase"
          data-test-id="user.login.drawer.continue-button"
          disabled={isPhoneFormButtonLoading}
          loading={isPhoneFormButtonLoading}
          onClick={handleSubmitPhoneForm}
        >
          {t('Continue')}
        </Button>
      </form>
    </>
  );
};

PhoneForm.displayName = 'PhoneForm';

PhoneForm.propTypes = {
  onCompleteReCAPTCHA: PropTypes.func,
};

PhoneForm.defaultProps = {
  onCompleteReCAPTCHA: () => {},
};

export default PhoneForm;
