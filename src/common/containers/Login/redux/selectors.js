import _get from 'lodash/get';
import { createSelector } from 'reselect';
import config from '../../../../config';
import {
  COUNTRIES,
  API_REQUEST_STATUS,
  BEEP_OTP_ERROR_CODES,
  OTP_COMMON_ERROR_TYPES,
  OTP_ERROR_POPUP_I18N_KEYS,
  OTP_REQUEST_TYPES,
  OTP_SERVICE_ERROR_CODES,
  SMS_API_ERROR_CODES,
} from '../../../utils/constants';
import { ERROR_TYPES } from '../../../../utils/api/constants';
import {
  getIsCheckLoginRequestPending,
  getIsCheckLoginRequestRejected,
  getIsLoginRequestStatusPending,
  getIsLoginRequestStatusRejected,
} from '../../../../redux/modules/user/selectors';

export const getIsRecaptchaEnabled = () => config.recaptchaEnabled;

export const getGoogleRecaptchaSiteKey = () => config.googleRecaptchaSiteKey;

export const getCountries = () => Object.values(COUNTRIES);

export const getLoginPhoneNumber = state => state.login.phone.value;

export const getLoginPhoneCountry = state => state.login.phone.country || COUNTRIES.MY;

export const getOtpRequestType = state => state.login.otpRequestType;

export const getLoadIsPhoneWhatsAppSupportedRequestData = state => state.login.loadIsPhoneWhatsAppSupportedRequest.data;

export const getLoadIsPhoneWhatsAppSupportedRequestStatus = state =>
  state.login.loadIsPhoneWhatsAppSupportedRequest.status;

export const getLoadIsPhoneWhatsAppSupportedRequestError = state =>
  state.login.loadIsPhoneWhatsAppSupportedRequest.error;

export const getIsPhoneWhatsAppSupported = createSelector(
  getLoadIsPhoneWhatsAppSupportedRequestData,
  loadIsPhoneWhatsAppSupportedRequestData =>
    _get(loadIsPhoneWhatsAppSupportedRequestData, 'isPhoneWhatsAppSupported', false)
);

export const getSendOtpToUserRequestStatus = state => state.login.sendOtpToUserRequest.status;

export const getSendOtpToUserRequestError = state => state.login.sendOtpToUserRequest.error;

export const getVerifyOtpRequestData = state => state.login.verifyOtpRequest.data;

export const getVerifyOtpRequestStatus = state => state.login.verifyOtpRequest.status;

export const getVerifyOtpRequestError = state => state.login.verifyOtpRequest.error;

export const getVerifyOtpAccessToken = createSelector(getVerifyOtpRequestData, verifyOtpRequestData =>
  _get(verifyOtpRequestData, 'access_token', null)
);

export const getVerifyOtpRefreshToken = createSelector(getVerifyOtpRequestData, verifyOtpRequestData =>
  _get(verifyOtpRequestData, 'refresh_token', null)
);

/**
 * Derived selectors
 */
export const getIsSendOtpToUserRequestType = createSelector(
  getOtpRequestType,
  otpRequestType => otpRequestType === OTP_REQUEST_TYPES.OTP
);

export const getIsLoadIsPhoneWhatsAppSupportedRequestPending = createSelector(
  getLoadIsPhoneWhatsAppSupportedRequestStatus,
  status => status === API_REQUEST_STATUS.PENDING
);

export const getIsSendOtpToUserRequestPending = createSelector(
  getSendOtpToUserRequestStatus,
  status => status === API_REQUEST_STATUS.PENDING
);

export const getIsSendOtpToUserRequestFulfilled = createSelector(
  getSendOtpToUserRequestStatus,
  status => status === API_REQUEST_STATUS.FULFILLED
);

export const getIsSendOtpToUserRequestRejected = createSelector(
  getSendOtpToUserRequestStatus,
  status => status === API_REQUEST_STATUS.REJECTED
);

export const getIsPhoneFormButtonLoading = createSelector(
  getIsLoadIsPhoneWhatsAppSupportedRequestPending,
  getIsSendOtpToUserRequestPending,
  (isLoadIsPhoneWhatsAppSupportedRequestPending, isSendOtpToUserRequestPending) =>
    isLoadIsPhoneWhatsAppSupportedRequestPending || isSendOtpToUserRequestPending
);

export const getSendOtpAlertErrorI18nKey = createSelector(
  getIsSendOtpToUserRequestRejected,
  getIsSendOtpToUserRequestType,
  getSendOtpToUserRequestError,
  (isSendOtpToUserRequestRejected, isSendOtpToUserRequestType, sendOtpToUserRequestError) => {
    if (!sendOtpToUserRequestError) {
      return null;
    }

    const {
      code: sendOtpToUserRequestErrorCode,
      category: sendOtpToUserRequestErrorCategory,
    } = sendOtpToUserRequestError;
    const availableAlertErrorCodes = [
      BEEP_OTP_ERROR_CODES.PHONE_INVALID,
      OTP_SERVICE_ERROR_CODES.PHONE_INVALID,
      OTP_SERVICE_ERROR_CODES.MEET_DAY_LIMIT,
      OTP_SERVICE_ERROR_CODES.REQUEST_TOO_FAST,
      OTP_SERVICE_ERROR_CODES.HIGH_RISK,
      SMS_API_ERROR_CODES.NO_AVAILABLE_PROVIDER,
      SMS_API_ERROR_CODES.PHONE_INVALID,
    ];
    const availableAlertErrorCategories = [ERROR_TYPES.NETWORK_ERROR];

    if (!isSendOtpToUserRequestRejected) {
      return null;
    }

    if (!isSendOtpToUserRequestType) {
      return null;
    }

    return availableAlertErrorCodes.includes(sendOtpToUserRequestErrorCode)
      ? OTP_ERROR_POPUP_I18N_KEYS[sendOtpToUserRequestErrorCode]
      : availableAlertErrorCategories.includes(sendOtpToUserRequestErrorCategory)
      ? OTP_ERROR_POPUP_I18N_KEYS[sendOtpToUserRequestErrorCategory]
      : OTP_ERROR_POPUP_I18N_KEYS[OTP_COMMON_ERROR_TYPES.UNKNOWN_ERROR];
  }
);

export const getIsVerifyOtpRequestPending = createSelector(
  getVerifyOtpRequestStatus,
  status => status === API_REQUEST_STATUS.PENDING
);

export const getIsVerifyOtpRequestRejected = createSelector(
  getVerifyOtpRequestStatus,
  status => status === API_REQUEST_STATUS.REJECTED
);

export const getIsCompleteLoginFailed = createSelector(
  getIsVerifyOtpRequestRejected,
  getIsLoginRequestStatusRejected,
  getIsCheckLoginRequestRejected,
  (isVerifyOtpRequestRejected, isLoginRequestStatusRejected, isCheckLoginRequestRejected) =>
    isVerifyOtpRequestRejected || isLoginRequestStatusRejected || isCheckLoginRequestRejected
);

export const getIsLoginCompleteProcessing = createSelector(
  getIsVerifyOtpRequestPending,
  getIsLoginRequestStatusPending,
  getIsCheckLoginRequestPending,
  (isVerifyOtpRequestPending, isLoginRequestStatusPending, isCheckLoginRequestPending) =>
    isVerifyOtpRequestPending || isLoginRequestStatusPending || isCheckLoginRequestPending
);

// /**==================== Otp Error Popup ==================== */

// export const getIsOtpRequestStatusPending = createSelector(
//   getSendOtpToUserRequestStatus,
//   status => status === API_REQUEST_STATUS.PENDING
// );

// export const getShouldShowLoader = createSelector(
//   getIsSendOtpToUserRequestPending,
//   getIsLoginRequestStatusPending,
//   (isOtpRequestStatusPending, isLoginRequestStatusPending) => isOtpRequestStatusPending || isLoginRequestStatusPending
// );
