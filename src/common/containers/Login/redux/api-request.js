import { post } from '../../../../utils/api/api-fetch';
import config from '../../../../config';

export const postPhoneWhatsAppSupport = phone => post('/api/v3/otp/check-phone', { phone });

/**
 * Sends an OTP to the user's phone number
 * @param {Object} payload - The payload containing phone number and type
 * @param {string} payload.phone - The phone number to send OTP to
 * @param {string} payload.type - The type of OTP to send
 * @param {string} payload.platform - The platform to send OTP to
 * @param {string} payload.captchaToken - The captcha token
 * @param {string} payload.siteKey - The site key for reCAPTCHA
 * @returns {Promise<Object>} The response from the API
 */
export const postSendOtp = payload => post('/api/v3/otp', payload);

/**
 * Verifies an OTP
 * @param {Object} payload - The payload containing OTP
 * @param {string} payload.grant_type - The grant type
 * @param {string} payload.client - The client
 * @param {string} payload.business_name - The business name
 * @param {string} payload.username - The username
 * @param {string} payload.password - The password (OTP)
 * @returns {Promise<Object>} The response from the API
 */
export const postVerifyOtp = payload =>
  post(config.authApiUrl, payload, {
    mode: 'cors',
    credentials: 'omit',
  });
