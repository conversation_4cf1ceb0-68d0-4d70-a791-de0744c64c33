import { createAsyncThunk } from '@reduxjs/toolkit';
import { AUTH_INFO, OTP_REQUEST_PLATFORM, OTP_REQUEST_TYPES } from '../../../utils/constants';
import { KEY_EVENTS_FLOWS, KEY_EVENTS_STEPS } from '../../../../utils/monitoring/constants';
import { getLocalStorageVariable, setLocalStorageVariable } from '../../../utils';
import CleverTap from '../../../../common/utils/analytics/clevertap/index';
import logger from '../../../../utils/monitoring/logger';
import { getCountryKeyByPhone } from '../utils';
import { postPhoneWhatsAppSupport, postSendOtp, postVerifyOtp } from './api-request';
import { getMerchantBusiness } from '../../../../redux/modules/merchant/selectors';
import { initUserInfo, syncUserLoginInfo } from '../../../../redux/modules/user/thunks';
import {
  getIsRecaptchaEnabled,
  getGoogleRecaptchaSiteKey,
  getLoginPhoneNumber,
  getVerifyOtpAccessToken,
  getVerifyOtpRefreshToken,
  getIsVerifyOtpRequestRejected,
} from './selectors';
import { getUserPhoneNumber } from '../../../../redux/modules/user/selectors';

export const setLocalStorageValidPhoneNumber = createAsyncThunk(
  'login/setLocalStorageValidPhoneNumber',
  async phone => {
    setLocalStorageVariable('user.p', phone);

    return phone;
  }
);

export const getLocalStorageValidPhoneNumber = createAsyncThunk('login/getLocalStorageValidPhoneNumber', async () =>
  getLocalStorageVariable('user.p')
);

export const setLocalStorageValidCountry = createAsyncThunk('login/setLocalStorageValidCountry', async country => {
  setLocalStorageVariable('user.country', country);

  return country;
});

export const getLocalStorageValidCountry = createAsyncThunk('login/getLocalStorageValidCountry', async () =>
  getLocalStorageVariable('user.country')
);

/**
 * Updates the OTP request type
 * @param {Object} params Parameters for the action
 * @param {string} params.type The OTP request type to update to
 */
export const updateOtpRequestType = createAsyncThunk('login/updateOtpRequestType', async type => type);

/**
 * Fetches whether a phone number supports WhatsApp
 * @param {Object} params Parameters for the action
 * @param {string} params.phone The phone number to check
 */
export const fetchIsPhoneWhatsAppSupported = createAsyncThunk('login/fetchIsPhoneWhatsAppSupported', async phone => {
  const result = await postPhoneWhatsAppSupport(phone);

  return result;
});

/**
 * Sends OTP to user's phone number
 * @param {Object} params Parameters for the action
 * @param {string} params.phone The phone number to send OTP to
 */
export const sendOtpToUser = createAsyncThunk(
  'login/sendOtpToUser',
  async ({ captchaToken, siteKey, type = OTP_REQUEST_TYPES.OTP }, { dispatch, getState, rejectWithValue }) => {
    try {
      const state = getState();
      const phoneNumber = getLoginPhoneNumber(state);
      const payload = {
        phone: phoneNumber,
        type,
        platform: OTP_REQUEST_PLATFORM,
        captchaToken,
        siteKey,
      };

      await dispatch(updateOtpRequestType(payload.type));

      const result = await postSendOtp(payload);

      return result;
    } catch (error) {
      throw rejectWithValue(error);
    }
  }
);

export const verifyOtp = createAsyncThunk('login/verifyOtp', async (otp, { getState, rejectWithValue }) => {
  const state = getState();
  const phoneNumber = getLoginPhoneNumber(state);
  const merchantBusiness = getMerchantBusiness(state);

  if (!phoneNumber || typeof phoneNumber !== 'string') {
    throw new Error('Phone number is required and must be a string');
  }

  try {
    const result = await postVerifyOtp({
      grant_type: AUTH_INFO.GRANT_TYPE,
      client: AUTH_INFO.CLIENT,
      business_name: merchantBusiness,
      username: phoneNumber,
      password: otp,
    });

    return result;
  } catch (error) {
    logger.error(
      'Login_VerifyOtpFailed',
      { message: error?.message },
      {
        bizFlow: {
          flow: KEY_EVENTS_FLOWS.LOGIN,
          step: KEY_EVENTS_STEPS[KEY_EVENTS_FLOWS.LOGIN].SUBMIT_OTP,
        },
      }
    );

    throw rejectWithValue(error);
  }
});

/**
 * Creates a thunk action creator for handling login continue button click
 * @param {Object} params Parameters for the action
 * @param {string} params.phone The phone number entered by user
 * @param {string} params.captchaToken The captcha token
 */
export const clickContinueButton = createAsyncThunk(
  'login/clickContinueButton',
  async ({ phone, country, captchaToken }, { dispatch, getState }) => {
    if (!phone || typeof phone !== 'string') {
      throw new Error('Phone number is required and must be a string');
    }

    logger.log('Login_ClickContinueButton');
    CleverTap.pushEvent('Login - Continue');
    dispatch(setLocalStorageValidPhoneNumber(phone));
    dispatch(setLocalStorageValidCountry(country));

    const state = getState();
    const isRecaptchaEnabled = getIsRecaptchaEnabled(state);
    const googleRecaptchaSiteKey = getGoogleRecaptchaSiteKey(state);
    let siteKey;

    try {
      // Set reCAPTCHA payload
      if (isRecaptchaEnabled) {
        siteKey = googleRecaptchaSiteKey;
      }

      // Check WhatsApp support in parallel
      dispatch(fetchIsPhoneWhatsAppSupported(phone));

      // Send OTP to user's phone
      await dispatch(sendOtpToUser({ captchaToken, siteKey }));
    } catch (error) {
      // Log error with detailed context
      logger.error(
        'Login_SubmitPhoneNumberFailed',
        { message: error?.message },
        {
          bizFlow: {
            flow: KEY_EVENTS_FLOWS.LOGIN,
            step: KEY_EVENTS_STEPS[KEY_EVENTS_FLOWS.LOGIN].RECEIVE_OTP,
          },
          errorCategory: error?.category,
        }
      );

      throw error;
    }
  }
);

/**
 * Resends OTP to user's phone number
 * @param {Object} params Parameters for the action
 * @param {string} params.captchaToken The captcha token
 */
export const clickResendOtpButton = createAsyncThunk(
  'login/clickResendOtpButton',
  async (captchaToken, { dispatch, getState }) => {
    const state = getState();
    const phoneNumber = getLoginPhoneNumber(state);

    if (!phoneNumber || typeof phoneNumber !== 'string') {
      throw new Error('Phone number is required and must be a string');
    }

    logger.log('Login_ResendOtp', { type: OTP_REQUEST_TYPES.RE_SEND_OTP });
    CleverTap.pushEvent('Login - Resend OTP', { type: OTP_REQUEST_TYPES.RE_SEND_OTP });

    const isRecaptchaEnabled = getIsRecaptchaEnabled(state);
    const googleRecaptchaSiteKey = getGoogleRecaptchaSiteKey(state);
    let siteKey;

    try {
      if (isRecaptchaEnabled) {
        siteKey = googleRecaptchaSiteKey;
      }

      await dispatch(sendOtpToUser({ captchaToken, siteKey, type: OTP_REQUEST_TYPES.RE_SEND_OTP }));
    } catch (error) {
      // Log error with detailed context
      logger.error(
        'Login_ResendOtpFailed',
        { message: error?.message },
        {
          bizFlow: {
            flow: KEY_EVENTS_FLOWS.LOGIN,
            step: KEY_EVENTS_STEPS[KEY_EVENTS_FLOWS.LOGIN].RECEIVE_OTP,
          },
          errorCategory: error?.category,
        }
      );

      throw error;
    }
  }
);

/**
 * Resends OTP to user's phone number
 * @param {Object} params Parameters for the action
 * @param {string} params.captchaToken The captcha token
 */
export const clickResendWhatsappButton = createAsyncThunk(
  'login/clickResendWhatsappButton',
  async (_, { dispatch, getState }) => {
    const state = getState();
    const phoneNumber = getLoginPhoneNumber(state);

    if (!phoneNumber || typeof phoneNumber !== 'string') {
      throw new Error('Phone number is required and must be a string');
    }

    logger.log('Login_ResendWhatsapp', { type: OTP_REQUEST_TYPES.WHATSAPP });
    CleverTap.pushEvent('Login - Resend WhatsApp', { type: OTP_REQUEST_TYPES.WHATSAPP });

    try {
      await dispatch(sendOtpToUser({ type: OTP_REQUEST_TYPES.WHATSAPP }));
    } catch (error) {
      // Log error with detailed context
      logger.error(
        'Login_ResendWhatsappFailed',
        { message: error?.message },
        {
          bizFlow: {
            flow: KEY_EVENTS_FLOWS.LOGIN,
            step: KEY_EVENTS_STEPS[KEY_EVENTS_FLOWS.LOGIN].RECEIVE_OTP,
          },
          errorCategory: error?.category,
        }
      );

      throw error;
    }
  }
);

export const completeLogin = createAsyncThunk('login/completeLogin', async (otp, { dispatch, getState }) => {
  try {
    await dispatch(verifyOtp(otp));

    const isVerifyOtpRequestRejected = getIsVerifyOtpRequestRejected(getState());

    if (isVerifyOtpRequestRejected) {
      throw new Error('Verify OTP request rejected');
    }

    const accessToken = getVerifyOtpAccessToken(getState());
    const refreshToken = getVerifyOtpRefreshToken(getState());

    await dispatch(syncUserLoginInfo({ accessToken, refreshToken }));
  } catch (error) {
    logger.error(
      'Login_CompleteLoginFailed',
      { message: error?.message },
      {
        bizFlow: {
          flow: KEY_EVENTS_FLOWS.LOGIN,
          step: KEY_EVENTS_STEPS[KEY_EVENTS_FLOWS.LOGIN].SIGN_INTO_APP,
        },
      }
    );

    throw error;
  }
});

export const mounted = createAsyncThunk('login/mounted', async (_, { dispatch, getState }) => {
  dispatch(getLocalStorageValidPhoneNumber());
  dispatch(getLocalStorageValidCountry());

  await dispatch(initUserInfo());

  const phoneNumber = getUserPhoneNumber(getState());

  if (phoneNumber) {
    const countryKey = getCountryKeyByPhone(phoneNumber);

    dispatch(setLocalStorageValidPhoneNumber(phoneNumber));
    dispatch(setLocalStorageValidCountry(countryKey));
  }
});
