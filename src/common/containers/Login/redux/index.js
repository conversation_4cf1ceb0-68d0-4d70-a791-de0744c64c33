import { createSlice } from '@reduxjs/toolkit';
import { COUNTRIES, API_REQUEST_STATUS, OTP_REQUEST_TYPES } from '../../../utils/constants';
import {
  setLocalStorageValidPhoneNumber,
  getLocalStorageValidPhoneNumber,
  setLocalStorageValidCountry,
  getLocalStorageValidCountry,
  updateOtpRequestType,
  fetchIsPhoneWhatsAppSupported,
  sendOtpToUser,
  verifyOtp,
} from './thunks';

const initialState = {
  phone: {
    value: null,
    country: COUNTRIES.MY,
  },
  otpRequestType: OTP_REQUEST_TYPES.OTP,
  loadIsPhoneWhatsAppSupportedRequest: {
    data: null,
    status: null,
    error: null,
  },
  sendOtpToUserRequest: {
    status: null,
    error: null,
  },
  verifyOtpRequest: {
    data: null,
    status: null,
    error: null,
  },
};

export const { actions, reducer } = createSlice({
  name: 'user/login',
  initialState,
  reducers: {
    phoneChanged: (state, { payload }) => {
      state.phone.value = payload;
    },
    countryChanged: (state, { payload }) => {
      state.phone.country = payload;
    },
    stateReset: () => initialState,
  },
  extraReducers: {
    [setLocalStorageValidPhoneNumber.fulfilled.type]: (state, { payload }) => {
      state.phone.value = payload;
    },
    [getLocalStorageValidPhoneNumber.fulfilled.type]: (state, { payload }) => {
      state.phone.value = payload;
    },
    [setLocalStorageValidCountry.fulfilled.type]: (state, { payload }) => {
      state.phone.country = payload;
    },
    [getLocalStorageValidCountry.fulfilled.type]: (state, { payload }) => {
      state.phone.country = payload;
    },
    [updateOtpRequestType.fulfilled.type]: (state, { payload }) => {
      state.otpRequestType = payload;
    },
    [fetchIsPhoneWhatsAppSupported.pending.type]: state => {
      state.loadIsPhoneWhatsAppSupportedRequest.status = API_REQUEST_STATUS.PENDING;
      state.loadIsPhoneWhatsAppSupportedRequest.error = null;
    },
    [fetchIsPhoneWhatsAppSupported.fulfilled.type]: (state, { payload }) => {
      state.loadIsPhoneWhatsAppSupportedRequest.data = payload;
      state.loadIsPhoneWhatsAppSupportedRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loadIsPhoneWhatsAppSupportedRequest.error = null;
    },
    [fetchIsPhoneWhatsAppSupported.rejected.type]: (state, { error }) => {
      state.loadIsPhoneWhatsAppSupportedRequest.status = API_REQUEST_STATUS.REJECTED;
      state.loadIsPhoneWhatsAppSupportedRequest.error = error;
    },
    [sendOtpToUser.pending.type]: state => {
      state.sendOtpToUserRequest.status = API_REQUEST_STATUS.PENDING;
      state.sendOtpToUserRequest.error = null;
    },
    [sendOtpToUser.fulfilled.type]: state => {
      state.sendOtpToUserRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.sendOtpToUserRequest.error = null;
    },
    [sendOtpToUser.rejected.type]: (state, { payload }) => {
      state.sendOtpToUserRequest.status = API_REQUEST_STATUS.REJECTED;
      state.sendOtpToUserRequest.error = payload;
    },
    [verifyOtp.pending.type]: state => {
      state.verifyOtpRequest.status = API_REQUEST_STATUS.PENDING;
      state.verifyOtpRequest.error = null;
    },
    [verifyOtp.fulfilled.type]: (state, { payload }) => {
      state.verifyOtpRequest.data = payload;
      state.verifyOtpRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.verifyOtpRequest.error = null;
    },
    [verifyOtp.rejected.type]: (state, { payload }) => {
      state.verifyOtpRequest.status = API_REQUEST_STATUS.REJECTED;
      state.verifyOtpRequest.error = payload;
    },
  },
});

export default reducer;
