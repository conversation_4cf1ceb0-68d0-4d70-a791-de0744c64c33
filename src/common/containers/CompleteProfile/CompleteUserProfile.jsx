import React from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import CompleteProfileImage from '../../../images/complete-profile.svg';
import HeroSection from './components/HeroSection';
import CompleteProfileForm from './components/CompleteProfileForm';
import styles from './CompleteUserProfile.module.scss';

const CompleteUserProfile = ({
  disableBirthdayPicker,
  showSkipButton,
  showLogoutButton,
  onSkip,
  onSave,
  onClose,
  onLogout,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <HeroSection
        title={t('CompleteProfileTile')}
        image={CompleteProfileImage}
        description={<p className={styles.CompleteUserProfileDescription}>{t('CompleteProfileDescription')}</p>}
      />
      <CompleteProfileForm
        disableBirthdayPicker={disableBirthdayPicker}
        showSkipButton={showSkipButton}
        showLogoutButton={showLogoutButton}
        onClickSkipButton={onSkip}
        onClickSaveButton={onSave}
        onClose={onClose}
        onLogout={onLogout}
      />
    </>
  );
};

CompleteUserProfile.displayName = 'CompleteUserProfile';

CompleteUserProfile.propTypes = {
  disableBirthdayPicker: PropTypes.bool,
  showSkipButton: PropTypes.bool,
  showLogoutButton: PropTypes.bool,
  onSkip: PropTypes.func,
  onSave: PropTypes.func,
  onClose: PropTypes.func,
  onLogout: PropTypes.func,
};

CompleteUserProfile.defaultProps = {
  disableBirthdayPicker: true,
  showSkipButton: true,
  showLogoutButton: false,
  onSkip: () => {},
  onSave: () => {},
  onClose: () => {},
  onLogout: () => {},
};

export default CompleteUserProfile;
