import React, { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import logger from '../../../../../utils/monitoring/logger';
import { getIsUserProfileEmpty, getIsLogoutRequestPending } from '../../../../../redux/modules/user/selectors';
import {
  getProfileBirthday,
  getProfileEmail,
  getProfileFirstName,
  getProfileLastName,
  getIsUpdateProfileRequestStatusFulfilled,
  getIsUpdateProfileRequestStatusPending,
  getIsCompleteProfileFormButtonDisabled,
} from '../../redux/selectors';
import { actions as completeProfileActions } from '../../redux';
import { saveUserProfileInfo } from '../../redux/thunks';
import { logoutUser } from '../../../../../redux/modules/user/thunks';
import InputText from '../../../../components/Input/Text';
import InputEmail from '../../../../components/Input/Email';
import InputBirthday from '../../../../components/Input/Birthday';
import PageFooter from '../../../../components/PageFooter';
import Button from '../../../../components/Button';
import { toast } from '../../../../utils/feedback';
import styles from './CompleteProfileForm.module.scss';

const CompleteProfileForm = ({
  disableBirthdayPicker,
  showSkipButton,
  showLogoutButton,
  onClickSkipButton,
  onClickSaveButton,
  onClose,
  onLogout,
}) => {
  const { t } = useTranslation(['Profile']);
  const dispatch = useDispatch();
  const isUserProfileEmpty = useSelector(getIsUserProfileEmpty);
  const firstName = useSelector(getProfileFirstName);
  const lastName = useSelector(getProfileLastName);
  const email = useSelector(getProfileEmail);
  const birthday = useSelector(getProfileBirthday);
  const isUpdateProfileRequestStatusPending = useSelector(getIsUpdateProfileRequestStatusPending);
  const isUpdateProfileRequestStatusFulfilled = useSelector(getIsUpdateProfileRequestStatusFulfilled);
  const isCompleteProfileFormButtonDisabled = useSelector(getIsCompleteProfileFormButtonDisabled);
  const isLogoutRequestPending = useSelector(getIsLogoutRequestPending);
  const [invalidFields, setInvalidFields] = useState([]);
  const handleValidation = useCallback(
    ({ name, inInvalid }) => {
      if (inInvalid && !invalidFields.includes(name)) {
        setInvalidFields([...invalidFields, name]);
      } else if (!inInvalid && invalidFields.includes(name)) {
        const filteredInvalidFields = invalidFields.filter(invalidField => invalidField !== name);

        setInvalidFields(filteredInvalidFields);
      }
    },
    [invalidFields]
  );
  const handleUpdateFirstName = useCallback(
    targetFirstName => {
      dispatch(completeProfileActions.firstNameUpdated(targetFirstName));
    },
    [dispatch]
  );
  const handleUpdateLastName = useCallback(
    targetLastName => {
      dispatch(completeProfileActions.lastNameUpdated(targetLastName));
    },
    [dispatch]
  );
  const handleUpdateEmail = useCallback(
    targetEmail => {
      dispatch(completeProfileActions.emailUpdated(targetEmail));
    },
    [dispatch]
  );
  const handleUpdateBirthday = useCallback(
    targetBirthday => {
      dispatch(completeProfileActions.birthdayUpdated(targetBirthday));
    },
    [dispatch]
  );
  const handleSkipForm = useCallback(() => {
    onClickSkipButton && onClickSkipButton();
    onClose();
  }, [onClickSkipButton, onClose]);
  const handleSaveForm = useCallback(
    async event => {
      event.preventDefault();

      try {
        if (invalidFields.length > 0) {
          return;
        }

        await dispatch(saveUserProfileInfo()).unwrap();

        onClickSaveButton && onClickSaveButton();
        onClose();
      } catch (error) {
        logger.error('Common_CompleteProfile_Form_SaveFormFailed', { message: error?.message });
      }
    },
    [dispatch, onClickSaveButton, onClose, invalidFields]
  );

  const handleClickLogoutButton = useCallback(async () => {
    try {
      await dispatch(logoutUser());
      onLogout();
    } catch (error) {
      logger.error('Common_CompleteProfile_Form_LogoutButtonFailed', { message: error?.message });
    }
  }, [dispatch, onLogout]);

  useEffect(() => {
    if (isUpdateProfileRequestStatusFulfilled) {
      toast.success(t('SaveProfileSuccess'));
    }
  }, [isUpdateProfileRequestStatusFulfilled, t]);

  return (
    <form className={styles.CompleteProfileForm} onSubmit={handleSaveForm}>
      <section className={styles.CompleteProfileFieldsSection}>
        <InputText
          data-test-id="profile.complete-form.first-name"
          label={t('FirstName')}
          name="firstName"
          rules={{ required: true }}
          value={firstName}
          onChange={handleUpdateFirstName}
          onBlur={handleUpdateFirstName}
          onValidation={handleValidation}
        />
        <InputText
          data-test-id="profile.complete-form.last-name"
          label={t('LastName')}
          name="lastName"
          value={lastName}
          onChange={handleUpdateLastName}
          onBlur={handleUpdateLastName}
          onValidation={handleValidation}
        />
        <InputEmail
          data-test-id="profile.complete-form.email"
          name="email"
          rules={{ required: true }}
          value={email}
          onChange={handleUpdateEmail}
          onBlur={handleUpdateEmail}
          onValidation={handleValidation}
        />
        <InputBirthday
          data-test-id="profile.complete-form.birthday"
          name="birthday"
          rules={{ required: true }}
          disabled={disableBirthdayPicker}
          value={birthday}
          onChange={handleUpdateBirthday}
          onBlur={handleUpdateBirthday}
          onValidation={handleValidation}
        />
      </section>

      <PageFooter className={styles.CompleteProfileFormPageFooter}>
        <div className={styles.CompleteProfileFormFooter}>
          {showSkipButton && (
            <Button
              type="secondary"
              className={styles.CompleteProfileFormFooterButton}
              data-test-id="profile.complete-form.skip-for-now-button"
              buttonType="button"
              onClick={handleSkipForm}
            >
              {t('SkipForNow')}
            </Button>
          )}
          <Button
            data-test-id="profile.complete-form.save-button"
            className={styles.CompleteProfileFormFooterButton}
            loading={isUpdateProfileRequestStatusPending}
            disabled={isCompleteProfileFormButtonDisabled}
          >
            {isUserProfileEmpty ? t('Complete') : t('Save')}
          </Button>
        </div>
        {showLogoutButton && (
          <div className={styles.CompleteProfileFormLogoutContainer}>
            <Button
              type="text"
              className={styles.CompleteProfileFormLogoutButton}
              buttonType="button"
              data-test-id="profile.complete-form.logout-button"
              loading={isLogoutRequestPending}
              disabled={isCompleteProfileFormButtonDisabled}
              onClick={handleClickLogoutButton}
            >
              {t('Logout')}
            </Button>
          </div>
        )}
      </PageFooter>
    </form>
  );
};

CompleteProfileForm.displayName = 'CompleteProfileForm';

CompleteProfileForm.propTypes = {
  disableBirthdayPicker: PropTypes.bool,
  showSkipButton: PropTypes.bool,
  showLogoutButton: PropTypes.bool,
  onClickSkipButton: PropTypes.func,
  onClickSaveButton: PropTypes.func,
  onClose: PropTypes.func,
  onLogout: PropTypes.func,
};

CompleteProfileForm.defaultProps = {
  disableBirthdayPicker: true,
  showSkipButton: true,
  showLogoutButton: false,
  onClickSkipButton: () => {},
  onClickSaveButton: () => {},
  onClose: () => {},
  onLogout: () => {},
};

export default CompleteProfileForm;
