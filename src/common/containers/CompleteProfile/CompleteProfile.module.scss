.CompleteProfileDrawer {
  @apply tw-p-0;
}

.CompleteProfileDrawerContent {
  @apply tw-flex tw-flex-col;
}

.CompleteProfileHeader {
  @apply tw-border-b-0 tw-bg-blue-darkest tw-text-gray-50;

  min-height: 50px;
}

.CompleteProfileHeaderTitle {
  @apply tw-text-gray-50;
}

.CompleteProfileHeaderCloseButton {
  @apply tw-p-16 sm:tw-p-16px tw--mx-8 sm:tw--mx-8px tw-text-gray-50;

  &:hover {
    @apply tw-text-gray-50;
  }

  &:active {
    @apply tw-text-gray-50;
  }
}

.CompleteProfileHeaderCloseButtonContent {
  @apply tw-flex tw-items-center tw-justify-center tw-text-gray-50;
}

.CompleteProfileHeaderLogoutButton {
  @apply tw-p-16 sm:tw-p-16px tw--mx-8 sm:tw--mx-8px tw-text-gray-50;

  &:hover {
    @apply tw-text-gray-50;
  }

  &:active {
    @apply tw-text-gray-50;
  }
}

.CompleteProfileHeaderLogoutButtonContent {
  @apply tw-flex tw-items-center tw-justify-center tw-text-gray-50;
}
