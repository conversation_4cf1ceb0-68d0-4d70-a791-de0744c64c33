import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { X } from 'phosphor-react';
import { isWebview } from '../../utils';
import CleverTap from '../../../common/utils/analytics/clevertap/index';
import {
  getUploadProfileError,
  getIsUploadProfileEmailDuplicateError,
  getIsUserBirthdayChangeAllowed,
} from '../../../redux/modules/user/selectors';
import {
  getIsUpdateBirthdayRequestShow,
  getIsProfileRequestMountStatusPending,
  getIsProcessingShow,
  getIsBirthdayEmpty,
} from './redux/selectors';
import { actions as completeProfileActions } from './redux';
import { mount } from './redux/thunks';
import Drawer from '../../components/Drawer';
import DrawerHeader from '../../components/Drawer/DrawerHeader';
import Button from '../../components/Button';
import PageToast from '../../components/PageToast';
import Loader from '../../components/Loader';
import { alert } from '../../utils/feedback';
import SkeletonLoader from './components/SkeletonLoader';
import CompleteUserProfile from './CompleteUserProfile';
import CompleteBirthday from './CompleteBirthday';
import styles from './CompleteProfile.module.scss';

const CompleteProfile = ({
  show,
  showHeader,
  showSkipButton,
  showLogoutButton,
  isCompleteBirthdayFirst,
  headerTitle,
  onSkipBirthday,
  onSaveBirthday,
  onSkip,
  onSave,
  onClose,
  onLogout,
}) => {
  const { t } = useTranslation(['Profile']);
  const dispatch = useDispatch();
  const uploadProfileError = useSelector(getUploadProfileError);
  const isUploadProfileEmailDuplicateError = useSelector(getIsUploadProfileEmailDuplicateError);
  const isUpdateBirthdayRequestShow = useSelector(getIsUpdateBirthdayRequestShow);
  const isProfileRequestMountStatusPending = useSelector(getIsProfileRequestMountStatusPending);
  const isProcessingShow = useSelector(getIsProcessingShow);
  const isBirthdayEmpty = useSelector(getIsBirthdayEmpty);
  const isUserBirthdayChangeAllowed = useSelector(getIsUserBirthdayChangeAllowed);
  const isBirthdayPickerDisabled = (isCompleteBirthdayFirst && !isBirthdayEmpty) || !isUserBirthdayChangeAllowed;

  useEffect(() => {
    if (show) {
      dispatch(mount(isCompleteBirthdayFirst));
    } else {
      dispatch(completeProfileActions.stateReset());
    }
  }, [show, isCompleteBirthdayFirst, dispatch]);

  useEffect(() => {
    if (uploadProfileError && isUploadProfileEmailDuplicateError) {
      alert(t('ErrorDuplicatedEmailAlertEmail'), {
        closeButtonContent: t('ErrorDuplicatedEmailAlertBackToEdit'),
        title: t('ErrorDuplicatedEmailAlertTitle'),
        onClose: () => {
          CleverTap.pushEvent('Complete profile page email duplicate pop up - Click back to edit');
          dispatch(completeProfileActions.birthdayUpdated(''));
        },
      });
    } else if (uploadProfileError) {
      alert(t('ErrorOtpCodeVerificationFailedDescription'), {
        title: t('ErrorOtpCodeVerificationFailedTitle'),
      });
    }
  }, [uploadProfileError, isUploadProfileEmailDuplicateError, t, dispatch]);

  if (isWebview()) {
    return null;
  }

  return (
    <Drawer
      className={styles.CompleteProfileDrawer}
      childrenClassName={styles.CompleteProfileDrawerContent}
      fullScreen
      show={show}
      header={
        showHeader && (
          <DrawerHeader
            className={styles.CompleteProfileHeader}
            titleClassName={styles.CompleteProfileHeaderTitle}
            left={
              <Button
                className={styles.CompleteProfileHeaderCloseButton}
                contentClassName={styles.CompleteProfileHeaderCloseButtonContent}
                type="text"
                onClick={onClose}
                data-test-id="common.complete-profile.header.close-button"
              >
                <X weight="light" size={24} />
              </Button>
            }
          >
            {headerTitle}
          </DrawerHeader>
        )
      }
      onClose={onClose}
    >
      {isProfileRequestMountStatusPending ? (
        <SkeletonLoader />
      ) : isUpdateBirthdayRequestShow ? (
        <CompleteBirthday onSkip={onSkipBirthday} onSave={onSaveBirthday} />
      ) : (
        <CompleteUserProfile
          disableBirthdayPicker={isBirthdayPickerDisabled}
          showSkipButton={showSkipButton}
          showLogoutButton={showLogoutButton}
          onSkip={onSkip}
          onSave={onSave}
          onClose={onClose}
          onLogout={onLogout}
        />
      )}
      {isProcessingShow && (
        <PageToast icon={<Loader className="tw-m-8 sm:tw-m-8px" size={30} />}>{`${t('Processing')}...`}</PageToast>
      )}
    </Drawer>
  );
};

CompleteProfile.displayName = 'CompleteProfile';

CompleteProfile.propTypes = {
  show: PropTypes.bool,
  showHeader: PropTypes.bool,
  showSkipButton: PropTypes.bool,
  showLogoutButton: PropTypes.bool,
  isCompleteBirthdayFirst: PropTypes.bool,
  headerTitle: PropTypes.string,
  onSkipBirthday: PropTypes.func,
  onSaveBirthday: PropTypes.func,
  onSkip: PropTypes.func,
  onSave: PropTypes.func,
  onClose: PropTypes.func,
  onLogout: PropTypes.func,
};

CompleteProfile.defaultProps = {
  show: false,
  showHeader: false,
  showSkipButton: true,
  showLogoutButton: false,
  isCompleteBirthdayFirst: false,
  headerTitle: '',
  onSkipBirthday: () => {},
  onSaveBirthday: () => {},
  onSkip: () => {},
  onSave: () => {},
  onClose: () => {},
  onLogout: () => {},
};

export default CompleteProfile;
