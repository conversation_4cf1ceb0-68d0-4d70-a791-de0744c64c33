import qs from 'qs';
import {
  SSE_CLOSE_SOURCES,
  SSE_CONNECT_SOURCES,
  SSE_DEFAULT_QUERY_PARAMS,
  SSE_ERROR_TYPES,
  SSE_TOPICS,
} from './constants';
import { getUUID } from '..';
import logger from '../../../utils/monitoring/logger';

const getInvalidQueryParamsType = queryParams => {
  const availableSseTopics = Object.values(SSE_TOPICS);
  // eventSource can't get response body
  // If query has error, only from options to confirm
  const { topics = '', subscriberGroups = '' } = queryParams;
  const invalidTopics = topics.filter(topic => !availableSseTopics.includes(topic));
  const [, storeId, tableId] = subscriberGroups.split(':');

  if (!topics || topics.length === 0) {
    return SSE_ERROR_TYPES.TOPICS_REQUIRED;
  }

  if (invalidTopics?.length > 0) {
    return SSE_ERROR_TYPES.TOPICS_INVALID;
  }

  if (!storeId) {
    return SSE_ERROR_TYPES.STORE_ID_REQUIRED;
  }

  if (!tableId) {
    return SSE_ERROR_TYPES.TABLE_ID_REQUIRED;
  }

  return null;
};

/**
 *
 * @param {string} url
 * @param {object} queryParams
 */
export const getSSEUrl = (url, queryParams) => {
  const urlObj = new URL(url);
  const urlParams = qs.parse(urlObj.search, { ignoreQueryPrefix: true });
  const queryObject = { ...urlParams, ...SSE_DEFAULT_QUERY_PARAMS, ...queryParams };

  urlObj.search = qs.stringify(queryObject, { indices: false });

  return urlObj.toString();
};
class SSEClient {
  constructor(url, options = {}) {
    const { queryParams = {}, maxReconnectAttempts = 5, reconnectInterval = 3000, onError } = options || {};

    this.url = getSSEUrl(url, queryParams);
    this.queryParams = queryParams;
    this.eventSource = null;
    this.listeners = new Map();
    this.reconnectAttempts = 0; // retry connection times
    this.maxReconnectAttempts = maxReconnectAttempts; // retry connection max times
    this.reconnectInterval = reconnectInterval; // The retry interval for reconnection（ms）
    this.isVisible = true; // The tab visibility is set to visible by default
    this.onError = onError;
    this.uuid = null;

    // listening the tab visibility
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
  }

  handleVisibilityChange() {
    this.isVisible = !document.hidden;

    // If the tab is visible, connect server
    // If the tab is not visible, disconnect server
    this.isVisible ? this.connect(SSE_CONNECT_SOURCES.VISIBLE) : this.close(SSE_CLOSE_SOURCES.HIDDEN);
  }

  connect(from = SSE_CONNECT_SOURCES.DEFAULT) {
    if (this.eventSource || !this.isVisible) {
      return; // If eventSource existed or tab is not visible, not continue connect
    }

    this.uuid = getUUID();
    logger.log('SSE_Connecting', { from, uuid: this.uuid });

    this.eventSource = new EventSource(this.url, { withCredentials: true });

    this.resetEventListeners();

    this.eventSource.onopen = () => {
      logger.log('SSE_Connection_Opened', {
        uuid: this.uuid,
      });

      // reset reconnectAttempts 0 must after addEventListener
      // If connection successful, reset retry connection times
      if ([SSE_CONNECT_SOURCES.DEFAULT, SSE_CONNECT_SOURCES.VISIBLE].includes(from)) {
        this.reconnectAttempts = 0;
      }
    };

    this.eventSource.onerror = error => {
      logger.info('SSE_Connection_Error', error);

      const { currentTarget } = error;
      const { readyState } = currentTarget;
      const invalidQueryParamsType = getInvalidQueryParamsType(this.queryParams);

      if (invalidQueryParamsType) {
        logger.error('SSE_QueryParams_Error', {
          type: invalidQueryParamsType,
          readyState,
        });

        this.onError && this.onError({ type: invalidQueryParamsType });

        return;
      }

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts += 1;

        logger.log('SSE_Attempting_To_Reconnect', { reconnectAttempts: this.reconnectAttempts });

        // disconnect current connection
        this.close(SSE_CLOSE_SOURCES.RECONNECT);

        // reconnect after a delay
        setTimeout(() => this.connect(SSE_CONNECT_SOURCES.RECONNECT), this.reconnectInterval);
      } else {
        logger.error('SSE_Max_Reconnection_Reached', { readyState });

        this.close(SSE_CLOSE_SOURCES.ERROR);

        this.onError && this.onError({ type: SSE_ERROR_TYPES.MAX_RECONNECTION_REACHED });
      }
    };
  }

  close(from) {
    if (this.eventSource) {
      logger.log('SSE_Connection_Closing', { from, uuid: this.uuid });

      this.eventSource.close();
      this.eventSource = null;

      logger.log('SSE_Connection_Closed', { from, uuid: this.uuid });
    }
  }

  addEventListener(eventType, callbacks) {
    const addEventListenerCallbacks = Array.isArray(callbacks) ? callbacks : [callbacks];

    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }

    const existingListeners = this.listeners.get(eventType);

    addEventListenerCallbacks.forEach(callback => {
      const isAlreadyAdded = existingListeners.some(existingListener => existingListener === callback);

      if (!isAlreadyAdded) {
        existingListeners.push(callback);
        /* eslint-disable-next-line no-underscore-dangle */
        this._addEventSourceListener(eventType, callback);
      }
    });
  }

  removeEventListener(eventType, callbacks) {
    const listeners = this.listeners.get(eventType) || [];

    if (!callbacks) {
      listeners.forEach(listener => {
        /* eslint-disable-next-line no-underscore-dangle */
        this._removeEventSourceListener(eventType, listener);
      });

      this.listeners.delete(eventType);

      return;
    }

    const removeEventListenerCallbacks = Array.isArray(callbacks) ? callbacks : [callbacks];

    removeEventListenerCallbacks.forEach(callback => {
      const listenerIndex = listeners.findIndex(listener => listener === callback);

      if (listenerIndex !== -1) {
        /* eslint-disable-next-line no-underscore-dangle */
        this._removeEventSourceListener(eventType, listeners[listenerIndex]);
        listeners.splice(listenerIndex, 1);
      }
    });

    if (listeners.length === 0) {
      this.listeners.delete(eventType);
    }
  }

  resetEventListeners() {
    this.listeners.forEach((listenerList, eventType) => {
      listenerList.forEach(listener => {
        /* eslint-disable-next-line no-underscore-dangle */
        this._addEventSourceListener(eventType, listener);
      });
    });
  }

  /* eslint-disable-next-line no-underscore-dangle */
  _addEventSourceListener(eventType, listener) {
    if (this.eventSource) {
      this.eventSource.addEventListener(eventType, listener);
    }
  }

  /* eslint-disable-next-line no-underscore-dangle */
  _removeEventSourceListener(eventType, listener) {
    if (this.eventSource) {
      this.eventSource.removeEventListener(eventType, listener);
    }
  }
}

export default SSEClient;
