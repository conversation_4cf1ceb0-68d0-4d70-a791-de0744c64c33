import { PROMO_VOUCHER_STATUS, PROMOTION_CLIENT_TYPES } from '../constants';

export const REWARDS_APPLIED_ALL_STORES = 'All';

export const DEFAULT_NEAR_EXPIRY_DAYS = 8;

export const REWARDS_APPLIED_SOURCES = {
  POS: 1,
  E_Commerce: 2,
  Beep_Pickup: 5,
  Beep_Delivery: 6,
  Beep_Takeaway: 7,
  Beep_DineIn: 8,
};

export const REWARD_APPLY_TO_LIMITS_CONDITIONS = {
  ENTITY: {
    TRANSACTION: 'TRANSACTION',
    PRODUCT: 'PRODUCT',
    CUSTOMER: 'CUSTOMER',
    BUSINESS: 'BUSINESS',
  },
  PROPERTY_NAME: {
    TOTAL: 'total',
    TAGS: 'tags',
    ID: 'id',
    CATEGORY: 'category',
  },
  OPERATOR: {
    GTE: 'gte',
  },
};

export const REWARD_APPLIED_CODE_ERRORS = {
  ENTER_INVALID_PROMO_CODE: 'enterInvalidPromoCode',
};

export const REWARD_APPLIED_ERROR_CODES = {
  ONLY_FOR_DELIVERY_ORDERS: '41003',
  NOT_START_OR_EXPIRED: '54403',
  WEEKDAY_NOT_MATCH: '54404',
  TIME_NOT_MATCH: '54405',
  FREE_PROMOTION_NOT_MATCH_CONDITION: '54406',
  INVALID_PROMOTION_CODE: '54301',
  DELETED_PROMOTION: '54410',
  PRODUCT_NOT_EXISTS: '54401',
  NOT_MATCH_CONDITION: '54407',
  REQUIRE_SAME_BUSINESS: '54408',
  STORE_DOES_NOT_SATISFY: '54409',
  PROMOTION_REACHES_MAX_CLAIM_COUNT: '54411',
  REQUIRE_CUSTOMER: '54412',
  REACH_CUSTOMER_CLAIM_COUNT_LIMIT: '54413',
  REQUIRE_FIRST_TIME_PURCHASE: '54414',
  NO_SOURCE_PROPERTY: '54415',
  APPLIED_SOURCE_DOES_NOT_MATCH: '54416',
  NOT_MATCH_MIN_SUBTOTAL_CONSUMING_PROMO: '54417',
  NOT_MATCH_APPLIED_CLIENT_TYPE: '54418',
  ONLY_APPLICABLE_FOR_PARTICIPATING: '54419',
  REACH_MAX_BUSINESS_CLAIM_COUNT: '54420',
  NOT_EXISTED: '60001',
  NOT_ACTIVE: '60002',
  EXPIRED: '60003',
  LESS_THAN_MIN_SUBTOTAL_CONSUMING_VOUCHER: '60004',
  CHANNEL_NOT_MATCH: '60005',
  APPLY_FAILED: '60006',
  FORBIDDEN: '60007',
  VOUCHER_HAS_BEEN_USED: '60009',
  UPDATE_VOUCHER_STATUS_FAILED: '60008',
  INVALID_PROMOTION_OR_VOUCHER: '60000',
  VOUCHER_NOT_MATCH_SOURCE: '60010',
};

/**
 * i18n keys
 */
export const UNIQUE_PROMO_STATUS_I18KEYS = {
  [PROMO_VOUCHER_STATUS.EXPIRED]: 'Expired',
  [PROMO_VOUCHER_STATUS.REDEEMED]: 'Redeemed',
};

export const REWARD_STATUS_I18N_KEYS = {
  [PROMO_VOUCHER_STATUS.EXPIRED]: 'Expired',
  [PROMO_VOUCHER_STATUS.REDEEMED]: 'Redeemed',
};

export const REWARDS_APPLIED_SOURCE_I18KEYS = {
  [REWARDS_APPLIED_SOURCES.POS]: 'POS',
  [REWARDS_APPLIED_SOURCES.E_Commerce]: 'Ecommerce',
  [REWARDS_APPLIED_SOURCES.Beep_Pickup]: 'BeepPickup',
  [REWARDS_APPLIED_SOURCES.Beep_Delivery]: 'BeepDelivery',
  [REWARDS_APPLIED_SOURCES.Beep_Takeaway]: 'BeepTakeaway',
  [REWARDS_APPLIED_SOURCES.Beep_DineIn]: 'BeepDineIn',
};

export const WEEK_DAYS_MAPPING = {
  2: 'Mon',
  3: 'Tue',
  4: 'Wed',
  5: 'Thu',
  6: 'Fri',
  7: 'Sat',
  1: 'Sun',
};

export const REWARD_CLIENT_TYPES_I18N_KEYS = {
  [PROMOTION_CLIENT_TYPES.WEB]: 'Beepit.com',
  [PROMOTION_CLIENT_TYPES.APP]: 'BeepApp',
  [PROMOTION_CLIENT_TYPES.TNG_MINI_PROGRAM]: 'BeepTngMiniProgram',
  [PROMOTION_CLIENT_TYPES.GCASH_MINI_PROGRAM]: 'BeepGCashMiniProgram',
};

export const REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS = {
  VALID_DAYS_STRING: 'validDaysString',
  VALID_TIME_FROM: 'validTimeFrom',
  VALID_TIME_TO: 'validTimeTo',
  SUPPORTED_CHANNEL: 'supportedChannel',
  MIN_SUBTOTAL_CONSUMING_PROMO: 'minSubtotalConsumingPromo',
  SUPPORT_CLIENT: 'supportClient',
};

export const REWARD_APPLIED_ERROR_I8NS = {
  [REWARD_APPLIED_ERROR_CODES.ONLY_FOR_DELIVERY_ORDERS]: {
    i18nKey: '41003UniversalPromotionError',
  },
  [REWARD_APPLIED_ERROR_CODES.NOT_START_OR_EXPIRED]: {
    i18nKey: '54403NotStartOrExpired',
  },
  [REWARD_APPLIED_ERROR_CODES.WEEKDAY_NOT_MATCH]: {
    i18nKey: '54404WeekdayNotMatch',
    i18nParamKeys: [REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS.VALID_DAYS_STRING],
  },
  [REWARD_APPLIED_ERROR_CODES.TIME_NOT_MATCH]: {
    i18nKey: '54405TimeNotMatch',
    i18nParamKeys: [
      REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS.VALID_TIME_FROM,
      REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS.VALID_TIME_TO,
    ],
  },
  [REWARD_APPLIED_ERROR_CODES.FREE_PROMOTION_NOT_MATCH_CONDITION]: {
    i18nKey: '54406FreePromotionNotMatchCondition',
  },
  [REWARD_APPLIED_ERROR_CODES.INVALID_PROMOTION_CODE]: {
    i18nKey: '54301InvalidPromotionCode',
  },
  [REWARD_APPLIED_ERROR_CODES.DELETED_PROMOTION]: {
    i18nKey: '54410DeletedPromotion',
  },
  [REWARD_APPLIED_ERROR_CODES.PRODUCT_NOT_EXISTS]: {
    i18nKey: '54401ProductNotExist',
  },
  [REWARD_APPLIED_ERROR_CODES.NOT_MATCH_CONDITION]: {
    i18nKey: '54407NotMatchCondition',
  },
  [REWARD_APPLIED_ERROR_CODES.REQUIRE_SAME_BUSINESS]: {
    i18nKey: '54408RequireSameBusiness',
  },
  [REWARD_APPLIED_ERROR_CODES.STORE_DOES_NOT_SATISFY]: {
    i18nKey: '54409StoreDoesNotSatisfy',
  },
  [REWARD_APPLIED_ERROR_CODES.PROMOTION_REACHES_MAX_CLAIM_COUNT]: {
    i18nKey: '54411PromotionReachesMaxClaimCount',
  },
  [REWARD_APPLIED_ERROR_CODES.REQUIRE_CUSTOMER]: {
    i18nKey: '54412RequireCustomer',
  },
  [REWARD_APPLIED_ERROR_CODES.REACH_CUSTOMER_CLAIM_COUNT_LIMIT]: {
    i18nKey: '54413ReachCustomerClaimCountLimit',
  },
  [REWARD_APPLIED_ERROR_CODES.REQUIRE_FIRST_TIME_PURCHASE]: {
    i18nKey: '54414RequireFirstTimePurchase',
  },
  [REWARD_APPLIED_ERROR_CODES.NO_SOURCE_PROPERTY]: {
    i18nKey: '54415NoSourceProperty',
  },
  [REWARD_APPLIED_ERROR_CODES.APPLIED_SOURCE_DOES_NOT_MATCH]: {
    i18nKey: '54416AppliedSourceDoesNotMatch',
    i18nParamKeys: [REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS.SUPPORTED_CHANNEL],
  },
  [REWARD_APPLIED_ERROR_CODES.NOT_MATCH_MIN_SUBTOTAL_CONSUMING_PROMO]: {
    i18nKey: '54417NotMatchMinSubtotalConsumingPromo',
    i18nParamKeys: [REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS.MIN_SUBTOTAL_CONSUMING_PROMO],
  },
  [REWARD_APPLIED_ERROR_CODES.NOT_MATCH_APPLIED_CLIENT_TYPE]: {
    i18nKey: '54418NotMatchAppliedClientType',
    i18nParamKeys: [REWARD_APPLIED_ERROR_I8NS_PARAMS_KEYS.SUPPORT_CLIENT],
  },
  [REWARD_APPLIED_ERROR_CODES.ONLY_APPLICABLE_FOR_PARTICIPATING]: {
    i18nKey: '54419OnlyApplicableForParticipating',
  },
  [REWARD_APPLIED_ERROR_CODES.REACH_MAX_BUSINESS_CLAIM_COUNT]: {
    i18nKey: '54420ReachMaxBusinessClaimCount',
  },
  [REWARD_APPLIED_ERROR_CODES.NOT_EXISTED]: {
    i18nKey: '60001NotExisted',
  },
  [REWARD_APPLIED_ERROR_CODES.NOT_ACTIVE]: {
    i18nKey: '60002NotActive',
  },
  [REWARD_APPLIED_ERROR_CODES.EXPIRED]: {
    i18nKey: '60003Expired',
  },
  [REWARD_APPLIED_ERROR_CODES.LESS_THAN_MIN_SUBTOTAL_CONSUMING_VOUCHER]: {
    i18nKey: '60004LessThanMinSubtotalConsumingVoucher',
  },
  [REWARD_APPLIED_ERROR_CODES.CHANNEL_NOT_MATCH]: {
    i18nKey: '60005ChannelNotMatch',
  },
  [REWARD_APPLIED_ERROR_CODES.APPLY_FAILED]: {
    i18nKey: '60006ApplyFailed',
  },
  [REWARD_APPLIED_ERROR_CODES.FORBIDDEN]: {
    i18nKey: '60007Forbidden',
  },
  [REWARD_APPLIED_ERROR_CODES.VOUCHER_HAS_BEEN_USED]: {
    i18nKey: '60009VoucherHasBeenUsed',
  },
  [REWARD_APPLIED_ERROR_CODES.UPDATE_VOUCHER_STATUS_FAILED]: {
    i18nKey: '60008UpdateVoucherStatusFailed',
  },
  [REWARD_APPLIED_ERROR_CODES.INVALID_PROMOTION_OR_VOUCHER]: {
    i18nKey: '60000InvalidPromotionOrVoucher',
  },
  [REWARD_APPLIED_ERROR_CODES.VOUCHER_NOT_MATCH_SOURCE]: {
    i18nKey: '60010VoucherNotMatchSource',
  },
};

export const REWARD_APPLIED_CODE_ERRORS_I18N_KEYS = {
  [REWARD_APPLIED_CODE_ERRORS.ENTER_INVALID_PROMO_CODE]: 'FillInvalidPromoCode',
};
/* end of i18n */
