/**
 * This is the new version of constants which export constants separately.
 * NOTE: If you copy something from the legacy constants file, please change same constants in that
 * file to a reference to this file, so that each constant is defined only once.
 */

// Import SSE constants from their proper location
import { SSE_TOPICS, SSE_EVENT_NAMES } from '../sse/constants';

/**
 * yarn build always set NODE_ENV as production
 */
export const BUILD_NODE_ENV = 'production';

export const DESKTOP_PAGE_WIDTH = 414;

export const COUNTRIES = {
  MY: 'MY',
  TH: 'TH',
  PH: 'PH',
  SG: 'SG',
  CN: 'CN',
  BN: 'BN',
};

export const COUNTRIES_DEFAULT_CURRENCIES = {
  [COUNTRIES.MY]: 'MYR',
  [COUNTRIES.TH]: 'THB',
  [COUNTRIES.PH]: 'PHP',
  [COUNTRIES.SG]: 'SGD',
  [COUNTRIES.CN]: 'CNY',
  [COUNTRIES.BN]: 'BND',
};

export const COUNTRIES_DEFAULT_LOCALE = {
  [COUNTRIES.MY]: 'MS-MY',
  [COUNTRIES.TH]: 'TH-TH',
  [COUNTRIES.PH]: 'EN-PH',
  [COUNTRIES.SG]: 'EN-SG',
  [COUNTRIES.CN]: 'ZH-CN',
  [COUNTRIES.BN]: 'en-BN',
};

export const WEB_VIEW_SOURCE = {
  IOS: 'iOS',
  Android: 'Android',
};

export const CLIENTS = {
  WEB: 'web',
  IOS: 'iOS',
  ANDROID: 'Android',
  MAC: 'Mac',
  PC: 'PC',
  TNG_MINI_PROGRAM: 'tngMiniProgram',
  GCASH_MINI_PROGRAM: 'gcashMiniProgram',
  MAYBANK_MAE_MINI_PROGRAM: 'maybankMAEMiniProgram',
};

export const AUTH_INFO = {
  MAE_GRANT_TYPE: 'mae',
  GRANT_TYPE: 'otp',
  CLIENT: 'beep',
};

export const REGISTRATION_SOURCE = {
  BEEP_APP: 'BeepApp',
  RECEIPT: 'Receipt',
  BEEP_STORE: 'BeepStore',
  BEEP_SITE: 'BeepSite',
  TNGD_MINI_PROGRAM: 'BeepTngMiniProgram',
  GCASH_MINI_PROGRAM: 'BeepGCashMiniProgram',
  MAYBANK_MAE_MINI_PROGRAM: 'BeepMaybankMAEMiniProgram',
  SHARED_LINK: 'SharedLink',
};

export const SOURCE_TYPE = {
  SHOPPING_CART: 'shoppingCart',
  SHARED_LINK: 'SharedLink',
  PUSH_NOTIFICATION: 'PushNotification',
  SMS: 'SMS',
};

export const URL_TYPES = {
  STATIC: 'static',
  DYNAMIC: 'dynamic',
};

export const DOWNLOAD_BEEP_APP_URL = {
  UTM_SOURCE: {
    WEB: 'web',
    MERCHANT_PROMO: 'merchantpromo',
    BEEPQR: 'beepqr',
    BEEP_SITE: 'beepit.co',
    STORE_LINK: 'store_link',
  },
  UTM_MEDIUM: {
    BANNER: 'banner',
    SHARE: 'share',
    WEB_SCANNER: 'web_scanner',
  },
  UTM_CAMPAIGN: {
    SEAMLESS_LOYALTY: 'seamlessloyalty',
    CLAIM_PROMO: 'claimpromo',
    MY_MEMBERSHIPS: 'my_memberships',
    WEB_SCANNER: 'web_scanner',
  },
};

export const API_REQUEST_STATUS = {
  PENDING: 'pending',
  FULFILLED: 'fulfilled',
  REJECTED: 'rejected',
};

export const API_REQUEST_OMIT_CREDENTIALS = 'omit';

export const PATH_NAME_MAPPING = {
  TERMS_OF_USE: '/terms-of-use',
  THANK_YOU: '/thank-you',
  PRIVACY: '/privacy-policy',
  ERROR: '/error',
  SORRY: '/sorry',
  REPORT_DRIVER: '/report-driver',
  STORES_HOME: '/',
  // ordering App basename
  ORDERING_BASE: '/ordering',
  ORDERING_HOME: '/',
  ORDERING_LOGIN: '/login',
  ORDERING_LOCATION_AND_DATE: '/location-date',
  ORDERING_LOCATION: '/location',
  ORDERING_CUSTOMER_INFO: '/customer',
  ORDERING_CART: '/cart',
  ORDERING_CART_SUBMISSION_STATUS: '/cart/cart-submission',
  ORDERING_TABLE_SUMMARY: '/table-summary',
  ORDERING_PAYMENT: '/payment',
  ORDERING_STRIPE_PAYMENT: '/payment/stripe',
  ORDERING_STRIPE_PAYMENT_SAVE: '/payment/stripe/save',
  ORDERING_CREDIT_CARD_PAYMENT: '/payment/creditcard',
  ORDERING_ONLINE_BANKING_PAYMENT: '/payment/online-banking',
  ORDERING_ONLINE_SAVED_CARDS: '/payment/cards',
  MERCHANT_INFO: '/need-help',
  ORDERING_STORE_LIST: '/storeList',
  ADDRESS_LIST: '/addressList',
  ADDRESS_DETAIL: '/addressDetail',
  CONTACT_DETAIL: '/contactDetails',
  // cashback App basename
  CASHBACK_BASE: '/loyalty',
  CASHBACK_HOME: '/',
  CASHBACK_CLAIM: '/claim',
  CASHBACK_HISTORIES: '/activities',
  // site
  SITE_BASE: '/',
  SITE_HOME: '/home',
  QRSCAN: '/qrscan',
  SCAN: '/scan',
  SCAN_NOT_SUPPORT: '/scanNotSupport',
  ORDER_DETAILS: '/orderdetails',
  ORDER_HISTORY: '/order-history',
  // voucher
  VOUCHER_HOME: '/voucher',
  VOUCHER_CONTACT: '/voucher/contact',
  VOUCHER_THANK_YOU: '/voucher/thank-you',
  VOUCHER_SORRY: '/voucher/sorry',
  VOUCHER_PAYMENT: '/ordering/payment',
  // rewards App basename
  REWARDS_BASE: '/rewards',
  REWARDS_HOME: '/',
  REWARDS_BUSINESS: '/business',
  REWARDS_MEMBERSHIP: '/membership',
  SIGN_UP: '/sign-up',
  MEMBERSHIP_DETAIL: '/membership-detail',
  POINTS_HISTORY: '/points-history',
  CASHBACK_CREDITS_HISTORY: '/cashback-credits-history',
  SEAMLESS_LOYALTY: '/seamless-loyalty',
  UNIQUE_PROMO: '/promo',
  POINTS_REWARDS: '/points-rewards',
  REWARDS_CONSUMER: '/consumer',
  CLAIM: '/claim',
  CASHBACK: '/cashback',
  CASHBACK_DETAIL: '/cashback-detail',
  // user App basename
  USER_BASE: '/user',
  // dine
  DINE: '/dine',
  FOOD_COURT: '/food-court',
  STORE_REVIEW: '/store-review',
  // common
  REWARD: '/reward',
  LIST: '/list',
  DETAIL: '/detail',
  LOGIN: '/login',
};

export const SHIPPING_TYPES = {
  DELIVERY: 'delivery',
  PICKUP: 'pickup',
  DINE_IN: 'dine-in',
  TAKE_AWAY: 'takeaway',
  DIGITAL: 'digital',
};

export const PROMOTION_CLIENT_TYPES = {
  TNG_MINI_PROGRAM: 'tngMiniProgram',
  GCASH_MINI_PROGRAM: 'gcashMiniProgram',
  MAYBANK_MAE_MINI_PROGRAM: 'maybankMAEMiniProgram',
  APP: 'app',
  WEB: 'web',
};

export const WEEK_DAYS_I18N_KEYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export const SH_LOGISTICS_VALID_TIME = {
  FROM: '09:00',
  TO: '21:00',
};

export const LOCATION_SELECTION_REASON_CODES = {
  OUT_OF_DELIVERY_RANGE: 'OutOfDeliveryRange',
  ADDRESS_NOT_FOUND: 'AddressNotFound',
};

export const ADDRESS_RANGE = {
  STREET: 2,
  CITY: 4,
  STATE: 5,
  COUNTRY: 6,
};

export const PAYMENT_METHOD_LABELS = {
  STRIPE: 'stripe',
  ONLINE_BANKING_PAY: 'OnlineBanking',
  CREDIT_CARD_PAY: 'CreditAndDebitCard',
  GRAB_PAY: 'GrabPay',
  BOOST_PAY: 'Boost',
  TNG_PAY: 'TouchNGo',
  GCASH_PAY: 'GCash',
  LINE_PAY: 'Line',
  GETZ_PAY: 'GetzPay',
  APPLE_PAY: 'ApplePay',
};

export const PAYMENT_PROVIDERS = {
  STRIPE: 'Stripe',
  APPLE_PAY: 'StripeApplePay',
  STRIPE_FPX: 'StripeFPX',
  TNG_ONLINE: 'TnGOnline',
  BOOST: 'Boost',
  GRAB_PAY: 'GrabPay',
  BEEP_TH_CREDIT_CARD: 'BeepTHCreditCard',
  BEEP_TH_ONLINE_BANKING: 'BeepTHOnlineBanking',
  BEEP_TH_LINE_PAY: 'BeepTHLinePay',
  BEEP_PH_CREDIT_CARD: 'BeepPHCreditCard',
  BEEP_PH_CCPP_GCASH: 'BeepPHCCPPGcash',
  SH_OFFLINE_PAYMENT: 'SHOfflinePayment', // Pay at counter
  TNG_MINI_PROGRAM: 'TNGMiniProgram',
  GCASH_MINI_PROGRAM: 'GCashMiniProgram',
  MAYBANK_MAE_MINI_PROGRAM: 'Maybank2uMAE',
};

export const MAYBANK_MAE_USERAGENT_NAME = 'Maybank-MY';

export const PRODUCT_STOCK_STATUS = {
  NOT_TRACK_INVENTORY: 'notTrackInventory',
  IN_STOCK: 'inStock',
  LOW_STOCK: 'lowStock',
  OUT_OF_STOCK: 'outOfStock',
  UNAVAILABLE: 'unavailable',
};

export const ORDER_SOURCE = {
  TNG_MINI_PROGRAM: 'BeepTngMiniProgram',
  GCASH_MINI_PROGRAM: 'BeepGCashMiniProgram',
  MAYBANK_MAE_MINI_PROGRAM: 'BeepMaybankMAEMiniProgram',
  BEEP_APP: 'BeepApp',
  BEEP_SITE: 'BeepSite',
  BEEP_STORE: 'BeepStore',
};

export const PRE_ORDER_IMMEDIATE_TAG = {
  from: 'now',
  to: 'now',
};

export const ORDER_STATUS = {
  CREATED: 'created',
  PENDING_PAYMENT: 'pendingPayment',
  PENDING_VERIFICATION: 'pendingVerification',
  PAID: 'paid',
  PAYMENT_CANCELLED: 'paymentCancelled',
  READY_FOR_DELIVERY: 'readyForDelivery',
  READY_FOR_PICKUP: 'readyForPickup',
  SHIPPED: 'shipped',
  CANCELLED: 'cancelled',
  FAILED: 'failed',
  ACCEPTED: 'accepted',
  LOGISTICS_CONFIRMED: 'logisticsConfirmed',
  CONFIRMED: 'confirmed',
  DELIVERED: 'delivered',
  /**
   * If shipping type is delivery, pickedUp means picked up by rider.
   * if shipping type is self-pickup, pickedUp means picked up by customer
   * */
  PICKED_UP: 'pickedUp',
};

export const REGISTRATION_TOUCH_POINT = {
  CLAIM_CASHBACK: 'ClaimCashback',
  ONLINE_ORDER: 'OnlineOrder',
  QR_ORDER: 'QROrder',
  TNG: 'TNG',
  GCash: 'GCash',
  MaybankMAE: 'MaybankMAE',
};

// TODO: remove this after we have a better solution
export const TIME_SLOT_NOW = 'now';

export const TIME_SLOT = {
  NOW: 'now',
  TODAY: 'Today',
  TOMORROW: 'Tomorrow',
};

export const CASHBACK_SOURCE = {
  REGISTER: 'REGISTER',
  RECEIPT: 'RECEIPT',
  QR_ORDERING: 'QR_ORDERING',
};

export const CLAIM_CASHBACK_QUERY_NAMES = {
  STATUS: 'claimedStatus',
  VALUE: 'cashback',
};

export const REFERRER_SOURCE_TYPES = {
  PAYMENT: 'payment',
  CASHBACK: 'cashback',
  PAY_AT_COUNTER: 'payAtCounter',
  LOGIN: 'login',
  THANK_YOU: 'thankyou',
};

export const BECOME_MERCHANT_MEMBER_METHODS = {
  JOIN_MEMBERSHIP_URL_CLICK: 'JoinMembershipURL_ClickJoin',
  THANK_YOU_CASHBACK_CLICK: 'BeepQR_ThankYou',
  EARNED_CASHBACK_QR_SCAN: 'Receipt_CashbackQR',
  MEMBERSHIP_QR_SCAN: 'Receipt_MembershipQR',
  QR_ORDERING_ORDER_COMPLETED: 'BeepQR_Transaction',
  DELIVERY_ORDERING_ORDER_COMPLETED: 'BeepDel_Transaction',
  OFFLINE_STORE_ORDER_COMPLETE: 'POS_Transaction',
  SEAMLESS_LOYALTY_QR_SCAN: 'POS_SeamlessLoyaltyQR',
  RECEIPT_MEMBERSHIP_DETAIL_QR_SCAN: 'Receipt_MembershipDetailsQR',
  RECEIPT_JOIN_MEMBERSHIP_URL_QR_SCAN: 'Receipt_JoinMembershipURL',
  RECEIPT_POINTS_QR: 'Receipt_PointsQR',
  RECEIPT_POINTS_CASHBACK_QR: 'Receipt_PointsCashbackQR',
};

export const PROMO_VOUCHER_DISCOUNT_TYPES = {
  PERCENTAGE: 'percentage',
  ABSOLUTE: 'absolute',
};

export const PROMO_VOUCHER_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  REDEEMED: 'redeemed',
};

export const REWARDS_TYPE = {
  PROMOTION: 'promotion',
  VOUCHER: 'voucher',
};

export const MEMBER_LEVELS = {
  MEMBER: 1,
  SLIVER: 2,
  GOLD: 3,
  PLATINUM: 4,
};

export const MEMBER_CARD_COLOR_PALETTES = {
  [MEMBER_LEVELS.MEMBER]: {
    icon: {
      crown: {
        startColor: '#91F7E7',
        endColor: '#52A1FF',
      },
      background: {
        startColor: '#DCFCFF',
        endColor: '#D2DEFF',
      },
      outlineColor: '#303030',
    },
    background: {
      startColor: '#91F7E7',
      midColor: '#99C8FF',
      endColor: '#52A1FF',
    },
    progress: '#303030',
    font: '#303030',
  },
  [MEMBER_LEVELS.SLIVER]: {
    icon: {
      crown: {
        startColor: '#AFAFAF',
        endColor: '#AFAFAF',
      },
      background: {
        startColor: '#F3F3F3',
        endColor: '#CCCCCC',
      },
      outlineColor: '#303030',
    },
    background: {
      startColor: '#AFAFAF',
      midColor: '#E0E0E0',
      endColor: '#CCC',
    },
    progress: '#303030',
    font: '#303030',
  },
  [MEMBER_LEVELS.GOLD]: {
    icon: {
      crown: {
        startColor: '#FFF143',
        endColor: '#FFBD17',
      },
      background: {
        startColor: '#FFFBE6',
        endColor: '#FFFEAD',
      },
      outlineColor: '#303030',
    },
    background: {
      startColor: '#FFF143',
      midColor: '#FFBD17',
      endColor: '#FFCF43',
    },
    progress: '#303030',
    font: '#303030',
  },
  [MEMBER_LEVELS.PLATINUM]: {
    icon: {
      crown: {
        startColor: '#000000',
        endColor: '#000000',
      },
      background: {
        startColor: '#EAEAEA',
        endColor: '#AFAFAF',
      },
      outlineColor: '#717171',
    },
    background: {
      startColor: '#000000',
      midColor: '#4E4E4E',
      endColor: '#000000',
    },
    progress: '#717171',
    font: '#FFFFFF',
  },
};

export const MEMBER_LEVELS_PALETTES = {
  [MEMBER_LEVELS.MEMBER]: {
    icon: {
      crown: {
        startColor: '#91F7E7',
        endColor: '#52A1FF',
      },
      background: {
        startColor: '#DCFCFF',
        endColor: '#D2DEFF',
      },
      strokeColor: '#5CADFC',
    },
    background: {
      startColor: '#CFFFF7',
      midColor: '#C8E1FF',
      endColor: '#97C4FA',
    },
    progress: '#231651',
    font: '#1C1C1C',
  },
  [MEMBER_LEVELS.SLIVER]: {
    icon: {
      crown: {
        startColor: '#869696',
        endColor: '#CDCDCD',
      },
      background: {
        startColor: '#FFFFFF',
        endColor: '#D9D9D9',
      },
      strokeColor: '#9E9E9E',
    },
    background: {
      startColor: '#D2D2D2',
      midColor: '#F2F2F2',
      endColor: '#CCCCCC',
    },
    progress: '#231651',
    font: '#303030',
  },
  [MEMBER_LEVELS.GOLD]: {
    icon: {
      crown: {
        startColor: '#E6AF20',
        endColor: '#BF8B09',
      },
      background: {
        startColor: '#FDFBC1',
        endColor: '#E3B151',
      },
      strokeColor: '#C4900C',
    },
    background: {
      startColor: '#FFFDCA',
      midColor: '#FED48E',
      endColor: '#FFEED0',
    },
    progress: '#231651',
    font: '#303030',
  },
  [MEMBER_LEVELS.PLATINUM]: {
    icon: {
      crown: {
        startColor: '#FFFFFF',
        endColor: '#999999',
      },
      background: {
        startColor: '#757575',
        endColor: '#000000',
      },
      strokeColor: '#141414',
    },
    background: {
      startColor: '#000000',
      midColor: '#6A6A6A',
      endColor: '#202020',
    },
    progress: '#231651',
    font: '#FFFFFF',
  },
};

export const DISPLAY_ICON_TYPES = {
  FUNNEL_SIMPLE: 'FunnelSimple',
  CARET_DOWN: 'CaretDown',
};

export const ORDER_SHIPPING_TYPE_DISPLAY_NAME_MAPPING = {
  [SHIPPING_TYPES.DINE_IN]: 'dine in',
  [SHIPPING_TYPES.PICKUP]: 'self pickup',
  [SHIPPING_TYPES.DELIVERY]: 'delivery',
  [SHIPPING_TYPES.TAKE_AWAY]: 'takeaway',
};

export const AVAILABLE_REPORT_DRIVER_ORDER_STATUSES = [ORDER_STATUS.DELIVERED, ORDER_STATUS.PICKED_UP];

export const LIVE_CHAT_SOURCE_TYPES = {
  ORDER_DETAILS: 'order details',
};

export const OTP_COMMON_ERROR_TYPES = {
  NETWORK_ERROR: 'Network Error',
  UNKNOWN_ERROR: 'Unknown Error',
};

export const OTP_BFF_ERROR_CODES = {
  PHONE_INVALID: 41001,
  TYPE_INVALID: 41002,
};

export const OTP_API_ERROR_CODES = {
  PHONE_INVALID: 394761,
  REQUEST_TOO_FAST: 394757,
  MEET_DAY_LIMIT: 394755,
  HIGH_RISK: 394756,
};

export const SMS_API_ERROR_CODES = {
  PHONE_INVALID: 395011,
  NO_AVAILABLE_PROVIDER: 395012,
};

export const OTP_ERROR_POPUP_I18N_KEYS = {
  [OTP_API_ERROR_CODES.MEET_DAY_LIMIT]: {
    title: 'ApiError:394755Title',
    description: 'ApiError:394755Description',
  },
  [OTP_API_ERROR_CODES.HIGH_RISK]: {
    title: 'ApiError:394756Title',
    description: 'ApiError:394756Description',
  },
  [OTP_COMMON_ERROR_TYPES.NETWORK_ERROR]: {
    title: 'NetworkErrorTitle',
    description: 'NetworkErrorDescription',
  },
  [OTP_COMMON_ERROR_TYPES.UNKNOWN_ERROR]: {
    title: 'UnknownErrorTitle',
    description: 'UnknownErrorDescription',
  },
};

export const OTP_SERVER_ERROR_I18N_KEYS = {
  [OTP_BFF_ERROR_CODES.PHONE_INVALID]: 'ApiError:41001ShortDescription',
  [OTP_API_ERROR_CODES.PHONE_INVALID]: 'ApiError:394761ShortDescription',
  [SMS_API_ERROR_CODES.PHONE_INVALID]: 'ApiError:395011ShortDescription',
  [OTP_API_ERROR_CODES.MEET_DAY_LIMIT]: 'ApiError:394755ShortDescription',
  [OTP_API_ERROR_CODES.REQUEST_TOO_FAST]: 'ApiError:394757ShortDescription',
  [SMS_API_ERROR_CODES.NO_AVAILABLE_PROVIDER]: 'ApiError:395012ShortDescription',
};

export const PAYMENT_FAILED_ERROR_CODES = {
  AUTHENTICATION_REQUIRED: 'AuthenticationRequired',
  BANK_DECLINED: 'BankDeclined',
  CARD_EXPIRED: 'CardExpired',
  INCORRECT_CVC: 'IncorrectCvc',
  AMOUNT_TOO_LARGE: 'AmountTooLarge',
  AMOUNT_TOO_SMALL: 'AmountTooSmall',
  BALANCE_INSUFFICIENT: 'BalanceInsufficient',
  GATE_WAY_DECLINED: 'GateWayDeclined',
  PAYMENT_GATEWAY_ERROR: 'PaymentGatewayError',
  UNKNOWN_ERROR: 'UnknownError',
};

export const PAYMENT_FAILED_ERROR_I18N_KEYS = {
  [PAYMENT_FAILED_ERROR_CODES.AUTHENTICATION_REQUIRED]: 'OrderingPayment:AuthenticationRequiredDescription',
  [PAYMENT_FAILED_ERROR_CODES.BANK_DECLINED]: 'OrderingPayment:BankDeclinedDescription',
  [PAYMENT_FAILED_ERROR_CODES.CARD_EXPIRED]: 'OrderingPayment:CardExpiredDescription',
  [PAYMENT_FAILED_ERROR_CODES.INCORRECT_CVC]: 'OrderingPayment:IncorrectCvcDescription',
  [PAYMENT_FAILED_ERROR_CODES.AMOUNT_TOO_LARGE]: 'OrderingPayment:AmountTooLargeDescription',
  [PAYMENT_FAILED_ERROR_CODES.AMOUNT_TOO_SMALL]: 'OrderingPayment:AmountTooSmallDescription',
  [PAYMENT_FAILED_ERROR_CODES.BALANCE_INSUFFICIENT]: 'OrderingPayment:BalanceInsufficientDescription',
  [PAYMENT_FAILED_ERROR_CODES.GATE_WAY_DECLINED]: 'OrderingPayment:GateWayDeclinedDescription',
  [PAYMENT_FAILED_ERROR_CODES.PAYMENT_GATEWAY_ERROR]: 'OrderingPayment:PaymentGatewayErrorDescription',
  [PAYMENT_FAILED_ERROR_CODES.UNKNOWN_ERROR]: 'OrderingPayment:UnknownErrorDescription',
};

// Profile related constants
export const PROFILE_FIELD_ERROR_TYPES = {
  REQUIRED: 'required',
  UNAVAILABLE: 'unavailable',
  DUPLICATED: 'duplicated',
  OUT_OF_DATE: 'outOfDate',
};

export const ERROR_TRANSLATION_KEYS = {
  [PROFILE_FIELD_ERROR_TYPES.REQUIRED]: {
    firstName: 'FirstNameIsRequired',
    email: 'EmailIsRequired',
    birthday: 'BirthdayIsRequired',
  },
  [PROFILE_FIELD_ERROR_TYPES.UNAVAILABLE]: {
    email: 'NotValidEmail',
    birthday: 'NotValidBirthday',
  },
  [PROFILE_FIELD_ERROR_TYPES.DUPLICATED]: {
    email: 'DuplicatedEmail',
  },
  [PROFILE_FIELD_ERROR_TYPES.OUT_OF_DATE]: {
    birthday: 'BirthdayCanNotLaterThanToday',
  },
};

export const PROFILE_SKIP_CYCLE = 3650;

export const PROFILE_BIRTHDAY_FORMAT = 'DD/MM/YYYY';

export const BIRTHDAY_DATE = {
  MIN: '1900-01-01',
  MAX: new Date().toISOString().split('T')[0], // Using native JS instead of dayjs
};

// Login related constants
export const DISABLED_GUEST_OPTION_REDIRECT_LOCATIONS = [PATH_NAME_MAPPING.ORDER_HISTORY];

// Ordering page routes
export const PAGE_ROUTES = {
  LOGIN: PATH_NAME_MAPPING.ORDERING_LOGIN,
  MENU: PATH_NAME_MAPPING.ORDERING_HOME,
  CUSTOMER: PATH_NAME_MAPPING.ORDERING_CUSTOMER_INFO,
  LOCATION: PATH_NAME_MAPPING.ORDERING_LOCATION,
  CART: PATH_NAME_MAPPING.ORDERING_CART,
  REWARD_LIST: `${PATH_NAME_MAPPING.REWARD}${PATH_NAME_MAPPING.LIST}`,
  REWARD_DETAIL: `${PATH_NAME_MAPPING.REWARD}${PATH_NAME_MAPPING.DETAIL}`,
  TABLE_SUMMARY: PATH_NAME_MAPPING.ORDERING_TABLE_SUMMARY,
  THANK_YOU: PATH_NAME_MAPPING.THANK_YOU,
  THANK_YOU_ORDER_DETAIL: PATH_NAME_MAPPING.ORDER_DETAILS,
};

// Payment related constants
export const VENDOR_STRIPE = 'Stripe';

export const CREATE_ORDER_ERROR_CODES = {
  TRIAL_ORDERS_REACHED_LIMITATION: '57016',
  STORE_TEMP_STOPS_RECEIVING_ORDERS: '40027',
  PROMOTION_EXCEEDED_TOTAL_CLAIM_LIMIT: '54050',
  PROMOTION_INVALID: '54051',
  NO_PERMISSION: '40003',
  NO_STORE: '40006',
  NO_STORE_LOCATION: '40007',
  NO_DELIVERY_LOCATION: '40008',
  OVER_DELIVERY_DISTANCE: '40009',
  CREATE_ORDER_ERROR: '40010',
  CONTACT_NAME_AND_PHONE_REQUIRED: '40012',
  STORE_IS_ON_VACATION: '40013',
  PRODUCT_NOT_ENOUGH_INVENTORY: '54013',
  STORE_OUT_OF_STOCK: '54012',
  INVENTORY_SYNC_ERROR: '80000',
  INVENTORY_SYNC_TIMEOUT: '80001',
};

export const CREATE_ORDER_ERROR_ORDERING_ROUTES = {
  [CREATE_ORDER_ERROR_CODES.NO_PERMISSION]: PAGE_ROUTES.LOGIN,
  [CREATE_ORDER_ERROR_CODES.NO_STORE]: PAGE_ROUTES.MENU,
  [CREATE_ORDER_ERROR_CODES.NO_STORE_LOCATION]: PAGE_ROUTES.MENU,
  [CREATE_ORDER_ERROR_CODES.NO_DELIVERY_LOCATION]: PAGE_ROUTES.LOCATION,
  [CREATE_ORDER_ERROR_CODES.OVER_DELIVERY_DISTANCE]: PAGE_ROUTES.LOCATION,
  [CREATE_ORDER_ERROR_CODES.CREATE_ORDER_ERROR]: PAGE_ROUTES.CART,
  [CREATE_ORDER_ERROR_CODES.CONTACT_NAME_AND_PHONE_REQUIRED]: PAGE_ROUTES.CUSTOMER,
  [CREATE_ORDER_ERROR_CODES.STORE_IS_ON_VACATION]: PAGE_ROUTES.MENU,
  [CREATE_ORDER_ERROR_CODES.PRODUCT_NOT_ENOUGH_INVENTORY]: PAGE_ROUTES.CART,
  [CREATE_ORDER_ERROR_CODES.STORE_OUT_OF_STOCK]: PAGE_ROUTES.CART,
};

export const CREATE_ORDER_ERROR_I18N_KEYS = {
  [CREATE_ORDER_ERROR_CODES.TRIAL_ORDERS_REACHED_LIMITATION]: {
    titleI18nKey: 'ErrorTrailOrderReachedLimitationTitle',
    descriptionI18nKey: 'ErrorTrailOrderReachedLimitationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.STORE_TEMP_STOPS_RECEIVING_ORDERS]: {
    titleI18nKey: 'ErrorStoreTempStopsReceivingOrdersTitle',
    descriptionI18nKey: 'ErrorStoreTempStopsReceivingOrdersDescription',
  },
  [CREATE_ORDER_ERROR_CODES.PROMOTION_EXCEEDED_TOTAL_CLAIM_LIMIT]: {
    titleI18nKey: 'ErrorPromotionExceededTotalClaimLimitTitle',
    descriptionI18nKey: 'ErrorPromotionExceededTotalClaimLimitDescription',
  },
  [CREATE_ORDER_ERROR_CODES.PROMOTION_INVALID]: {
    titleI18nKey: 'ErrorPromotionInvalidTitle',
    descriptionI18nKey: 'ErrorPromotionInvalidDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_PERMISSION]: {
    titleI18nKey: 'ErrorNoPermissionTitle',
    descriptionI18nKey: 'ErrorNoPermissionDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_STORE]: {
    titleI18nKey: 'ErrorNoStoreTitle',
    descriptionI18nKey: 'ErrorNoStoreDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_STORE_LOCATION]: {
    titleI18nKey: 'ErrorNoStoreLocationTitle',
    descriptionI18nKey: 'ErrorNoStoreLocationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.NO_DELIVERY_LOCATION]: {
    titleI18nKey: 'ErrorNoDeliveryLocationTitle',
    descriptionI18nKey: 'ErrorNoDeliveryLocationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.OVER_DELIVERY_DISTANCE]: {
    titleI18nKey: 'ErrorOverDeliveryDistanceTitle',
    descriptionI18nKey: 'ErrorOverDeliveryDistanceDescription',
  },
  [CREATE_ORDER_ERROR_CODES.CREATE_ORDER_ERROR]: {
    titleI18nKey: 'ErrorCreateOrderErrorTitle',
    descriptionI18nKey: 'ErrorCreateOrderErrorDescription',
  },
  [CREATE_ORDER_ERROR_CODES.CONTACT_NAME_AND_PHONE_REQUIRED]: {
    titleI18nKey: 'ErrorContactNameAndPhoneRequiredTitle',
    descriptionI18nKey: 'ErrorContactNameAndPhoneRequiredDescription',
  },
  [CREATE_ORDER_ERROR_CODES.STORE_IS_ON_VACATION]: {
    titleI18nKey: 'ErrorStoreIsOnVacationTitle',
    descriptionI18nKey: 'ErrorStoreIsOnVacationDescription',
  },
  [CREATE_ORDER_ERROR_CODES.PRODUCT_NOT_ENOUGH_INVENTORY]: {
    titleI18nKey: 'ErrorProductNotEnoughInventoryTitle',
    descriptionI18nKey: 'ErrorProductNotEnoughInventoryDescription',
  },
  [CREATE_ORDER_ERROR_CODES.STORE_OUT_OF_STOCK]: {
    titleI18nKey: 'ErrorStoreOutOfStockTitle',
    descriptionI18nKey: 'ErrorStoreOutOfStockDescription',
  },
  [CREATE_ORDER_ERROR_CODES.INVENTORY_SYNC_ERROR]: {
    titleI18nKey: 'ErrorInventorySyncErrorTitle',
    descriptionI18nKey: 'ErrorInventorySyncErrorDescription',
  },
  [CREATE_ORDER_ERROR_CODES.INVENTORY_SYNC_TIMEOUT]: {
    titleI18nKey: 'ErrorInventorySyncTimeoutTitle',
    descriptionI18nKey: 'ErrorInventorySyncTimeoutDescription',
  },
};

// Reward related constants
export const REWARD_DETAIL_QUERY_PARAMS = {
  REWARD_ID: 'rewardId',
  REWARD_TYPE: 'rewardType',
};

// SSE related constants
export const SSE_SUBSCRIPTION_GROUPS = {
  PAY_LATER_ORDERING: 'PAY_LATER_ORDERING',
  ORDERED: 'ORDERED',
};

export const SSE_EVENT_LISTENER_PATHNAME_KEYS = {
  SHOPPING_CART_UPDATED: 'SHOPPING_CART_UPDATED',
  TABLE_SUMMARY_UPDATED: 'TABLE_SUMMARY_UPDATED',
  ORDER_UPDATED: 'ORDER_UPDATED',
};

export const SSE_SUBSCRIPTION_GROUP_PATHNAMES = {
  [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: [
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.MENU}`,
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.CART}`,
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.TABLE_SUMMARY}`,
  ],
  [SSE_SUBSCRIPTION_GROUPS.ORDERED]: [
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU}`,
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU_ORDER_DETAIL}`,
  ],
};

export const SSE_SUBSCRIPTION_GROUP_TOPICS = {
  [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: [SSE_TOPICS.PAY_LATER_SHOPPING_CART_UPDATED, SSE_TOPICS.ORDER_UPDATED],
  [SSE_SUBSCRIPTION_GROUPS.ORDERED]: SSE_TOPICS.ORDER_UPDATED,
};

export const SSE_EVENT_LISTENERS_LIMITATIONS = {
  [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: {
    [SSE_EVENT_LISTENER_PATHNAME_KEYS.SHOPPING_CART_UPDATED]: {
      eventName: SSE_EVENT_NAMES.SHOPPING_CART_UPDATED,
      pathnames: [
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.MENU}`,
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.CART}`,
      ],
    },
    [SSE_EVENT_LISTENER_PATHNAME_KEYS.TABLE_SUMMARY_UPDATED]: {
      eventName: SSE_EVENT_NAMES.ORDER_UPDATED,
      pathnames: [`${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.TABLE_SUMMARY}`],
    },
  },
  [SSE_SUBSCRIPTION_GROUPS.ORDERED]: {
    [SSE_EVENT_LISTENER_PATHNAME_KEYS.ORDER_UPDATED]: {
      eventName: SSE_EVENT_NAMES.ORDER_UPDATED,
      pathnames: [
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU}`,
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU_ORDER_DETAIL}`,
      ],
    },
  },
};
