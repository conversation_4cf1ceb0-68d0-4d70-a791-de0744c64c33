@keyframes scaleToDisappearance {
  0% {
    transform: scale(1);
    -webkit-transform: scale(1);
    opacity: 0.5;
  }
  25% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.5);
    -webkit-transform: scale(1.5);
    opacity: 0;
  }
}

@-webkit-keyframes scaleToDisappearance {
  0% {
    transform: scale(1);
    -webkit-transform: scale(1);
    opacity: 0.5;
  }
  25% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.25);
    -webkit-transform: scale(1.25);
    opacity: 0.1;
  }
  100% {
    transform: scale(1.5);
    -webkit-transform: scale(1.5);
    opacity: 0;
  }
}

@mixin scale-rectangle($width, $height) {
  $quarter-stage-scale: 0.4;
  $half-stage-scale: 0.4;
  $full-stage-scale: 0.6;

  @keyframes scaleRectangle {
    0% {
      transform: scale(1);
      -webkit-transform: scale(1);
      opacity: 0.5;
    }
    25% {
      transform: scale(calc(1 + ($quarter-stage-scale * $height / $width)), 1.4);
      -webkit-transform: scale(calc(1 + ($quarter-stage-scale * $height / $width)), 1.4);
      opacity: 0.3;
    }
    50% {
      transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.4);
      -webkit-transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.4);
      opacity: 0.1;
    }
    100% {
      transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.6);
      -webkit-transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.6);
      opacity: 0;
    }
  }

  @-webkit-keyframes scaleRectangle {
    0% {
      transform: scale(1);
      -webkit-transform: scale(1);
      opacity: 0.5;
    }
    25% {
      transform: scale(calc(1 + ($quarter-stage-scale * $height / $width)), 1.4);
      -webkit-transform: scale(calc(1 + ($quarter-stage-scale * $height / $width)), 1.4);
      opacity: 0.3;
    }
    50% {
      transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.4);
      -webkit-transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.4);
      opacity: 0.1;
    }
    100% {
      transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.6);
      -webkit-transform: scale(calc(1 + ($half-stage-scale * $height / $width)), 1.6);
      opacity: 0;
    }
  }
}

.beep-animated {
  animation-fill-mode: both;
}

.beep-animated--infinite {
  animation-iteration-count: infinite;
}

.beep-animated-default-duration {
  animation-duration: 1s;
}

.beep-scale-to-disappearance {
  animation-name: scaleToDisappearance;
}

.beep-scale-rectangle {
  animation-name: scaleRectangle;
}
