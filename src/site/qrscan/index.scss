@use "../Variables" as *;

/* QRScan */
.qrscan {
  &__recommend {
    top: 48px;
  }

  &__cover {
    position: absolute;
    top: 48px;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &__video-player {
    position: absolute;
    top: 50%;
    left: 50%;
    width: auto;
    height: auto;
    min-width: 100%;
    min-height: 100%;
    transform: translateX(-50%) translateY(-50%);
  }

  &__canvas {
    width: 200px;
    height: 200px;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
  }

  &__logo {
    position: absolute;
    top: 10vh;
    max-width: 40%;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: $z-index-high;
  }

  &__tips {
    position: absolute;
    top: 50vh;
    z-index: $z-index-base;
    display: block;
    width: 100%;
    margin-top: 140px;
    color: $white;
  }

  &__qrcode {
    position: relative;
    height: 100vh;
    background-color: transparent;
    //use border to make a transparency scanner area square and marsk aroung the square
    border-bottom: solid calc(50vh - 100px) rgba(77, 77, 77, 0.5);
    border-top: solid calc(50vh - 100px) rgba(77, 77, 77, 0.5);
    border-right: solid calc(50vw - 100px) rgba(77, 77, 77, 0.5);
    border-left: solid calc(50vw - 100px) rgba(77, 77, 77, 0.5);

    div:nth-child(1) {
      border-left: 5px solid #f5a623;
      border-top: 5px solid #f5a623;
      border-top-left-radius: 10px;
      width: 50px;
      height: 50px;
      left: -5px;
      top: -5px;
      position: absolute;
    }

    div:nth-child(2) {
      border-right: 5px solid #f5a623;
      border-top: 5px solid #f5a623;
      border-top-right-radius: 10px;
      width: 50px;
      height: 50px;
      left: 150px;
      top: -5px;
      position: absolute;
    }

    div:nth-child(3) {
      border-right: 5px solid #f5a623;
      border-bottom: 5px solid #f5a623;
      border-bottom-right-radius: 10px;
      width: 50px;
      height: 50px;
      left: 150px;
      top: 150px;
      position: absolute;
    }

    div:nth-child(4) {
      border-left: 5px solid #f5a623;
      border-bottom: 5px solid #f5a623;
      border-bottom-left-radius: 10px;
      width: 50px;
      height: 50px;
      top: 150px;
      left: -5px;
      position: absolute;
    }
  }
}
/* end of QRScan */

@media (min-width: 770px) {
  /* QRScan */
  .qrscan {
    &__qrcode {
      //use border to make a transparency scanner area square and marsk aroung the square
      border-bottom: solid calc(50vh - 100px) rgba(77, 77, 77, 0.5);
      border-top: solid calc(50vh - 100px) rgba(77, 77, 77, 0.5);
      border-right: solid calc(107px) rgba(77, 77, 77, 0.5);
      border-left: solid calc(107px) rgba(77, 77, 77, 0.5);
    }
  }
  /* end of QRScan */
}
