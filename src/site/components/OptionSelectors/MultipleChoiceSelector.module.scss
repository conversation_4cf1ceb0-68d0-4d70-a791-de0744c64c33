.MultipleChoiceSelectorCheckbox {
  @apply tw-flex-shrink-0;
}

.MultipleChoiceSelectorFooterContainer {
  @apply tw-flex tw-items-center tw-justify-between tw-px-16 sm:tw-px-16px tw-py-8 sm:tw-py-8px tw-gap-x-16 sm:tw-gap-x-16px;

  box-shadow: 0 0 16px rgba(0, 0, 0, 0.1);
}

.MultipleChoiceSelectorFooterResetButton:global(.type-text-ghost) {
  height: 50px;
}

.MultipleChoiceSelectorFooterResetButtonContent {
  @apply tw-leading-relaxed tw-uppercase;
}
