@use "../../../Variables" as *;

.carousel {
  &__wrapper .swiper-wrapper {
    margin-left: 1.4vw;
    margin-right: 1.4vw;
  }

  &__see-all {
    color: $theme-color;
  }

  &__item {
    width: 46.667vw;
    height: auto;
    list-style-type: none;
    cursor: pointer;
  }

  &__image-container {
    position: relative;
    height: 38.65vw;
    overflow: hidden;
  }

  &__item-title {
    max-height: 1.5999rem;
    width: 100%;
    -webkit-line-clamp: 1;
  }

  &__item-info {
    height: 44px;
  }

  &__item-footer {
    margin-top: 2px;
    margin-bottom: 2px;
    height: 22px;
  }

  &__item-rating-text {
    margin-left: 2px;
    margin-right: 2px;
  }
}

@media (min-width: 770px) {
  .carousel {
    &__wrapper .swiper-wrapper {
      margin-left: 8px;
      margin-right: 8px;
    }

    &__item {
      width: 182px;
    }

    &__image-container {
      height: 120px;
    }
  }
}

.carousel-store {
  &__image {
    border-radius: 8px 8px 0 0;
  }
}

.carousel-list {
  &__loader {
    margin: 4vh auto;
  }

  &__item {
    cursor: pointer;
  }

  &__image-container {
    position: relative;
    border: 1px solid $gray-3;
    width: 26vw;
    height: 26vw;
    overflow: hidden;
  }

  &__tag {
    position: absolute;
    left: 0px;
    top: 0px;
    margin: 5px;
  }

  &__image-cover {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    color: $white;
    background-color: rgba(0, 0, 0, 0.35);
    white-space: pre-line;
  }

  &__image {
    width: 100%;
    height: 100%;
  }
}
