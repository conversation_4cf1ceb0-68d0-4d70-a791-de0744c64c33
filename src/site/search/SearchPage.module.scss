.SearchPageHeaderWrapper {
  @apply tw-flex tw-justify-between tw-items-center;
}

.SearchPageIconWrapper {
  @apply tw-flex tw-flex-shrink-0 tw-items-stretch sm:tw-p-12px tw-p-12 tw-border-none tw-outline-none tw-bg-transparent tw-text-gray tw-cursor-pointer;
}

.SearchPageFilterBarWrapper {
  @apply sm:tw-pt-4px tw-pt-4 sm:tw-pb-12px tw-pb-12
    tw-border-0 tw-border-b tw-border-solid tw-border-gray-200;
}

.SearchPageInfoCard {
  @apply tw-flex tw-flex-col tw-items-center;

  padding: 14.1176vh 0;
}

.SearchPageInfoCardImage {
  @apply tw-w-3/10 sm:tw-m-8px tw-m-8;

  max-width: 120px;
}

.SearchPageInfoCardHeading {
  @apply tw-text-base tw-leading-relaxed tw-font-bold tw-text-gray-700 tw-text-center sm:tw-px-16px tw-px-16 sm:tw-py-8px tw-py-8;
}

.SearchPageCategoryDrawerWrapper {
  @apply tw-p-0;

  div:global(.drawer-animation__children) {
    @apply tw-flex tw-flex-col;
  }
}

.SearchPageCategoryDrawerHeaderButton:global(.type-text-ghost) {
  @apply tw-flex-shrink-0 tw--mx-12 sm:tw--mx-12px;
}

.SearchPageCategoryDrawerHeaderButtonContent {
  font-size: 0;
}

.SearchPageCategoryDrawerHeaderTitle {
  @apply tw-flex-1 tw-text-lg tw-leading-relaxed tw-text-center tw-px-16 sm:tw-px-16px tw-font-bold tw-capitalize;
}

@media (min-width: 770px) {
  .SearchPageInfoCard {
    padding: 120px 0;
  }
}
