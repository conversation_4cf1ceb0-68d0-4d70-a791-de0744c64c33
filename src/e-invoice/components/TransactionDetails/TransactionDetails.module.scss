.TransactionDetailsTitle {
  @apply tw-my-16 sm:tw-my-16px tw-text-lg tw-font-bold tw-capitalize;
}

.TransactionDetailsFormItemButtonContainer {
  @apply tw-relative;

  padding-bottom: 23.6px;
}

.TransactionDetailsFormItemButton {
  @apply tw-flex tw-items-center tw-gap-x-8 sm:tw-gap-x-8px tw-py-8 sm:tw-py-8px tw-px-12 sm:tw-px-12px tw-border tw-border-solid tw-border-gray-500 tw-rounded tw-w-full tw-cursor-pointer;
}

.TransactionDetailsFormItemButtonError {
  @apply tw-border-red;
}

.TransactionDetailsFormItemButtonLeft {
  @apply tw-flex-1 tw-flex tw-flex-col tw-gap-2 sm:tw-gap-2px;
}

.TransactionDetailsFormItemLabel {
  @apply tw-flex tw-items-center tw-gap-2 sm:tw-gap-2px tw-text-sm;
}

.TransactionDetailsFormItemLabelText {
  @apply tw-text-sm;
}

.TransactionDetailsFormItemLabelRequired {
  @apply tw-text-red;
}

.TransactionDetailsFormItemText {
  @apply tw-leading-relaxed;

  min-height: 20px;
}

.TransactionDetailsFormItemErrorMessage {
  @apply tw-absolute tw-bottom-0 tw-block tw-my-4 sm:tw-my-4px tw-text-sm tw-text-red;
}

.TransactionDetailsClassificationDrawer {
  @apply tw-p-0;
}

.TransactionDetailsClassificationDrawerCloseButton {
  @apply tw-flex-shrink-0 tw-text-2xl tw-text-gray;
}

.TransactionDetailsClassificationDrawerHeaderTitleContainer {
  @apply tw-flex tw-flex-col tw-items-center;
}

.TransactionDetailsClassificationDrawerHeaderTitle {
  @apply tw-font-bold tw-text-lg tw-leading-relaxed;
}
