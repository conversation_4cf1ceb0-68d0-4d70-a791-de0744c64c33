html:global(#e-invoice-html) {
  @apply tw-relative tw-min-h-full;
}

.EInvoiceContainer {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-between tw-w-full tw-h-full;

  padding-bottom: 68px;
  min-height: inherit;
}

.EInvoiceDetail {
  @apply tw-flex-1 tw-w-full;
}

.EInvoiceTitle {
  @apply tw-p-16 sm:tw-p-16px tw-text-3xl tw-leading-normal tw-font-bold;
}

.EInvoiceFooter {
  @apply tw-absolute tw-left-0 tw-right-0 tw-bottom-0 tw-mx-auto tw-text-center tw-p-16 sm:tw-p-16px tw-w-full;

  max-width: 414px;
}

.EInvoiceFooterText,
.EInvoiceFooterPowerByText {
  @apply tw-leading-normal;
}

.EInvoiceFooterPowerByText {
  @apply tw-text-sm;
}

.EInvoiceFooterBrand {
  @apply tw-text-sm tw-text-orange tw-font-bold;
}

.EInvoiceRejectAlertContent {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-center tw-leading-loose;
}

.EInvoiceRejectAlertIcon {
  @apply tw-mb-8 sm:tw-mb-8px tw-rounded-full tw-overflow-hidden;

  width: 80px;
  height: 80px;

  > figure {
    @apply tw-bg-transparent;
  }
}

.EInvoiceRejectAlertTitle {
  @apply tw-flex tw-justify-center tw-text-center tw-m-auto tw-text-xl tw-leading-normal tw-text-gray-800 tw-font-bold;

  max-width: 264px;
  word-break: break-word;
}

.EInvoiceRejectAlertContent {
  @apply tw-leading-loose;
}
