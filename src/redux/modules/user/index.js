import { createSlice } from '@reduxjs/toolkit';
import config from '../../../config';
import { API_REQUEST_STATUS } from '../../../common/utils/constants';
import {
  fetchUserLoginStatus,
  fetchUserProfileInfo,
  uploadUserProfileInfo,
  loginUser,
  loginUserAsGuest,
  logoutUser,
} from './thunks';

const initialState = {
  checkLoginRequest: {
    data: {
      consumerId: null,
      login: false,
    },
    status: null,
    error: null,
  },
  loginRequest: {
    data: {
      isFirstLogin: false,
    },
    status: null,
    error: null,
  },
  guestLoginRequest: {
    data: {
      isGuest: config.isGuest,
    },
    status: null,
    error: null,
  },
  logoutRequest: {
    data: {},
    status: null,
    error: null,
  },
  loadProfileRequest: {
    data: {
      id: null,
      phone: null,
      birthday: null,
      email: null,
      firstName: null,
      lastName: null,
      birthdayChangeAllowed: false,
      birthdayModifiedTime: null,
      gender: null,
      notificationSettings: null,
    },
    status: null,
    error: null,
  },
  uploadProfileRequest: {
    status: null,
    error: null,
  },
};

const { reducer, actions } = createSlice({
  name: 'app/address',
  initialState,
  reducers: {},
  extraReducers: {
    [fetchUserLoginStatus.pending.type]: state => {
      state.checkLoginRequest.status = API_REQUEST_STATUS.PENDING;
      state.checkLoginRequest.error = null;
    },
    [fetchUserLoginStatus.fulfilled.type]: (state, { payload }) => {
      const { consumerId, login } = payload;

      state.checkLoginRequest.data.consumerId = consumerId;
      state.checkLoginRequest.data.login = login;

      state.checkLoginRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.checkLoginRequest.error = null;
    },
    [fetchUserLoginStatus.rejected.type]: (state, { error }) => {
      state.checkLoginRequest.status = API_REQUEST_STATUS.REJECTED;
      state.checkLoginRequest.error = error;
    },
    [loginUser.pending.type]: state => {
      state.loginRequest.status = API_REQUEST_STATUS.PENDING;
      state.loginRequest.error = null;
    },
    [loginUser.fulfilled.type]: (state, { payload }) => {
      const { isFirstLogin } = payload;

      state.loginRequest.data.isFirstLogin = isFirstLogin;

      state.loginRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loginRequest.error = null;
    },
    [loginUser.rejected.type]: (state, { error }) => {
      state.loginRequest.status = API_REQUEST_STATUS.REJECTED;
      state.loginRequest.error = error;
    },
    [fetchUserProfileInfo.pending.type]: state => {
      state.loadProfileRequest.status = API_REQUEST_STATUS.PENDING;
      state.loadProfileRequest.error = null;
    },
    [fetchUserProfileInfo.fulfilled.type]: (state, { payload }) => {
      const {
        phone,
        birthday,
        email,
        firstName,
        lastName,
        birthdayChangeAllowed,
        birthdayModifiedTime,
        gender,
        notificationSettings,
      } = payload;

      state.loadProfileRequest.data.phone = phone;
      state.loadProfileRequest.data.birthday = birthday;
      state.loadProfileRequest.data.email = email;
      state.loadProfileRequest.data.firstName = firstName;
      state.loadProfileRequest.data.lastName = lastName;
      state.loadProfileRequest.data.birthdayChangeAllowed = birthdayChangeAllowed;
      state.loadProfileRequest.data.birthdayModifiedTime = birthdayModifiedTime;
      state.loadProfileRequest.data.gender = gender;
      state.loadProfileRequest.data.notificationSettings = notificationSettings;

      state.loadProfileRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loadProfileRequest.error = null;
    },
    [fetchUserProfileInfo.rejected.type]: (state, { error }) => {
      state.loadProfileRequest.status = API_REQUEST_STATUS.REJECTED;
      state.loadProfileRequest.error = error;
    },
    [uploadUserProfileInfo.pending.type]: state => {
      state.uploadProfileRequest.status = API_REQUEST_STATUS.PENDING;
      state.uploadProfileRequest.error = null;
    },
    [uploadUserProfileInfo.fulfilled.type]: state => {
      state.uploadProfileRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.uploadProfileRequest.error = null;
    },
    [uploadUserProfileInfo.rejected.type]: (state, { error }) => {
      state.uploadProfileRequest.status = API_REQUEST_STATUS.REJECTED;
      state.uploadProfileRequest.error = error;
    },
    [loginUserAsGuest.pending.type]: state => {
      state.guestLoginRequest.status = API_REQUEST_STATUS.PENDING;
      state.guestLoginRequest.error = null;
    },
    [loginUserAsGuest.fulfilled.type]: state => {
      state.guestLoginRequest.data.isGuest = true;
      state.guestLoginRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.guestLoginRequest.error = null;
    },
    [loginUserAsGuest.rejected.type]: (state, { error }) => {
      state.guestLoginRequest.status = API_REQUEST_STATUS.REJECTED;
      state.guestLoginRequest.error = error;
    },
    [logoutUser.pending.type]: state => {
      state.logoutRequest.status = API_REQUEST_STATUS.PENDING;
      state.logoutRequest.error = null;
    },
    [logoutUser.fulfilled.type]: state => {
      // Reset all user-related state on successful logout
      state.checkLoginRequest.data.consumerId = null;
      state.checkLoginRequest.data.login = false;
      state.checkLoginRequest.status = null;
      state.checkLoginRequest.error = null;

      state.loginRequest.data.isFirstLogin = false;
      state.loginRequest.status = null;
      state.loginRequest.error = null;

      state.guestLoginRequest.data.isGuest = config.isGuest;
      state.guestLoginRequest.status = null;
      state.guestLoginRequest.error = null;

      state.loadProfileRequest.data.id = null;
      state.loadProfileRequest.data.phone = null;
      state.loadProfileRequest.data.birthday = null;
      state.loadProfileRequest.data.email = null;
      state.loadProfileRequest.data.firstName = null;
      state.loadProfileRequest.data.lastName = null;
      state.loadProfileRequest.data.birthdayChangeAllowed = false;
      state.loadProfileRequest.data.birthdayModifiedTime = null;
      state.loadProfileRequest.data.gender = null;
      state.loadProfileRequest.data.notificationSettings = null;
      state.loadProfileRequest.status = null;
      state.loadProfileRequest.error = null;

      state.uploadProfileRequest.status = null;
      state.uploadProfileRequest.error = null;

      state.logoutRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.logoutRequest.error = null;
    },
    [logoutUser.rejected.type]: (state, { error }) => {
      state.logoutRequest.status = API_REQUEST_STATUS.REJECTED;
      state.logoutRequest.error = error;
    },
  },
});

export default reducer;
export { actions };
