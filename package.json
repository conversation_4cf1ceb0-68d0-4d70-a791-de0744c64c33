{"name": "beep", "version": "0.1.0", "private": true, "engines": {"node": ">=18.19.0"}, "lint-staged": {"src/**/*.(js|jsx|scss)": ["prettier --write", "git add"]}, "prettier": {"trailingComma": "es5", "singleQuote": true, "tabWidth": 2, "useTabs": false, "printWidth": 120, "overrides": [{"files": "*.scss", "options": {"singleQuote": false}}]}, "dependencies": {"@craco/craco": "^6.4.3", "@dimak.dev/event-source-mock": "^1.0.0", "@googlemaps/js-api-loader": "^1.14.0", "@growthbook/growthbook": "^1.5.1", "@reduxjs/toolkit": "^1.6.2", "@sentry/integrations": "^7.18.0", "@sentry/react": "^7.18.0", "@sentry/utils": "^7.18.0", "@sentry/webpack-plugin": "^1.20.0", "@storehub/frontend-utils": "^0.0.7", "@stripe/react-stripe-js": "^1.1.2", "@stripe/stripe-js": "^1.4.0", "@tailwindcss/line-clamp": "^0.3.1", "autoprefixer": "^10.4.21", "aws-sdk": "^2.706.0", "bowser": "^2.11.0", "connected-react-router": "^6.9.2", "craco-esbuild": "^0.5.0", "dayjs": "^1.9.7", "debug": "^4.1.1", "deploy-aws-s3-cloudfront": "^3.2.3", "dsbridge": "^3.1.4", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-rulesdir": "^0.2.2", "fs-extra": "^8.1.0", "glob": "^7.1.6", "globalthis": "^1.0.3", "history": "4.10.1", "hoist-non-react-statics": "^3.3.2", "i18n-sync": "^1.3.3", "i18next": "^19.0.2", "i18next-browser-languagedetector": "^4.0.1", "i18next-xhr-backend": "^3.2.2", "inobounce": "^0.2.0", "intersection-observer": "^0.12.2", "invariant": "^2.2.4", "js-cookie": "^3.0.1", "keen-slider": "^6.8.3", "ky": "^1.8.1", "libphonenumber-js": "^1.9.19", "lodash": "^4.17.21", "normalize.css": "^8.0.1", "phosphor-react": "^1.4.0", "playwright-bdd": "^8.2.1", "postcss": "^8.5.3", "prop-types": "^15.7.2", "qrcode-decoder": "^0.1.1", "qs": "^6.10.1", "react": "17.0.2", "react-async-script": "^1.2.0", "react-dom": "17.0.2", "react-hook-inview": "^4.4.1", "react-i18next": "^11.14.0", "react-infinite-scroller": "^1.2.4", "react-lazyload": "^2.6.5", "react-loading-skeleton": "^3.3.1", "react-phone-number-input": "^3.1.6", "react-redux": "^7.2.6", "react-router-dom": "^5.0.0", "react-scripts": "^5.0.1", "react-simple-pull-to-refresh": "^1.2.3", "react-textarea-autosize": "^8.3.4", "react-transition-group": "^4.4.2", "react-use": "^17.3.1", "redux": "^4.1.2", "redux-thunk": "^2.4.0", "reselect": "^4.1.2", "sass": "^1.70.0", "serialize-error": "^8.1.0", "smoothscroll-polyfill": "^0.4.4", "spherical-geometry-js": "^2.0.0", "tailwindcss": "^3.4.17", "web-vitals": "^3.1.0", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "whatwg-fetch": "^3.6.2", "yargs": "^15.3.1"}, "scripts": {"analyze": "source-map-explorer 'build/static/js/*.js' --no-border-checks", "analyze:bundle-stats": "webpack-bundle-analyzer build/bundle-stats.json", "pre:env": "node copy-env.js", "start": "TAILWIND_MODE=watch craco start", "start:https": "./scripts/ssl-certs/check-dev-ssl-cert.sh && HTTPS=true SSL_CRT_FILE=.cert/cert.pem SSL_KEY_FILE=.cert/key.pem yarn start", "start:mock-server": "source .env && MOCK_ORIGINAL_SERVER_PROXY=$PROXY PROXY=http://localhost:3004 run-p \"mock-server\" \"start\"", "start:serve-prod-build": "node ./scripts/serve-prod-build.js", "build-sw": "node ./src/sw-build.js", "clean-cra-sw": "rm -f build/precache-manifest.*.js && rm -f build/service-worker.js", "build": "craco build && npm run build:postprocess && npm run build:sourcemap", "build:postprocess": "node ./scripts/postprocess-build/index.js", "build:stats": "craco build --stats && npm run build:postprocess && npm run build:sourcemap", "eslint": "eslint ./src", "prepush": "sh ../git_hooks/copy", "test": "craco test", "test:watch": "craco test --watchAll", "test:coverage": "craco test --coverage --watchAll=false", "test:debug": "craco --inspect-brk test --runInBand --no-cache", "eject": "craco eject", "deploy": "deploy-aws-s3-cloudfront --source ./build --cache-control index.html:no-cache static/**/*:max-age=2592000 i18n/**/*:max-age=2592000", "i18n-sync": "i18n-sync", "build:sourcemap": "node scripts/build-sourcemap --serve-sourcemap-url http://localhost:8080", "serve:sourcemap": "node scripts/serve-sourcemap --port 8080", "mock-server": "MOCK_SERVER_PORT=3004 frontend-mock-server", "generate-dev-ssl-cert": "./scripts/ssl-certs/generate-dev-ssl-cert.sh", "prepare": "husky install", "lint-staged": "lint-staged", "commitlint": "commitlint --edit", "commit-prettier": "commit-prettier --branch"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "devDependencies": {"@commitlint/cli": "^16.0.0", "@commitlint/config-conventional": "^16.0.0", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "babel-loader": "^8.1.0", "chalk": "^4.1.0", "commit-prettier": "^1.1.1", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "express": "^4.17.1", "frontend-mock-server": "^1.4.2", "http-proxy-middleware": "^1.0.6", "husky": "^7.0.0", "identity-obj-proxy": "^3.0.0", "jest-fetch-mock": "^3.0.3", "less-loader": "^6.1.1", "lint-staged": "^9.5.0", "list-imports-exports": "^0.1.2", "node-fetch": "^2.6.1", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "proxy-agent": "^3.1.1", "react-test-renderer": "^16.12.0", "redux-mock-store": "^1.5.4", "source-map-explorer": "^2.3.1", "webpack-bundle-analyzer": "^4.7.0", "workbox-build": "^4.3.0"}, "resolutions": {"sass-loader": "13.3.3", "babel-jest": "^27.5.1", "cheerio": "0.22.0"}}