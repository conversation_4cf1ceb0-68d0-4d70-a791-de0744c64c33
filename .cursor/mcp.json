{"mcpServers": {"Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--st<PERSON>"], "env": {"FIGMA_API_KEY": "${FIGMA_ACCESS_TOKEN}"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}", "GITHUB_REPOSITORIES": "${GITHUB_REPOSITORIES}"}}, "mcp-atlassian": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "JIRA_PROJECTS_FILTER", "-e", "ENABLED_TOOLS", "-e", "JIRA_URL", "-e", "JIRA_USERNAME", "-e", "JIRA_API_TOKEN", "ghcr.io/sooperset/mcp-atlassian:latest"], "env": {"JIRA_URL": "${JIRA_URL}", "JIRA_USERNAME": "${JIRA_USERNAME}", "JIRA_API_TOKEN": "${JIRA_API_TOKEN}", "JIRA_PROJECTS_FILTER": "${JIRA_PROJECTS_FILTER}", "ENABLED_TOOLS": "${ENABLED_TOOLS}"}}, "lark": {"command": "npx", "args": ["-y", "lark-mcp"], "env": {"LARK_APP_ID": "${LARK_APP_ID}", "LARK_APP_SECRET": "${LARK_APP_SECRET}", "LARK_BASE_URL": "${LARK_BASE_URL}"}}}}