---
description:
globs:
alwaysApply: true
---
# File Reference Rules

::alert{type="info"}
This rule defines the file reference order and best practices in components
::

## 🏗️ Architecture Constraint Rules

::alert{type="danger"}
**Important Architecture Constraint**: Strictly control the reference relationships between folders to maintain clear dependency boundaries
::

### ✅ Globally Referenceable Folders

The following folders under `src/` can be referenced by files in other folders:

```
src/
├── common/          ✅ Globally referenceable
├── components/      ✅ Globally referenceable
├── containers/      ✅ Globally referenceable
├── images/          ✅ Globally referenceable
├── redux/           ✅ Globally referenceable
└── utils/           ✅ Globally referenceable
```

### ❌ Non-Cross-Folder Referenceable Folders

Files in other folders under `src/` (such as `pages/`, `modules/`, `features/`, `ordering/`, `rewards/`, etc.) **cannot** be referenced by files in other folders under `src/`.

```javascript
// ❌ Wrong approach - Cross-folder references
import { OrderingComponent } from '../../ordering/components/OrderingComponent'
import { RewardsUtil } from '../../rewards/utils/helper'
import { PagesConstant } from '../../pages/constants'

// ✅ Correct approach - Only reference allowed global folders
import Button from '../../common/components/Button'
import { formatDate } from '../../utils/helpers'
import { API_ENDPOINTS } from '../../common/constants'
```

### 🔒 Containers Special Constraints

There are special constraints between folders under `src/containers/`:

- ❌ **Files in folders under containers cannot reference each other**
- ✅ **But can reference files in the aforementioned globally referenceable folders**

```javascript
// ✅ Correct approach - Containers referencing global folders
import Button from '../../common/components/Button'
import { getUser } from '../../redux/selectors'
import { formatDate } from '../../utils/helpers'
```

### 📋 Advantages of Architecture Constraints

- 🏗️ **Maintain clear architectural boundaries**: Prevent tight coupling between modules
- 🔄 **Avoid circular dependencies**: Ensure unidirectional dependency flow
- 📦 **Maintain module independence**: Improve code maintainability and testability
- 🎯 **Enforce separation of concerns**: Each module focuses on its own responsibilities

## 📝 Import Formatting Rules

::alert{type="warning"}
**Mandatory**: Follow strict formatting rules for import statements to maintain consistency
::

### No Empty Lines Between Import Groups

::alert{type="info"}
**Important Rule**: Do not add empty lines between different import groups/sections
::

All import statements should be written consecutively without empty lines between different categories.

#### ✅ Correct Import Formatting

```javascript
// Third-party packages
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
// Static resources
import LogoImage from '../images/logo.png'
import BackgroundImage from '../images/background.jpg'
// Constants
import { API_ENDPOINTS } from '../../common/constants'
import { VALIDATION_RULES } from '../../utils/constants'
// Utils
import { formatDate } from '../../utils/helpers'
import { validateEmail } from '../../utils/validators'
// Redux
import { getUser } from '../../redux/selectors'
import { fetchUserData } from '../../redux/thunks'
// Components
import Button from '../../common/components/Button'
import Modal from '../../common/components/Modal'
// Local files
import LocalComponent from './components/LocalComponent'
// Styles
import styles from './ComponentName.module.scss'
```

#### ❌ Wrong Import Formatting

```javascript
// Third-party packages
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import React, { useState, useEffect } from 'react'

// ❌ Empty line - Not allowed
import { useSelector, useDispatch } from 'react-redux'

// ❌ Empty line - Not allowed

// Static resources
import LogoImage from '../images/logo.png'

// ❌ Empty line - Not allowed

// Constants
import { API_ENDPOINTS } from '../../common/constants'

// ❌ Empty line - Not allowed

// Components
import Button from '../../common/components/Button'
```

### Benefits of No Empty Lines

- 🧹 **Cleaner code**: More compact and easier to scan
- 📦 **Consistent formatting**: Standardized import section across all files
- 🔍 **Better readability**: Clear separation through comments only
- 🚀 **Efficient use of space**: Reduces file length without losing clarity

::alert{type="success"}
Following this formatting rule ensures consistent and clean import sections across the entire codebase
::

## 📦 Third-Party Package Reference Order

Reference third-party packages in the following priority order:

```javascript
// 1. Packages that don't depend on React
// lodash sub-method imports, uniformly named with underscore prefix
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import _isEqual from 'lodash/isEqual'
import _cloneDeep from 'lodash/cloneDeep'
import _debounce from 'lodash/debounce'
import _uniq from 'lodash/uniq'
import _sumBy from 'lodash/sumBy'
import _floor from 'lodash/floor'
import _replace from 'lodash/replace'
import qs from 'qs'
import dayjs from 'dayjs'
import debug from 'debug'
import yargs from 'yargs'
import glob from 'glob'
import smoothscroll from 'smoothscroll-polyfill'
import 'whatwg-fetch'
import globalthis from 'globalthis'
import 'intersection-observer'
import iNoBounce from 'inobounce'
import invariant from 'invariant'
import Cookies from 'js-cookie'
import bowser from 'bowser'
import 'serialize-error'
import ky from 'ky'
import 'aws-sdk'
import libphonenumber from 'libphonenumber-js'
import { Loader } from '@googlemaps/js-api-loader'
import 'spherical-geometry-js'
import { GrowthBook } from '@growthbook/growthbook'
import dsBridge from 'dsbridge'

// 2. React core packages
import React from 'react'
import ReactDOM from 'react-dom'

// 3. React-related packages
import { BrowserRouter } from 'react-router-dom'
import i18next from 'i18next'
import Backend from 'i18next-xhr-backend'
import LanguageDetector from 'i18next-browser-languagedetector'
import { Star, MapPin, MagnifyingGlass, XCircle, CaretDown, FunnelSimple, X, CaretLeft, WarningCircle, CircleNotch, PlusCircle, MinusCircle, Headset, Clock } from 'phosphor-react'
import 'keen-slider'

// 4. Redux core packages
import { Provider } from 'react-redux'
import { createStore } from 'redux'

// 5. Redux Toolkit packages
import { configureStore } from '@reduxjs/toolkit'
import { createSlice } from '@reduxjs/toolkit'

// 6. Redux-related packages
import { persistStore } from 'redux-persist'
import thunk from 'redux-thunk'
```

## 🖼️ Static Resource Imports

```javascript
// Image imports - All images end with Image
import HeaderImage from '../images/header.png'
import LogoImage from '../images/logo.svg'
import BackgroundImage from '../images/background.jpg'
```

## 📋 Constants Import Order

According to architecture constraint rules, only reference allowed global folders:

```javascript
// 1. Constants under common/
import { API_ENDPOINTS } from '../../common/constants'

// 2. Constants under utils/
import { VALIDATION_RULES } from '../../utils/constants'

// 3. Constants under current folder utils/
import { FILE_CONSTANTS } from './utils/localConstants'
```

::alert{type="warning"}
**Note**: According to architecture constraints, cannot reference constants from other business module folders
::

## 🔧 Utils Import Order

::alert{type="info"}
**Important Rule**: Files under `src/utils/` are public utility methods and should be placed in the public utils import section. However, `common/utils/feedback` is an exception as it contains component-related utilities
::

### Standard Utils Import Order

```javascript
// 1. Utils under src/utils/ (public utility methods)
import { formatDate } from '../../utils/helpers'
import { validateEmail } from '../../utils/validators'
import { API_CONFIG } from '../../utils/config'

// 2. Utils under common/utils/ (excluding feedback)
import { calculateDistance } from '../../common/utils/location'
import { encryptData } from '../../common/utils/security'

// 3. Utils under current folder utils/
import { localHelper } from './utils/helper'
```

### Special Exception: Feedback Utils

`common/utils/feedback` contains component-like utilities and should be placed in the **component import section** rather than the utils section:

```javascript
// ❌ Wrong - Placing feedback utils in utils section
import { formatDate } from '../../utils/helpers'
import { alert, toast, confirm } from '../../common/utils/feedback'  // Should not be here

// ✅ Correct - Place feedback utils in component section
// ... other imports ...

// Components section
import Button from '../../common/components/Button'
import Modal from '../../common/components/Modal'
import { alert, toast, confirm } from '../../common/utils/feedback'  // Correct position
```

### Complete Example with Feedback Utils

```javascript
// Third-party packages
import _get from 'lodash/get'
import React, { useState } from 'react'

// Static resources
import LogoImage from '../images/logo.png'

// Constants
import { API_ENDPOINTS } from '../../common/constants'

// Utils (excluding feedback)
import { formatDate } from '../../utils/helpers'
import { validateEmail } from '../../utils/validators'
import { calculateDistance } from '../../common/utils/location'

// Redux
import { getUser } from '../../redux/selectors'

// Components (including feedback utilities)
import Button from '../../common/components/Button'
import Modal from '../../common/components/Modal'
import { alert, toast, confirm } from '../../common/utils/feedback'

// Local files
import LocalComponent from './components/LocalComponent'

// Styles
import styles from './MyComponent.module.scss'
```

### Why Feedback is an Exception

- 🧩 **Component-like behavior**: `alert`, `toast`, `confirm` render UI components
- 📱 **UI interaction**: These utilities directly interact with the user interface
- 🎯 **Component dependency**: Often used alongside other UI components
- 🔧 **Functional similarity**: Similar to importing other UI component utilities

::alert{type="success"}
Following this rule ensures logical grouping of imports while maintaining clear separation between pure utility functions and component-related utilities
::

## 🌍 Country Constants Usage Rules

::alert{type="info"}
**Important Rule**: Country constants must be imported from the correct source file based on their usage context
::

### 📱 Phone-Related Country Constants

For phone number validation, country codes, and phone-related functionality, use constants from `phone-number-constants.js`:

```javascript
// ✅ Correct - Phone-related country constants
import {
  COUNTRIES,
  PHONE_NUMBER_COUNTRIES,
  AVAILABLE_COUNTRIES,
  SYSTEM_DEFAULT_COUNTRY
} from '../../common/utils/phone-number-constants'

// Usage examples:
const phoneCountryCode = PHONE_NUMBER_COUNTRIES[COUNTRIES.MY] // '60'
const defaultCountry = SYSTEM_DEFAULT_COUNTRY // 'MY'
const availableCountries = AVAILABLE_COUNTRIES // ['MY', 'TH', 'PH', 'SG', 'CN', 'BN']
```

### 🌐 General Country Constants

For currency, locale, and other general country-related functionality, use constants from `constants.js`:

```javascript
// ✅ Correct - General country constants
import {
  COUNTRIES,
  COUNTRIES_DEFAULT_CURRENCIES,
  COUNTRIES_DEFAULT_LOCALE
} from '../../common/utils/constants'

// Usage examples:
const currency = COUNTRIES_DEFAULT_CURRENCIES[COUNTRIES.MY] // 'MYR'
const locale = COUNTRIES_DEFAULT_LOCALE[COUNTRIES.MY] // 'MS-MY'
```

### 🔄 Mixed Usage Scenarios

When you need both phone-related and general country constants, import from both files with aliases:

```javascript
// ✅ Correct - Mixed usage with aliases
import {
  COUNTRIES as PHONE_COUNTRIES,
  PHONE_NUMBER_COUNTRIES
} from '../../common/utils/phone-number-constants'
import {
  COUNTRIES,
  COUNTRIES_DEFAULT_CURRENCIES
} from '../../common/utils/constants'

// Usage examples:
const phoneCode = PHONE_NUMBER_COUNTRIES[PHONE_COUNTRIES.MY] // '60'
const currency = COUNTRIES_DEFAULT_CURRENCIES[COUNTRIES.MY] // 'MYR'
```

### ❌ Wrong Usage Examples

```javascript
// ❌ Wrong - Using general constants for phone functionality
import { COUNTRIES } from '../../common/utils/constants'
// This doesn't have PHONE_NUMBER_COUNTRIES mapping

// ❌ Wrong - Using phone constants for currency/locale
import { COUNTRIES } from '../../common/utils/phone-number-constants'
// This doesn't have COUNTRIES_DEFAULT_CURRENCIES or COUNTRIES_DEFAULT_LOCALE
```

### 📋 Available Constants by File

#### `phone-number-constants.js`
- `COUNTRIES` - Country codes (MY, TH, PH, SG, CN, BN)
- `PHONE_NUMBER_COUNTRIES` - Country to phone code mapping
- `AVAILABLE_COUNTRIES` - Array of available countries
- `SYSTEM_DEFAULT_COUNTRY` - Default country (MY)

#### `constants.js`
- `COUNTRIES` - Country codes (MY, TH, PH, SG, CN, BN)
- `COUNTRIES_DEFAULT_CURRENCIES` - Country to currency mapping
- `COUNTRIES_DEFAULT_LOCALE` - Country to locale mapping

### 🎯 Benefits

- 🎯 **Clear separation of concerns**: Phone vs general country functionality
- 📦 **Smaller bundle size**: Import only what you need
- 🔍 **Better maintainability**: Easy to locate and update specific functionality
- 🚀 **Improved performance**: Reduced import overhead

## 🔄 Redux-Related File Imports

For selectors, index, and thunks, reference according to architecture constraint rules:

```javascript
// 1. Files under src/redux/ (globally referenceable)
import { getUser } from '../../redux/selectors'
import store from '../../redux/index'
import { fetchUser } from '../../redux/thunks'

// 2. Files under current folder redux/
import { getCurrentData } from './redux/selectors'
import currentStore from './redux/index'
import { fetchCurrentData } from './redux/thunks'
```

::alert{type="warning"}
**Note**: According to architecture constraints, cannot cross-reference redux files between business modules
::

## 🧩 Component Import Order

According to architecture constraint rules, only reference allowed global folders:

```javascript
// 1. Components from src/common/components/
import Button from '../../common/components/Button'
import Modal from '../../common/components/Modal'

// 2. Components from src/components/
import { Header } from '../../components/Header'
import { Footer } from '../../components/Footer'

// 3. Components from src/containers/ (current container only, no cross-container references)
import { CurrentContainer } from '../currentContainer'

// 4. Files under current folder components/
import { LocalComponent } from './components/LocalComponent'
```

::alert{type="warning"}
**Note**: According to architecture constraints, cannot reference components from other business module folders under `src/` (such as `ordering/`, `rewards/`, `pages/`, etc.), and folders under containers cannot reference each other
::

## 🎨 Style File Imports

```javascript
// Only import module.scss files with the same name as the current file
import styles from './ComponentName.module.scss'
```

## ✅ Complete Example

```javascript
// Third-party packages - lodash sub-method imports (named with underscore prefix)
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import _debounce from 'lodash/debounce'

// Third-party packages - React core
import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { BrowserRouter as Router } from 'react-router-dom'

// Static resources
import LogoImage from '../images/logo.png'

// Constants (only globally referenceable folders)
import { API_ENDPOINTS } from '../../common/constants'
import { VALIDATION_RULES } from '../../utils/constants'

// Utils (excluding feedback - feedback goes in components section)
import { formatDate } from '../../utils/helpers'
import { validateEmail } from '../../utils/validators'
import { calculateDistance } from '../../common/utils/location'

// Redux (only globally referenceable folders)
import { getUser } from '../../redux/selectors'
import store from '../../redux/index'
import { fetchUser } from '../../redux/thunks'

// Components (only globally referenceable folders, including feedback utilities)
import Button from '../../common/components/Button'
import { Header } from '../../components/Header'
import { toast, alert } from '../../common/utils/feedback'

// Current folder components
import LocalComponent from './components/LocalComponent'

// Styles
import styles from './MyComponent.module.scss'

const MyComponent = () => {
  const [data, setData] = useState({})

  // Use lodash methods (with underscore prefix)
  const isEmpty = _isEmpty(data)
  const userName = _get(data, 'user.name', 'Anonymous')

  const debouncedSearch = _debounce((searchTerm) => {
    // Search logic
  }, 300)

  const handleSubmit = () => {
    if (validateEmail(data.email)) {
      toast('Email is valid')
    } else {
      alert('Please enter a valid email')
    }
  }

  // Component logic
}

MyComponent.displayName = 'MyComponent'

export default MyComponent
```

::alert{type="success"}
Following these rules maintains code consistency and maintainability while ensuring clear architectural boundaries
::

## 🔧 Lodash Reference Convention

::alert{type="warning"}
**Important Convention**: All lodash methods must be imported as sub-methods and uniformly named with underscore prefix
::

```javascript
// ✅ Correct approach - Sub-method imports, underscore prefix naming
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import _isEqual from 'lodash/isEqual'
import _cloneDeep from 'lodash/cloneDeep'
import _debounce from 'lodash/debounce'
import _uniq from 'lodash/uniq'
import _sumBy from 'lodash/sumBy'
import _floor from 'lodash/floor'
import _replace from 'lodash/replace'
import _isNil from 'lodash/isNil'
import _some from 'lodash/some'
import _zipWith from 'lodash/zipWith'
import _isFunction from 'lodash/isFunction'
import _isArray from 'lodash/isArray'
import _lowerCase from 'lodash/lowerCase'
import _once from 'lodash/once'
import _isNaN from 'lodash/isNaN'

// ❌ Wrong approach - Whole package import
import lodash from 'lodash'
import { get, isEmpty } from 'lodash'

// ❌ Wrong approach - Not using underscore prefix naming
import get from 'lodash/get'
import isEmpty from 'lodash/isEmpty'
```

**Advantages**:
- 🚀 Smaller bundle size (only import needed methods)
- 🎯 Clear naming convention (immediately recognizable as lodash methods)
- 🔍 Better code readability and maintainability

## 📍 Relative Path Examples by Location

### From `src/ordering/containers/App/`

```javascript
// Constants
import { API_ENDPOINTS } from '../../../common/constants'

// Utils (excluding feedback)
import { formatDate } from '../../../utils/helpers'
import { validateData } from '../../../utils/validators'

// Redux
import { getUser } from '../../../redux/selectors'

// Components (including feedback utilities)
import Button from '../../../common/components/Button'
import { alert, toast } from '../../../common/utils/feedback'

// Images
import LogoImage from '../../../images/logo.png'

// Other containers (not allowed)
// ❌ import { Header } from '../../Header/HeaderContainer'

// Local files
import LocalComponent from './components/LocalComponent'
import { LOCAL_CONSTANTS } from './utils/constants'
```

### From `src/rewards/containers/Home/`

```javascript
// Constants
import { API_ENDPOINTS } from '../../../common/constants'

// Utils (excluding feedback)
import { formatDate } from '../../../utils/helpers'
import { validateData } from '../../../utils/validators'
import { calculateDistance } from '../../../common/utils/location'

// Redux
import { getUser } from '../../../redux/selectors'

// Components (including feedback utilities)
import Button from '../../../common/components/Button'
import { alert, toast, confirm } from '../../../common/utils/feedback'

// Images
import LogoImage from '../../../images/logo.png'

// Local files
import LocalComponent from './components/LocalComponent'
import { LOCAL_CONSTANTS } from './utils/constants'
```

### From `src/common/components/Button/`

```javascript
// Constants
import { API_ENDPOINTS } from '../constants'

// Utils (excluding feedback)
import { formatDate } from '../../utils/helpers'

// Redux
import { getUser } from '../../../redux/selectors'

// Components (including feedback utilities)
import Button from '../../common/components/Button'
import { toast, alert } from '../../common/utils/feedback'

// Images
import IconImage from '../../../images/icon.png'

// Local files
import styles from './Button.module.scss'
```

### From `src/components/Header/`

```javascript
// Constants
import { API_ENDPOINTS } from '../../common/constants'

// Utils (excluding feedback)
import { formatDate } from '../../utils/helpers'
import { validateInput } from '../../utils/validators'

// Redux
import { getUser } from '../../redux/selectors'

// Components (including feedback utilities)
import Button from '../../common/components/Button'
import { toast, alert } from '../../common/utils/feedback'

// Images
import LogoImage from '../../images/logo.png'

// Local files
import styles from './Header.module.scss'
```

::alert{type="info"}
**Path Calculation Tip**: Count the number of `../` needed to go back to `src/` folder, then navigate to the target folder
::

## 🧹 Import Cleanup Rules

> **Critical Rule:**
> After making code modifications, always remove unused imports and require statements to maintain clean, efficient code

### 📋 Mandatory Cleanup Guidelines

#### When to Remove Imports
1. **After code refactoring**: Remove imports that are no longer referenced
2. **After component deletion**: Clean up imports pointing to deleted components
3. **After functionality changes**: Remove imports for unused utilities, constants, or components
4. **After library updates**: Remove deprecated or replaced package imports

#### Import Removal Checklist
- [ ] **Scan for unused variables**: Check if imported variables are actually used in the code
- [ ] **Remove orphaned imports**: Delete imports without corresponding usage
- [ ] **Clean up type imports**: Remove unused TypeScript type/interface imports
- [ ] **Remove debugging imports**: Delete temporary imports used for development/debugging
- [ ] **Update dependency arrays**: Remove unused dependencies from React hooks (useEffect, useCallback, useMemo)

### ✅ Examples of Proper Import Cleanup

#### Before Cleanup (❌ Wrong)
```javascript
// ❌ Contains unused imports
import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import _debounce from 'lodash/debounce'
import Button from '../../common/components/Button'
import Modal from '../../common/components/Modal'
import InputText from '../../common/components/Input/Text'
import { formatDate, validateEmail, calculateAge } from '../../utils/helpers'
import { getUserData, getOrderHistory, getRewards } from '../../redux/selectors'
import { toast, alert, confirm } from '../../common/utils/feedback'
import styles from './MyComponent.module.scss'

const MyComponent = () => {
  const { t } = useTranslation(['User'])
  const userData = useSelector(getUserData)

  const handleSubmit = () => {
    toast('Submitted successfully')
  }

  return (
    <div className={styles.MyComponentContainer}>
      <Button
        onClick={handleSubmit}
        data-test-id="folder.container.my-component.submit-button"
      >
        {t('SubmitButton')}
      </Button>
    </div>
  )
}

export default MyComponent
```

#### After Cleanup (✅ Correct)
```javascript
// ✅ Clean, only necessary imports
import React from 'react'
import { useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import Button from '../../common/components/Button'
import { getUserData } from '../../redux/selectors'
import { toast } from '../../common/utils/feedback'
import styles from './MyComponent.module.scss'

const MyComponent = () => {
  const { t } = useTranslation(['User'])
  const userData = useSelector(getUserData)

  const handleSubmit = () => {
    toast('Submitted successfully')
  }

  return (
    <div className={styles.MyComponentContainer}>
      <Button
        onClick={handleSubmit}
        data-test-id="folder.container.my-component.submit-button"
      >
        {t('SubmitButton')}
      </Button>
    </div>
  )
}

export default MyComponent
```

### 🔍 Common Unused Import Patterns

#### React Hooks Cleanup
```javascript
// ❌ Before - Unused hooks imported
import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'

const MyComponent = () => {
  return <div>Simple component</div>
}

// ✅ After - Only necessary imports
import React from 'react'

const MyComponent = () => {
  return <div>Simple component</div>
}
```

#### Lodash Methods Cleanup
```javascript
// ❌ Before - Unused lodash methods
import _get from 'lodash/get'
import _isEmpty from 'lodash/isEmpty'
import _debounce from 'lodash/debounce'
import _cloneDeep from 'lodash/cloneDeep'

const MyComponent = () => {
  const isEmpty = _isEmpty(data)
  return <div>{isEmpty ? 'Empty' : 'Has data'}</div>
}

// ✅ After - Only used methods
import _isEmpty from 'lodash/isEmpty'

const MyComponent = () => {
  const isEmpty = _isEmpty(data)
  return <div>{isEmpty ? 'Empty' : 'Has data'}</div>
}
```

#### Component Imports Cleanup
```javascript
// ❌ Before - Unused components
import Button from '../../common/components/Button'
import Modal from '../../common/components/Modal'
import InputText from '../../common/components/Input/Text'
import Card from '../../common/components/Card'

const MyComponent = () => {
  return (
    <div>
      <Button>Click me</Button>
    </div>
  )
}

// ✅ After - Only used components
import Button from '../../common/components/Button'

const MyComponent = () => {
  return (
    <div>
      <Button>Click me</Button>
    </div>
  )
}
```

#### Utility Functions Cleanup
```javascript
// ❌ Before - Unused utilities
import { formatDate, validateEmail, calculateAge, formatCurrency, parseQuery } from '../../utils/helpers'

const MyComponent = ({ email }) => {
  const isValid = validateEmail(email)
  return <div>{isValid ? 'Valid' : 'Invalid'}</div>
}

// ✅ After - Only used utilities
import { validateEmail } from '../../utils/helpers'

const MyComponent = ({ email }) => {
  const isValid = validateEmail(email)
  return <div>{isValid ? 'Valid' : 'Invalid'}</div>
}
```

#### Redux Selectors Cleanup
```javascript
// ❌ Before - Unused selectors
import { getUserData, getOrderHistory, getRewards, getNotifications, getSettings } from '../../redux/selectors'

const MyComponent = () => {
  const userData = useSelector(getUserData)
  return <div>Welcome {userData?.name}</div>
}

// ✅ After - Only used selectors
import { getUserData } from '../../redux/selectors'

const MyComponent = () => {
  const userData = useSelector(getUserData)
  return <div>Welcome {userData?.name}</div>
}
```

### 🚨 Critical Cleanup Scenarios

#### After Component Refactoring
```javascript
// ❌ Before refactoring - Complex component with many imports
import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import Modal from '../../common/components/Modal'
import Button from '../../common/components/Button'
import InputText from '../../common/components/Input/Text'
import { fetchUserData, updateProfile } from '../../redux/thunks'

// ✅ After refactoring to simple display component
import React from 'react'
import { useSelector } from 'react-redux'
import { getUserData } from '../../redux/selectors'

const UserProfile = () => {
  const userData = useSelector(getUserData)
  return <div>{userData?.name}</div>
}

UserProfile.displayName = 'UserProfile'
```

#### After Removing Features
```javascript
// ❌ Before - Component with search and filter features
import React, { useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import _debounce from 'lodash/debounce'
import Search from '../../common/components/Input/Search'
import Button from '../../common/components/Button'
import { alert } from '../../common/utils/feedback'

// ✅ After - Removed search feature, clean imports
import React from 'react'
import { useTranslation } from 'react-i18next'
import Button from '../../common/components/Button'

const SimpleComponent = () => {
  const { t } = useTranslation(['Common'])
  return <Button>{t('SubmitButton')}</Button>
}

SimpleComponent.displayName = 'SimpleComponent'
```

### 🔧 IDE Integration Benefits

#### ESLint Integration
```javascript
// ESLint can help identify unused imports with rules like:
// "no-unused-vars": "error"
// "@typescript-eslint/no-unused-imports": "error"
```

#### Automatic Cleanup Tools
- **VSCode**: Use "Organize Imports" (Shift+Alt+O)
- **WebStorm**: Use "Optimize Imports" (Ctrl+Alt+O)
- **Prettier**: Configure to remove unused imports

### 📦 Benefits of Import Cleanup

- 🚀 **Smaller bundle size**: Reduces final JavaScript bundle size
- ⚡ **Faster builds**: Less code to process during compilation
- 🔍 **Better readability**: Cleaner, more focused import sections
- 🧹 **Maintainable code**: Easier to understand actual dependencies
- 📈 **Performance**: Faster module resolution and loading
- 🎯 **Clear intent**: Only import what you actually use

### 🎯 Cleanup Implementation Checklist

When modifying any file:
- [ ] **Review all imports**: Check if each import is still used
- [ ] **Remove unused variables**: Delete imports with no references
- [ ] **Clean up after refactoring**: Remove imports from deleted/changed code
- [ ] **Update dependency arrays**: Remove unused dependencies from React hooks
- [ ] **Check default vs named imports**: Ensure proper import syntax
- [ ] **Validate import paths**: Ensure all paths are still correct
- [ ] **Test after cleanup**: Verify no functionality is broken

> **Success:**
> Following these import cleanup rules ensures clean, efficient code with optimal bundle size and better maintainability