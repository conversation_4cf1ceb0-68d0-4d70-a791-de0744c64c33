---
description:
globs:
alwaysApply: true
---
# Translation Management Rules

::alert{type="info"}
This rule defines how to handle translations and i18n content in the project
::

## 🌍 Translation Key Management

::alert{type="warning"}
**Mandatory**: All translation strings using `t()` or `i18next.t()` must be added to the corresponding namespace JSON files
::

### Translation Function Formats

The project uses the following translation function formats:

1. **Simple format**: `t('TranslationKey')`
2. **Namespaced format**: `i18next.t('Namespace:TranslationKey')`

### Namespace Detection Rules

#### 1. Using useTranslation Hook

```javascript
// ✅ Single namespace
const { t } = useTranslation(['Profile']);
// → Add translation keys to Profile.json

// ✅ Multiple namespaces
const { t } = useTranslation(['Profile', 'Common']);
// → Add keys to Profile.json or Common.json based on usage
```

#### 2. Using Namespaced Translation Keys

```javascript
// ✅ Explicit namespace in translation key
t('Profile:UserName'); // → Add to Profile.json
t('Common:ButtonsSave'); // → Add to Common.json
t('Orders:StatusPending'); // → Add to Orders.json
```

### File Organization

Translation files should be organized as follows:

```
src/
├── locales/
│   ├── en/
│   │   ├── Profile.json
│   │   ├── Common.json
│   │   ├── Orders.json
│   │   └── ...
│   ├── ms/
│   │   ├── Profile.json
│   │   ├── Common.json
│   │   └── ...
│   └── ...
```

### Examples

#### ✅ Correct Translation Usage

```javascript
// Component using Profile namespace
import { useTranslation } from 'react-i18next';

const ProfileComponent = () => {
  const { t } = useTranslation(['Profile']);

  return (
    <div>
      <h1>{t('Title')}</h1>
      <p>{t('Description')}</p>
      <button>{t('EditButton')}</button>
    </div>
  );
};

ProfileComponent.displayName = 'ProfileComponent';
```

**Corresponding Profile.json:**

```json
{
  "Title": "User Profile",
  "Description": "Manage your profile information",
  "EditButton": "Edit Profile"
}
```

#### ✅ Correct Namespaced Translation Usage

```javascript
// Component using multiple namespaces
import { useTranslation } from 'react-i18next';

const OrderComponent = () => {
  const { t } = useTranslation(['Orders', 'Common']);

  return (
    <div>
      <h1>{t('Orders:Title')}</h1>
      <p>{t('Orders:StatusPending')}</p>
      <button>{t('Common:ButtonsSave')}</button>
    </div>
  );
};

OrderComponent.displayName = 'OrderComponent';
```

**Corresponding JSON files:**

**Orders.json:**

```json
{
  "Title": "My Orders",
  "StatusPending": "Pending",
  "StatusCompleted": "Completed",
  "StatusCancelled": "Cancelled"
}
```

**Common.json:**

```json
{
  "ButtonsSave": "Save",
  "ButtonsCancel": "Cancel",
  "ButtonsDelete": "Delete"
}
```

### Namespace Resolution Rules

1. **Colon prefix determines namespace**: `t('Namespace:Key')` → Add to `Namespace.json`
2. **useTranslation array determines fallback**: If no colon prefix, use first namespace in array
3. **Multiple namespaces**: Check colon prefix first, then use appropriate namespace

## 📝 Translation Value Capitalization Rules

::alert{type="warning"}
**Mandatory**: All translation values must follow proper capitalization rules - no all uppercase or all lowercase text
::

### Capitalization Requirements

1. **First letter capitalization**: All translation values must start with a capital letter
2. **No all uppercase**: Avoid using all uppercase letters (ALL CAPS)
3. **No all lowercase**: Avoid using all lowercase letters
4. **Proper sentence case**: Use sentence case for descriptions and paragraphs
5. **Title case for buttons/labels**: Use title case for buttons, labels, and UI elements

### ✅ Correct Translation Values

```json
{
  "Title": "User Profile",
  "Description": "Manage your profile information and settings",
  "ButtonsSave": "Save Changes",
  "ButtonsCancel": "Cancel",
  "ButtonsDelete": "Delete Account",
  "StatusPending": "Pending",
  "StatusCompleted": "Completed",
  "StatusCancelled": "Cancelled",
  "MessagesSuccess": "Your profile has been updated successfully",
  "MessagesError": "Failed to update profile information"
}
```

### ❌ Wrong Translation Values

```json
{
  "Title": "USER PROFILE",           // ❌ All uppercase
  "Description": "manage your profile information", // ❌ First letter not capitalized
  "ButtonsSave": "SAVE CHANGES",         // ❌ All uppercase
  "ButtonsCancel": "cancel",             // ❌ First letter not capitalized
  "ButtonsDelete": "DELETE ACCOUNT",      // ❌ All uppercase
  "StatusPending": "pending",           // ❌ First letter not capitalized
  "StatusCompleted": "COMPLETED",       // ❌ All uppercase
  "StatusCancelled": "cancelled",        // ❌ First letter not capitalized
  "MessagesSuccess": "your profile has been updated successfully", // ❌ First letter not capitalized
  "MessagesError": "FAILED TO UPDATE PROFILE INFORMATION"          // ❌ All uppercase
}
```

### Special Cases

#### Acronyms and Brand Names
```json
{
  "SettingsApi": "API Settings",          // ✅ Acronyms can be uppercase
  "SettingsOauth": "OAuth Configuration", // ✅ Proper brand name capitalization
  "SettingsUrl": "URL Configuration"      // ✅ Common acronyms
}
```

#### Technical Terms
```json
{
  "TechnicalUserId": "User ID",            // ✅ ID can remain uppercase
  "TechnicalQrCode": "QR Code Scanner",    // ✅ QR is acceptable
  "TechnicalSmsCode": "SMS Verification"   // ✅ SMS is acceptable
}
```

### Benefits of Proper Capitalization

- 🎨 **Professional appearance**: Consistent capitalization creates a polished user interface
- 📖 **Better readability**: Proper sentence case improves text readability
- 🌍 **Localization consistency**: Standard capitalization rules across all languages
- 🔧 **Maintainability**: Easier to maintain and review translation files
- 👥 **User experience**: Follows UI/UX best practices for text presentation

::alert{type="success"}
Following proper capitalization rules ensures professional, readable, and consistent user interface text across all languages
::

### ❌ Common Mistakes

```javascript
// ❌ Wrong - Translation key not added to JSON file
const { t } = useTranslation(['Profile']);
return <h1>{t('MissingKey')}</h1>; // MissingKey not in Profile.json

// ❌ Wrong - Wrong namespace in colon prefix
const { t } = useTranslation(['Profile']);
return <h1>{t('Orders:Title')}</h1>; // Should use Orders namespace or add Orders to useTranslation

// ❌ Wrong - Hardcoded text instead of translation
return <h1>User Profile</h1>; // Should be {t('Title')}
```

### Benefits

- 🌍 **Internationalization**: Proper i18n support for multiple languages
- 🎯 **Organization**: Clear namespace-based file structure
- 🔍 **Maintainability**: Easy to locate and update translations
- 📦 **Modularity**: Namespace separation keeps translations organized
- 🚀 **Performance**: Lazy loading of translation namespaces

::alert{type="success"}
Following these translation rules ensures consistent internationalization support and maintainable translation management across the application
::

# Translation JSON Files Usage Rules

> **Info:**
> This document defines the usage rules and scope for translation JSON files in the project

## 🔗 File Mapping and Usage Scope

### 📦 Ordering Module Files

> **Warning:**
> All JSON files starting with "Ordering" are exclusively for use within the `src/ordering/` folder

#### Specific File Mappings:

1. **OrderHistory.json**
   - **Scope**: `src/ordering/containers/OrderHistory/` pages only
   - **Usage**: Order history related translations

2. **OrderingCart.json**
   - **Scope**: `src/ordering/containers/Cart/` pages only
   - **Usage**: Shopping cart related translations

3. **OrderingCustomer.json**
   - **Scope**: `src/ordering/containers/Customer/` pages only
   - **Usage**: Customer information related translations

4. **OrderingDelivery.json**
   - **Scope**: Delivery and pickup workflow files (no fixed location)
   - **Usage**: Delivery and pickup process translations
   - **Note**: Can be used across ordering module for delivery/pickup flows

5. **OrderingPayment.json**
   - **Scope**: `src/ordering/containers/payment/` and sub-files only
   - **Usage**: Payment process related translations

6. **OrderingPromotion.json**
   - **Scope**: `src/ordering/containers/rewards/` only
   - **Usage**: Ordering module promotions and rewards translations

7. **OrderingTableSummary.json**
   - **Scope**: `src/ordering/containers/TableSummary/` only
   - **Usage**: Table summary related translations

8. **OrderingThankYou.json**
   - **Scope**: All files under `src/ordering/containers/order-status/`
   - **Usage**: Order completion and thank you page translations

### 👤 Profile Module Files

#### Profile.json
- **Current Usage**: Existing files (legacy)
- **New Usage**: Only for files within `src/common/containers/Profile/`
- **Restriction**: New profile-related files should only use this JSON file within the Profile container

### 🌐 Site Module Files

#### Scanner.json
- **Scope**: `src/site/containers/qrscan/` pages only
- **Usage**: QR code scanner related translations

#### SiteHome.json
- **Scope**: `src/site/containers/home/<USER>
- **Usage**: Site home page related translations

#### Site.json
- **Scope**: Entire `src/site/` folder
- **Usage**: General site-wide translations

### 📊 Report Module Files

#### ReportDriver.json
- **Scope**: `src/ordering/containers/ReportDriver/` pages only
- **Usage**: Driver report related translations

### 🌍 Global Translation Files

> **Important:**
> These files can be used globally but with restrictions for new development

#### Common.json
- **Scope**: Global usage allowed
- **Restriction**: Due to refactoring, avoid adding new translations to this file
- **Recommendation**: Create module-specific translation files for new features

#### ApiError.json
- **Scope**: Global usage allowed
- **Restriction**: Do not add new errors to this file due to refactoring
- **Recommendation**: Handle errors in module-specific translation files

### 📁 Direct Folder Mapping

> **Strict Rule:**
> The following JSON files have direct 1:1 mapping with src folders and cannot be cross-used

| Translation File | Source Folder | Usage Scope |
|------------------|---------------|-------------|
| `Cashback.json` | `src/cashback/` | Cashback module only |
| `EInvoice.json` | `src/e-invoice/` | E-invoice module only |
| `Rewards.json` | `src/rewards/` | Rewards module only |
| `User.json` | `src/user/` | User module only |
| `Voucher.json` | `src/voucher/` | Voucher module only |

## 📝 Translation Key Naming Rules

> **Important:**
> All translation keys must follow strict naming conventions for consistency

### 🔤 Key Naming Convention

#### Basic Rules
1. **PascalCase Format**: All keys must start with uppercase letter and use PascalCase
2. **Functional Naming**: Name keys based on their functionality and purpose
3. **Descriptive Suffixes**: Use clear suffixes to indicate the type of content
4. **No Dots**: Translation keys must not contain dots (.) anywhere in the name
5. **Namespace PascalCase**: All namespace names must also start with uppercase letter and use PascalCase

#### Standard Functional Suffixes

```javascript
// ✅ Correct key naming examples
{
  "MembershipListTitle": "Membership List",           // Page/section titles
  "SearchPlaceholder": "Search members...",           // Input placeholders
  "MembershipListDescription": "View all members",    // Descriptions
  "ViewDetailButton": "View Detail",                  // Button labels
  "LoadingMessage": "Loading members...",             // Status messages
  "EmptyStateTitle": "No Members Found",              // Empty state titles
  "ConfirmationMessage": "Are you sure?",             // Confirmation texts
  "SuccessMessage": "Member added successfully",      // Success messages
  "MembershipListSubtitle": "Manage your members",    // Subtitles
  "TooltipText": "Click to view details",             // Tooltip texts
  "TabLabel": "Active Members",                       // Tab labels
  "MenuOption": "Export List",                        // Menu options
  "FormLabel": "Member Name",                         // Form field labels
  "ValidationMessage": "Name is required"             // Validation messages
}
```

### 🚨 Error Message Naming Rules

> **Critical Rule:**
> Error messages have special naming and placement requirements

#### Error Key Format
- **Pattern**: `Error{FunctionalityName}`
- **Examples**: `ErrorMemberNotFound`, `ErrorInvalidEmail`, `ErrorNetworkTimeout`

#### Error Message Examples

```javascript
// ✅ Correct error key naming
{
  // Regular translations (top section)
  "MembershipListTitle": "Membership List",
  "SearchPlaceholder": "Search members...",
  "ViewDetailButton": "View Detail",

  // Error messages (bottom section)
  "ErrorMemberNotFound": "Member not found",
  "ErrorInvalidEmail": "Please enter a valid email address",
  "ErrorNetworkTimeout": "Network request timed out",
  "ErrorPermissionDenied": "You don't have permission to view this"
}
```

## 📋 JSON File Structure and Organization

### 🔄 Adding New Translations

> **Mandatory Rule:**
> When adding new containers or files, append translations to the corresponding JSON file according to the mapping rules

#### File Addition Process
1. **Identify Target JSON**: Use the file mapping rules to determine the correct JSON file
2. **Add Regular Translations**: Append new translations before any existing error messages
3. **Add Error Messages**: Append error messages at the very bottom of the JSON file

#### JSON Organization Structure

```json
{
  // ===== REGULAR TRANSLATIONS (TOP SECTION) =====
  "ExistingTitle": "Existing Title",
  "ExistingDescription": "Existing description",

  // ===== NEW TRANSLATIONS (APPEND HERE) =====
  "NewFeatureTitle": "New Feature Title",
  "NewFeatureDescription": "New feature description",
  "NewFeatureButton": "Click Here",

  // ===== ERROR MESSAGES (BOTTOM SECTION) =====
  "ErrorExistingError": "Existing error message",
  "ErrorNewFeatureNotFound": "New feature not found",
  "ErrorNewFeatureInvalid": "Invalid new feature data"
}
```

### 📍 Translation Placement Rules

#### Rule 1: Regular Translations
- **Position**: Append **before** any existing error messages
- **Order**: Add new translations in logical grouping order

#### Rule 2: Error Messages
- **Position**: Always at the **bottom** of the JSON file
- **Identifier**: All error keys start with `Error`
- **Order**: New error messages append after existing error messages

#### Rule 3: JSON Without Existing Errors
- **Position**: Append all new translations (including errors) at the **bottom**
- **Organization**: Regular translations first, then error messages

### 📝 Example: Adding to User.json

```json
{
  // Existing translations
  "ProfileTitle": "User Profile",
  "EditProfileButton": "Edit Profile",

  // ✅ New regular translations (append here)
  "MembershipListTitle": "Membership List",
  "SearchMembersPlaceholder": "Search members...",
  "ViewMemberDetailButton": "View Detail",
  "NoMembersFoundTitle": "No Members Found",
  "MembershipListDescription": "Manage your membership list",

  // Existing errors
  "ErrorProfileNotFound": "Profile not found",

  // ✅ New error messages (append here)
  "ErrorMembershipListLoadFailed": "Failed to load membership list",
  "ErrorMemberNotFound": "Member not found",
  "ErrorInvalidMemberData": "Invalid member data provided"
}
```

## 🚫 Cross-Usage Restrictions

### Forbidden Practices

```javascript
// ❌ Wrong - Cross-module usage
// In src/user/containers/Profile.jsx
import { t } from 'react-i18next'
const text = t('RewardsPoints') // Using rewards.json in user module

// In src/rewards/containers/Home.jsx
const text = t('VoucherTitle') // Using voucher.json in rewards module
```

### Correct Usage

```javascript
// ✅ Correct - Module-specific usage
// In src/user/containers/ViewMembershipList/index.jsx
import { t } from 'react-i18next'
const text = t('MembersList') // Using User.json in user module

// In src/ordering/containers/Cart/index.jsx
const text = t('AddToCart') // Using OrderingCart.json in ordering module

// ✅ Correct - Global usage
// In any file
const commonText = t('Save') // Using Common.json globally
const errorText = t('NetworkError') // Using ApiError.json globally
```

## 📋 Translation Key Naming Convention

### Module-Specific Keys
- Use descriptive, hierarchical key names
- Start with the component or page context
- Use PascalCase for consistency
- Avoid dots in key names

```json
{
  "MembershipListTitle": "Membership List",
  "MembershipListSearchPlaceholder": "Search members...",
  "MembershipListNoMembers": "No members found",
  "MembershipCardViewDetail": "View Detail",
  "MembershipCardPoints": "Points",
  "MembershipCardJoined": "Joined"
}
```

### Global Keys (Common.json)
- Use generic, reusable terms
- Avoid module-specific context
- Use PascalCase for consistency

```json
{
  "ActionsSave": "Save",
  "ActionsCancel": "Cancel",
  "ActionsDelete": "Delete",
  "ActionsEdit": "Edit",
  "StatesLoading": "Loading...",
  "StatesError": "Error occurred",
  "StatesSuccess": "Success"
}
```

## ✅ Best Practices Checklist

- [ ] Use correct translation file for the module you're working in
- [ ] Don't cross-reference translation files between modules
- [ ] Use PascalCase for all translation keys
- [ ] Ensure keys do not contain dots (.)
- [ ] Name keys based on functionality with appropriate suffixes
- [ ] Start error message keys with `Error` followed by functionality name
- [ ] Append regular translations before existing error messages
- [ ] Append error messages at the bottom of JSON files
- [ ] Follow the direct folder mapping for the 5 core modules
- [ ] Use descriptive, hierarchical key names
- [ ] Test translations in the correct module context

## 🔧 Development Guidelines

### When Creating New Features
1. **Identify the module**: Determine which `src/` folder your feature belongs to
2. **Choose the correct JSON file**: Use the corresponding translation file
3. **Create new keys**: Add translations using PascalCase and functional naming
4. **Organize translations**: Place regular translations before errors, errors at bottom
5. **Follow naming patterns**: Use appropriate suffixes (Title, Description, Button, etc.)
6. **Handle errors properly**: Use `Error{FunctionalityName}` format for error messages

### When Adding New Containers
1. **Map to correct JSON**: Use the container mapping rules to identify target JSON file
2. **Append translations**: Add new translations at the appropriate position in the JSON
3. **Maintain organization**: Keep regular translations separated from error messages
4. **Use consistent naming**: Follow PascalCase and functional naming conventions

### When Refactoring
1. **Review translation usage**: Ensure files use the correct translation scope
2. **Check key naming**: Verify all keys follow PascalCase convention
3. **Organize JSON structure**: Ensure proper separation of regular and error translations
4. **Move misplaced translations**: Relocate translations to the correct files
5. **Update import statements**: Fix any cross-module translation usage
6. **Test thoroughly**: Verify all translations work in their intended context

### Naming Pattern Examples

```javascript
// ✅ Correct naming patterns
{
  // Page/Section titles
  "DashboardTitle": "Dashboard",
  "UserProfileTitle": "User Profile",

  // Descriptions
  "FeatureDescription": "This feature helps you...",
  "PageDescription": "Welcome to the main page",

  // Buttons and actions
  "SaveChangesButton": "Save Changes",
  "CancelActionButton": "Cancel",
  "SubmitFormButton": "Submit",

  // Form elements
  "EmailFieldLabel": "Email Address",
  "PasswordFieldPlaceholder": "Enter your password",
  "ConfirmPasswordLabel": "Confirm Password",

  // Messages and notifications
  "WelcomeMessage": "Welcome back!",
  "SuccessNotification": "Operation completed successfully",
  "LoadingMessage": "Please wait...",

  // Error messages (bottom section)
  "ErrorInvalidCredentials": "Invalid email or password",
  "ErrorNetworkUnavailable": "Network connection unavailable",
  "ErrorDataLoadFailed": "Failed to load data"
}
```

> **Success:**
> Following these translation rules ensures consistent, maintainable, and properly scoped internationalization across the application

## 🔧 useTranslation Hook Usage Rules

> **Critical Rule:**
> Proper usage of useTranslation hook and t function for namespace management

### 📋 JSX Files Translation Usage

#### useTranslation Hook Definition
- **Format**: `useTranslation(['NamespaceName'])`
- **Namespace**: Must match the JSON file name (without .json extension)
- **Single Namespace**: New pages should avoid using multiple namespaces

#### Translation Key Usage in JSX
- **Format**: `t('KeyName')` - Direct key usage without namespace prefix
- **No Namespace Prefix**: Don't use `{namespace}.{key}` format in JSX files

#### Examples for JSX Files

```javascript
// ✅ Correct - JSX file usage
import { useTranslation } from 'react-i18next'

// Single namespace definition
const { t } = useTranslation(['User'])

// Direct key usage without namespace prefix
const title = t('MembershipListTitle')
const button = t('ViewDetailButton')
const message = t('LoadingMembersMessage')
const error = t('ErrorMemberNotFound')

// ✅ Correct - Different namespaces for different modules
// In src/rewards/ files
const { t } = useTranslation(['Rewards'])
const rewardTitle = t('RewardListTitle')

// In src/ordering/ files
const { t } = useTranslation(['OrderingCart'])
const cartTitle = t('AddToCartButton')

// ❌ Wrong - Multiple namespaces (avoid for new pages)
const { t } = useTranslation(['User', 'Common']) // Not recommended for new pages

// ❌ Wrong - Using namespace prefix in JSX
const title = t('UserMembershipListTitle') // Don't use namespace prefix
const title = t('User:MembershipListTitle') // Don't use colon format in JSX
```

### 📄 Non-JSX Files Translation Usage

#### i18next.t Direct Usage
- **Format**: `i18next.t('Namespace:KeyName')`
- **Usage**: For utility functions, helpers, and non-React files
- **Namespace Format**: Use colon `:` to separate namespace and key

#### Examples for Non-JSX Files

```javascript
// ✅ Correct - Non-JSX file usage (utils, helpers, etc.)
import i18next from 'i18next'

// Utility functions
export const getUserMembershipTitle = () => {
  return i18next.t('User:MembershipListTitle')
}

export const getErrorMessage = (errorType) => {
  return i18next.t(`User:Error${errorType}`)
}

// Helper functions
export const formatMembershipStatus = (status) => {
  return i18next.t(`User:${status}StatusLabel`)
}

// Service layer
export const validateMemberData = (data) => {
  if (!data.email) {
    throw new Error(i18next.t('User:ErrorInvalidEmail'))
  }
}

// ❌ Wrong - Using useTranslation in non-JSX files
import { useTranslation } from 'react-i18next' // Don't use in utils/helpers

// ❌ Wrong - Missing namespace in non-JSX files
const message = i18next.t('MembershipListTitle') // Missing namespace
```

### 🎯 Namespace Selection Guidelines

#### Choose Correct Namespace
1. **Module-based Selection**: Use the namespace that matches your file location
2. **Single Namespace Preference**: Avoid multiple namespaces for new features
3. **Consistent Usage**: Use the same namespace throughout the component/page

#### Namespace Mapping for useTranslation

```javascript
// ✅ Correct namespace selection based on file location

// src/user/ files
const { t } = useTranslation(['User'])

// src/rewards/ files
const { t } = useTranslation(['Rewards'])

// src/ordering/containers/Cart/ files
const { t } = useTranslation(['OrderingCart'])

// src/ordering/containers/payment/ files
const { t } = useTranslation(['OrderingPayment'])

// src/cashback/ files
const { t } = useTranslation(['Cashback'])

// src/voucher/ files
const { t } = useTranslation(['Voucher'])

// src/e-invoice/ files
const { t } = useTranslation(['EInvoice'])

// For global translations (when absolutely necessary)
const { t } = useTranslation(['Common'])
```

### 🚫 Deprecated Patterns

#### Avoid These Patterns

```javascript
// ❌ Wrong - Old patterns with incorrect casing
const title = t('membershipListTitle')
const button = t('commonSave')

// ❌ Wrong - Multiple namespaces for new features
const { t } = useTranslation(['User', 'Common', 'Rewards'])

// ❌ Wrong - Mixing useTranslation and i18next.t in same JSX file
const { t } = useTranslation(['User'])
const title = t('MembershipListTitle')
const error = i18next.t('User:ErrorMessage') // Use t() instead

// ❌ Wrong - Using colon format in JSX files
const title = t('User:MembershipListTitle')
```

### ✅ Migration Examples

#### Before (Old Pattern)
```javascript
// ❌ Old pattern
import { useTranslation } from 'react-i18next'

const { t } = useTranslation()
const title = t('membershipListTitle')
const error = t('apiErrorNetworkError')
```

#### After (New Pattern)
```javascript
// ✅ New pattern
import { useTranslation } from 'react-i18next'

const { t } = useTranslation(['User'])
const title = t('MembershipListTitle')
const error = t('ErrorNetworkTimeout')
```

### 🔧 Implementation Checklist

- [ ] Use `useTranslation(['NamespaceName'])` in JSX files
- [ ] Choose namespace based on file location and mapping rules
- [ ] Use direct key names with `t('KeyName')` in JSX files
- [ ] Use `i18next.t('Namespace:KeyName')` in non-JSX files
- [ ] Prefer single namespace for new features
- [ ] Follow PascalCase naming for all translation keys
- [ ] Ensure keys do not contain dots (.)
- [ ] Place error keys at bottom of JSON files with `Error` prefix

## 🚫 Cross-Usage Restrictions
