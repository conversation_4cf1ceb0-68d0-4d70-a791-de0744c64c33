---
description:
globs:
alwaysApply: true
---
# Common Coding Rules

::alert{type="info"}
This document defines the common coding standards and best practices for the project
::

## 🧪 Testing & Data Attributes

### data-test-id Requirements

::alert{type="warning"}
**Mandatory**: All clickable elements (with `onClick`) must include a `data-test-id` attribute
::

#### Naming Convention

The `data-test-id` follows a hierarchical dot-separated structure:

```
{folder}.{containers}.{component-name}.{element-function}
```

**Layer Rules:**
1. **Layer 1**: Folder name under `src/`
2. **Layer 2 to n-2**: Container folder names under each level
3. **Layer n-1**: Current component name (convert from camelCase to kebab-case, all lowercase)
4. **Layer n**: Element's functional name

#### Examples

```javascript
// ✅ Correct data-test-id examples
<button
  onClick={handleSubmit}
  data-test-id="auth.login.login-form.submit-button"
>
  Submit
</button>

<div
  onClick={handleCardClick}
  data-test-id="rewards.membership.reward-card.view-details"
>
  View Details
</div>

<button
  onClick={handleDelete}
  data-test-id="ordering.cart.cart-item.delete-button"
>
  Delete
</button>
```

#### Component Name Conversion

```javascript
// Component: LoginForm → login-form
// Component: RewardCard → reward-card
// Component: CartItem → cart-item
// Component: UserProfile → user-profile
```

## 🚫 Code Modification Policy

::alert{type="danger"}
**Strict Rule**: Do not modify existing code unless explicitly instructed to optimize or modify
::

- Preserve existing functionality and structure
- Only make changes when specifically requested
- Maintain backward compatibility
- Document any necessary changes

## 🐛 Console Logging Standards

### ESLint Disable Comment

::alert{type="info"}
**Automatic Rule**: Only `console.log` statements must include the ESLint disable comment on the line above. `console.error` and `console.warn` do not require ESLint disable comments.
::

```javascript
// ✅ Correct console.log usage - requires ESLint disable comment
// eslint-disable-next-line no-console
console.log('Debug information:', data)

// ✅ Correct console.error usage - no ESLint disable comment needed
console.error('Error occurred:', error)

// ✅ Correct console.warn usage - no ESLint disable comment needed
console.warn('Warning message:', warning)
```

### Console Usage Guidelines

- Use `console.log` for development debugging only
- Use `console.error` for error logging
- Use `console.warn` for warning messages
- Remove console statements before production deployment (except error logging)

## 📋 Logger Action Name Standards

::alert{type="warning"}
**Mandatory**: All logger method calls (info, error, warn, debug, etc.) must follow the standardized action name convention
::

### Action Name Naming Convention

The first parameter of logger methods must follow a hierarchical underscore-separated structure:

```
{SrcFolder}_{ContainerPath}_{FunctionName}
```

#### Layer Rules

1. **Layer 1**: Source folder name under `src/` (capitalized)
2. **Layer 2 to n-1**: Container folder names path (each level capitalized)
3. **Layer n**: Function/action name based on purpose
   - For `error` logs: Method name + `Failed`
   - For other logs: Descriptive function name

#### Examples

```javascript
// ✅ Correct logger action name examples
logger.error('Common_CompleteProfile_Form_LogoutButtonFailed', { message: error?.message });
logger.info('Ordering_Cart_Item_AddedToCart', { productId, quantity });
logger.warn('Rewards_Membership_Detail_DataIncomplete', { userId, missingFields });
logger.debug('Auth_Login_Form_ValidationStarted', { fieldName });

// More examples with different paths
logger.error('Pages_Home_Banner_LoadImageFailed', { imageUrl });
logger.info('Common_Header_Navigation_MenuOpened', { menuType });
logger.warn('Ordering_Checkout_Payment_InvalidCardNumber', { cardType });
```

#### Path Structure Breakdown

```javascript
// File: src/common/containers/CompleteProfile/components/CompleteProfileForm/index.jsx
// Method: handleClickLogoutButton
logger.error('Common_CompleteProfile_Form_LogoutButtonFailed', { message: error?.message });
//         ↑      ↑               ↑     ↑
//      Layer 1  Layer 2       Layer 3  Layer 4
//    (src folder) (container)  (component) (method+Failed)

// File: src/ordering/containers/Cart/components/CartItem/index.jsx
// Method: handleAddToCart
logger.info('Ordering_Cart_Item_AddedToCart', { productId });
//        ↑        ↑     ↑    ↑
//     Layer 1   Layer 2 Layer 3 Layer 4
//   (src folder) (container) (component) (action)
```

#### Action Name Guidelines

**For Error Logs:**
- Always end with the method name + `Failed`
- Use camelCase for the method name part
- Examples: `LoginFailed`, `SaveFormFailed`, `LoadDataFailed`

**For Info/Debug/Warn Logs:**
- Use descriptive action names based on functionality
- Examples: `DataLoaded`, `FormValidated`, `MenuOpened`, `PaymentProcessed`

#### Wrong Examples

```javascript
// ❌ Wrong - Lowercase, no structure
logger.error('logout failed', { error });

// ❌ Wrong - Inconsistent separator
logger.error('Common-CompleteProfile-Form-LogoutButtonFailed', { error });

// ❌ Wrong - Missing layers
logger.error('LogoutButtonFailed', { error });

// ❌ Wrong - Incorrect capitalization
logger.error('common_completeProfile_form_logoutButtonFailed', { error });
```

### Benefits

- 🎯 **Consistent logging**: Standardized action names across the entire project
- 🔍 **Easy debugging**: Quick identification of log source and context
- 📦 **Better monitoring**: Structured logging for better analysis and alerting
- 🧹 **Maintainable code**: Clear relationship between code location and log entries
- 🚀 **Enhanced productivity**: Developers can quickly locate issues from log messages

::alert{type="success"}
Following these logger action name rules ensures consistent and traceable logging across the application
::

## ➡️ Arrow Function Style Standards

::alert{type="warning"}
**Mandatory**: Follow ESLint arrow-body-style rules to maintain consistent arrow function formatting
::

### Arrow Function Body Rules

::alert{type="info"}
**Important Rule**: When an arrow function only returns a single expression, avoid using block statements with explicit return
::

#### Correct Arrow Function Styles

```javascript
// ✅ Correct - Direct return for single expressions
const add = (a, b) => a + b;

const getUser = (id) => users.find(user => user.id === id);

const isActive = (user) => user.status === 'active';

const formatName = (firstName, lastName) => `${firstName} ${lastName}`;

// ✅ Correct - Object literal return (use parentheses)
const createUser = (name, email) => ({
  id: generateId(),
  name,
  email,
  createdAt: new Date()
});

// ✅ Correct - Multi-line expressions (use parentheses)
const renderButton = (text, onClick) => (
  <button
    onClick={onClick}
    className="primary-button"
  >
    {text}
  </button>
);

// ✅ Correct - Use block statement when you need multiple statements
const processUser = (user) => {
  logger.info('Processing user', { userId: user.id });
  const processed = transformUser(user);
  return processed;
};

// ✅ Correct - Use block statement for early returns or complex logic
const validateUser = (user) => {
  if (!user) {
    return false;
  }

  const isValidEmail = validateEmail(user.email);
  const isValidAge = user.age >= 18;

  return isValidEmail && isValidAge;
};
```

#### Wrong Arrow Function Styles

```javascript
// ❌ Wrong - Unnecessary block statement for simple return
const add = (a, b) => {
  return a + b;
};

// ❌ Wrong - Block statement for single expression
const getUser = (id) => {
  return users.find(user => user.id === id);
};

// ❌ Wrong - Block statement for simple conditional
const isActive = (user) => {
  return user.status === 'active';
};

// ❌ Wrong - Block statement for string template
const formatName = (firstName, lastName) => {
  return `${firstName} ${lastName}`;
};

// ❌ Wrong - Block statement for object literal
const createUser = (name, email) => {
  return {
    id: generateId(),
    name,
    email,
    createdAt: new Date()
  };
};
```

#### When to Use Block Statements

Use block statements `{}` with explicit `return` when:

1. **Multiple Statements**: Function contains more than one statement
```javascript
const processData = (data) => {
  const cleaned = cleanData(data);
  const validated = validateData(cleaned);
  return transformed(validated);
};
```

2. **Early Returns**: Function has conditional early returns
```javascript
const findUser = (id) => {
  if (!id) {
    return null;
  }
  return users.find(user => user.id === id);
};
```

3. **Complex Logic**: Function contains loops, conditions, or multiple variables
```javascript
const calculateTotal = (items) => {
  let total = 0;
  for (const item of items) {
    total += item.price * item.quantity;
  }
  return total;
};
```

4. **Side Effects**: Function performs actions beyond returning a value
```javascript
const saveAndReturn = (data) => {
  localStorage.setItem('data', JSON.stringify(data));
  logger.info('Data saved', { data });
  return data;
};
```

#### Component Handler Functions

```javascript
// ✅ Correct - Simple event handlers
const handleClick = () => setCount(count + 1);

const handleChange = (event) => setValue(event.target.value);

const handleSubmit = (formData) => dispatch(submitForm(formData));

// ✅ Correct - Complex handlers that need block statements
const handleFormSubmit = (event) => {
  event.preventDefault();
  const formData = new FormData(event.target);
  const data = Object.fromEntries(formData);
  dispatch(submitForm(data));
};

// ❌ Wrong - Unnecessary block for simple handlers
const handleClick = () => {
  return setCount(count + 1);
};

const handleChange = (event) => {
  return setValue(event.target.value);
};
```

#### Array Methods and Functional Programming

```javascript
// ✅ Correct - Direct return for array methods
const activeUsers = users.filter(user => user.isActive);

const userNames = users.map(user => user.name);

const totalAge = users.reduce((sum, user) => sum + user.age, 0);

// ✅ Correct - Object transformations
const userObjects = users.map(user => ({
  ...user,
  fullName: `${user.firstName} ${user.lastName}`
}));

// ❌ Wrong - Unnecessary blocks for simple transformations
const activeUsers = users.filter(user => {
  return user.isActive;
});

const userNames = users.map(user => {
  return user.name;
});
```

#### ESLint Configuration

This rule corresponds to the ESLint `arrow-body-style` configuration:

```json
{
  "rules": {
    "arrow-body-style": ["error", "as-needed"]
  }
}
```

#### Benefits of Consistent Arrow Function Style

- 🎯 **Conciseness**: Shorter, more readable code for simple operations
- 🔍 **Clarity**: Clear distinction between simple expressions and complex logic
- 📦 **Consistency**: Uniform code style across the project
- 🚀 **Performance**: Slightly better performance for simple expressions
- 🧹 **Maintainability**: Easier to scan and understand code intent

#### Migration Examples

```javascript
// ❌ Before - Inconsistent styles
const utils = {
  add: (a, b) => { return a + b; },
  subtract: (a, b) => a - b,
  multiply: (a, b) => {
    return a * b;
  },
  divide: (a, b) => a / b
};

// ✅ After - Consistent styles
const utils = {
  add: (a, b) => a + b,
  subtract: (a, b) => a - b,
  multiply: (a, b) => a * b,
  divide: (a, b) => a / b
};

// ❌ Before - Unnecessary blocks in component
const UserCard = ({ user }) => {
  const getName = () => {
    return `${user.firstName} ${user.lastName}`;
  };

  const getStatus = () => {
    return user.isActive ? 'Active' : 'Inactive';
  };

  return <div>{getName()} - {getStatus()}</div>;
};

// ✅ After - Proper arrow function usage
const UserCard = ({ user }) => {
  const getName = () => `${user.firstName} ${user.lastName}`;
  const getStatus = () => user.isActive ? 'Active' : 'Inactive';

  return <div>{getName()} - {getStatus()}</div>;
};
```

::alert{type="success"}
Following these arrow function style rules ensures consistent, readable, and ESLint-compliant code across the project
::

## 📝 Additional Coding Standards

### Import Organization

Follow the established import order:
1. Third-party packages (non-React)
2. React core packages
3. React-related packages
4. Redux packages
5. Static resources
6. Constants
7. Redux files
8. Components
9. Local files
10. Styles

### DisplayName Requirements

::alert{type="warning"}
**Mandatory**: All React components must include a `displayName` property
::

Every React component must have a `displayName` property set to exactly match the component name:

```javascript
// ✅ Correct displayName usage
const MyComponent = () => {
  return <div>Content</div>
}

MyComponent.displayName = 'MyComponent'

export default MyComponent

// ✅ Works with arrow functions
const UserProfile = () => {
  return <div>User Profile</div>
}

UserProfile.displayName = 'UserProfile'

// ✅ Works with function declarations
function ProductCard() {
  return <div>Product Card</div>
}

ProductCard.displayName = 'ProductCard'

// ✅ Works with forwardRef
const CustomInput = forwardRef((props, ref) => {
  return <input ref={ref} {...props} />
})

CustomInput.displayName = 'CustomInput'
```

**Why displayName is important:**
- 🔍 Easier debugging in React DevTools
- 🧪 Better component identification in tests
- 📝 Clearer error messages and stack traces
- 🔧 Enhanced development experience

### PropTypes and DefaultProps Requirements

::alert{type="danger"}
**Mandatory**: All React components that accept props from external sources must define both `propTypes` and `defaultProps`
::

#### When PropTypes and DefaultProps Are Required

Components **MUST** define `propTypes` and `defaultProps` when:
- Receiving props from parent components
- Used as reusable components across the application
- Props affect component behavior or appearance
- Component is exported for external use

#### When PropTypes and DefaultProps Are Optional

Components **MAY SKIP** `propTypes` and `defaultProps` when:
- Component has no props (only internal state and logic)
- Component is a simple container with no configurable behavior
- Internal-only components with fixed usage patterns

#### Examples

```javascript
// ✅ Correct - Component with external props has both propTypes and defaultProps
import React from 'react'
import PropTypes from 'prop-types'
import styles from './Button.module.scss'

const Button = ({ className, disabled, loading, type, size, onClick, children }) => {
  return (
    <button
      className={className}
      disabled={disabled || loading}
      type={type}
      onClick={onClick}
    >
      {loading ? 'Loading...' : children}
    </button>
  )
}

// Required: PropTypes definition
Button.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  onClick: PropTypes.func,
  children: PropTypes.node.isRequired
}

// Required: DefaultProps definition
Button.defaultProps = {
  className: '',
  disabled: false,
  loading: false,
  type: 'button',
  size: 'medium',
  onClick: () => {}
}

Button.displayName = 'Button'

export default Button

// ✅ Correct - Component with no external props (PropTypes/defaultProps optional)
const SimpleContainer = () => {
  const [count, setCount] = useState(0)

  return (
    <div>
      <span>Count: {count}</span>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  )
}

SimpleContainer.displayName = 'SimpleContainer'

export default SimpleContainer
```

```javascript
// ❌ Wrong - Component with external props missing PropTypes and defaultProps
import React from 'react'
import styles from './Card.module.scss'

const Card = ({ title, content, onClose, variant, showBorder }) => {
  return (
    <div className={styles.CardContainer}>
      <h3>{title}</h3>
      <p>{content}</p>
      {onClose && <button onClick={onClose}>Close</button>}
    </div>
  )
}

Card.displayName = 'Card'

export default Card  // ❌ Missing PropTypes and defaultProps

// ❌ Wrong - Only PropTypes defined, missing defaultProps
const IncompleteComponent = ({ name, age, isActive }) => {
  return <div>{name} - {age} - {isActive ? 'Active' : 'Inactive'}</div>
}

IncompleteComponent.propTypes = {
  name: PropTypes.string.isRequired,
  age: PropTypes.number,
  isActive: PropTypes.bool
}
// ❌ Missing defaultProps

IncompleteComponent.displayName = 'IncompleteComponent'

// ❌ Wrong - Only defaultProps defined, missing PropTypes
const AnotherIncompleteComponent = ({ status, count }) => {
  return <div>Status: {status}, Count: {count}</div>
}

AnotherIncompleteComponent.defaultProps = {
  status: 'pending',
  count: 0
}
// ❌ Missing propTypes

AnotherIncompleteComponent.displayName = 'AnotherIncompleteComponent'
```

#### PropTypes Definition Rules

1. **Import PropTypes**: Always import `PropTypes` from 'prop-types'
2. **Define all props**: Include all props received by the component
3. **Use appropriate types**: Choose the most specific PropTypes validator
4. **Mark required props**: Use `.isRequired` for mandatory props
5. **Follow props order**: Match the props organization order (style, boolean, string, number, object, function)

#### DefaultProps Definition Rules

1. **Provide sensible defaults**: Set reasonable default values for optional props
2. **Match PropTypes order**: Use the same order as propTypes definition
3. **Don't default required props**: Skip `.isRequired` props in defaultProps
4. **Use appropriate types**: Ensure default values match expected prop types
5. **Provide functional defaults**: Use empty functions `() => {}` for optional function props

#### Advanced PropTypes Examples

```javascript
// ✅ Advanced PropTypes usage
import PropTypes from 'prop-types'

const AdvancedComponent = ({
  items,
  onSelect,
  theme,
  config,
  renderItem,
  metadata
}) => {
  // Component implementation
}

AdvancedComponent.propTypes = {
  // Array with specific shape
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      name: PropTypes.string.isRequired,
      category: PropTypes.string
    })
  ).isRequired,

  // Function with specific parameters
  onSelect: PropTypes.func,

  // Enum values
  theme: PropTypes.oneOf(['light', 'dark', 'auto']),

  // Object with specific shape
  config: PropTypes.shape({
    maxItems: PropTypes.number,
    allowMultiSelect: PropTypes.bool,
    sortBy: PropTypes.oneOf(['name', 'date', 'category'])
  }),

  // Custom render function
  renderItem: PropTypes.func,

  // Any additional data
  metadata: PropTypes.object
}

AdvancedComponent.defaultProps = {
  onSelect: () => {},
  theme: 'light',
  config: {
    maxItems: 10,
    allowMultiSelect: false,
    sortBy: 'name'
  },
  renderItem: null,
  metadata: {}
}

AdvancedComponent.displayName = 'AdvancedComponent'
```

#### Benefits of PropTypes and DefaultProps

- 🛡️ **Type safety**: Catch prop type errors during development
- 📚 **Self-documenting**: Clear API definition for component consumers
- 🐛 **Early error detection**: Identify issues before runtime
- 🔍 **Better debugging**: Clear error messages for prop mismatches
- 🧪 **Improved testing**: Better understanding of component requirements
- 📖 **Enhanced documentation**: Automatic API documentation for components
- 🚀 **Development confidence**: Reduced runtime errors and unexpected behavior

### Constants and Variables Definition Order

::alert{type="info"}
**Important Rule**: Follow the specific order for defining constants and variables within component functions
::

#### Definition Order Rules

1. **Constants first, then variables** - No empty lines needed between constant and variable definitions
2. **Constants definition order**:
   1. Third-party constants first (e.g., `const { t } = useTranslation()`, `const dispatch = useDispatch()`)
   2. Constants from selectors - defined in order of element usage
   3. Constants derived from selector constants
3. **Variables definition**
4. **Method constants definition last**

#### No Empty Lines Between Definition Groups

::alert{type="warning"}
**Important Rule**: Do not add empty lines between constant and variable definition groups within component functions
::

All constant and variable definitions should be written consecutively without empty lines between different groups.

```javascript
// ✅ Correct - No empty lines and no comment grouping for definitions
const ComponentName = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const user = useSelector(getUserData)
  const isLoading = useSelector(getLoadingState)
  const cartItems = useSelector(getCartItems)
  const userName = user?.name || 'Guest'
  const totalItems = cartItems?.length || 0
  const isCartEmpty = totalItems === 0
  const [localState, setLocalState] = useState({})
  const [isModalOpen, setIsModalOpen] = useState(false)
  const handleSubmit = useCallback(() => {
    // Handler logic
  }, [])

  useEffect(() => {
    // Effect logic
  }, [])

  return <div>Component content</div>
}

// ❌ Wrong - Empty lines between definitions and comment grouping
const ComponentName = () => {
  // ❌ Comment grouping not needed
  const { t } = useTranslation()
  const dispatch = useDispatch()

  // ❌ Empty line not allowed between definitions

  // ❌ Comment grouping not needed
  const user = useSelector(getUserData)
  const isLoading = useSelector(getLoadingState)

  // ❌ Empty line not allowed between definitions

  // ❌ Comment grouping not needed
  const [localState, setLocalState] = useState({})

  return <div>Component content</div>
}
```

#### Benefits of No Empty Lines in Definitions

- 🧹 **Compact code**: More concise component structure
- 🔍 **Clear grouping**: Comments provide sufficient separation
- 📦 **Consistent formatting**: Standardized across all components
- 🚀 **Better focus**: Related definitions are visually grouped together

#### Hook and Method Execution Order

::alert{type="danger"}
**Critical Rule**: All hooks and method executions must be placed AFTER all constant and variable definitions are completed
::

All constant and variable definitions must be completed first, then followed by hooks execution and method calls.

```javascript
// ✅ Correct - All definitions first, then hooks execution
const ComponentName = () => {
  // All constant and variable definitions first
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const user = useSelector(getUserData)
  const userName = user?.name || 'Guest'
  const [localState, setLocalState] = useState({})
  const [isModalOpen, setIsModalOpen] = useState(false)
  const handleSubmit = useCallback(() => {
    // Handler logic
  }, [])

  // Hook executions and method calls AFTER all definitions
  useEffect(() => {
    // Effect logic
  }, [])

  useEffect(() => {
    dispatch(fetchUserData())
  }, [dispatch])

  // Other method executions
  const processData = useMemo(() => {
    return calculateProcessedData(user)
  }, [user])

  return <div>Component content</div>
}

// ❌ Wrong - Mixing definitions with hook executions
const ComponentName = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()

  // ❌ Hook execution mixed with definitions
  useEffect(() => {
    // Effect logic
  }, [])

  const user = useSelector(getUserData)  // ❌ Definition after hook execution
  const userName = user?.name || 'Guest'

  // ❌ Another hook execution mixed with definitions
  useEffect(() => {
    dispatch(fetchUserData())
  }, [dispatch])

  const [localState, setLocalState] = useState({})  // ❌ Definition after hook execution

  return <div>Component content</div>
}
```

#### No Comment Grouping for Definitions

::alert{type="info"}
**Important Rule**: Constant and variable definitions do not require comment grouping - write them consecutively without group comments
::

Write all constant and variable definitions consecutively without adding comment separators between different types of definitions.

```javascript
// ✅ Correct - No comment grouping for definitions
const ComponentName = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const user = useSelector(getUserData)
  const isLoading = useSelector(getLoadingState)
  const cartItems = useSelector(getCartItems)
  const userName = user?.name || 'Guest'
  const totalItems = cartItems?.length || 0
  const isCartEmpty = totalItems === 0
  const [localState, setLocalState] = useState({})
  const [isModalOpen, setIsModalOpen] = useState(false)
  const handleSubmit = useCallback(() => {
    // Handler logic
  }, [])
  const handleModalToggle = useCallback(() => {
    setIsModalOpen(!isModalOpen)
  }, [isModalOpen])

  useEffect(() => {
    // Effect logic
  }, [])

  return <div>Component content</div>
}

// ❌ Wrong - Using comment grouping for definitions
const ComponentName = () => {
  // ❌ Third-party constants
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  // ❌ Constants from selectors
  const user = useSelector(getUserData)
  const isLoading = useSelector(getLoadingState)
  const cartItems = useSelector(getCartItems)
  // ❌ Constants derived from selectors
  const userName = user?.name || 'Guest'
  const totalItems = cartItems?.length || 0
  const isCartEmpty = totalItems === 0
  // ❌ Variables
  const [localState, setLocalState] = useState({})
  const [isModalOpen, setIsModalOpen] = useState(false)
  // ❌ Method constants
  const handleSubmit = useCallback(() => {
    // Handler logic
  }, [])

  return <div>Component content</div>
}
```

#### Benefits of These Rules

- 🎯 **Clear execution order**: Definitions before executions prevents temporal dead zone issues
- 🔍 **Cleaner code**: No unnecessary comment noise in definition sections
- 📦 **Consistent structure**: Standardized approach across all components
- 🧹 **Better readability**: Focus on the actual code rather than comments
- 🚀 **Maintainability**: Easier to scan and modify definitions

#### Examples

```javascript
// ✅ Correct constants and variables order (no comment grouping, no empty lines)
const ComponentName = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const user = useSelector(getUserData)
  const isLoading = useSelector(getLoadingState)
  const cartItems = useSelector(getCartItems)
  const userName = user?.name || 'Guest'
  const totalItems = cartItems?.length || 0
  const isCartEmpty = totalItems === 0
  const [localState, setLocalState] = useState({})
  const [isModalOpen, setIsModalOpen] = useState(false)
  const handleSubmit = useCallback(() => {
    // Handler logic
  }, [])
  const handleModalToggle = useCallback(() => {
    setIsModalOpen(!isModalOpen)
  }, [isModalOpen])

  useEffect(() => {
    // Effect logic
  }, [])

  return <div>Component content</div>
}
```

```javascript
// ❌ Wrong order - mixed constants and variables
const ComponentName = () => {
  const [localState, setLocalState] = useState({})  // Variable before constants
  const { t } = useTranslation()                    // Third-party constant after variable
  const user = useSelector(getUserData)             // Selector constant after variable
  const userName = user?.name || 'Guest'            // Derived constant
  const [isModalOpen, setIsModalOpen] = useState(false)  // Variable mixed with constants
  const dispatch = useDispatch()                    // Third-party constant at wrong position

  // ... rest of component
}
```

#### Benefits

- 🎯 **Clear dependency flow**: Easy to understand data dependencies
- 🔍 **Better readability**: Logical grouping of related definitions
- 📦 **Consistent structure**: Standardized component organization
- 🧹 **Maintainable code**: Easier to locate and modify definitions

### Component Structure

```javascript
// ✅ Recommended component structure (no comment grouping for definitions)
import React, { useState, useEffect, useCallback } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import Button from '../../common/components/Button'
import { getUserData } from '../../redux/selectors'
import styles from './ComponentName.module.scss'

const ComponentName = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const userData = useSelector(getUserData)
  const userName = userData?.name || 'Guest'
  const [data, setData] = useState({})
  const handleClick = useCallback(() => {
    // eslint-disable-next-line no-console
    console.log('Button clicked')
    // Handler logic
  }, [])

  useEffect(() => {
    // Effect logic
  }, [])

  return (
    <div className={styles.ComponentNameContainer}>
      <Button
        onClick={handleClick}
        data-test-id="folder.container.component-name.action-button"
      >
        Click Me
      </Button>
    </div>
  )
}

ComponentName.displayName = 'ComponentName'

export default ComponentName
```

### Props Organization Order

::alert{type="info"}
**Important Rule**: Follow the specific order for organizing props in JSX elements and component parameter destructuring for consistent and readable code
::

#### Props Order Rules

Props should be organized in the following order to maintain consistency and readability:

1. **Style-related props** (className, style, etc.) - Always at the beginning
2. **Boolean props** - Props with true/false values
3. **String props** - Props with string values
4. **Number props** - Props with numeric values
5. **Object props** - Props with object values
6. **Function props** - Event handlers and callbacks - Always at the end

**This order applies to all three contexts:**
- JSX element props when using components
- Component parameter destructuring when defining components
- Component defaultProps and propTypes definitions

#### Component Parameter Destructuring Order

::alert{type="warning"}
**Important Rule**: Component parameter destructuring must follow the exact same props order as JSX usage and PropTypes definitions
::

When defining components, the parameters in the destructuring assignment must be ordered according to the props organization rules:

```javascript
// ✅ Correct parameter destructuring order
const MemberListItem = ({
  className,           // Style-related props (first)
  isCurrentUser,       // Boolean props
  type,                // String props
  size,                // Number props
  member,              // Object props
  onViewDetail         // Function props (last)
}) => {
  // Component implementation
}

// ❌ Wrong parameter destructuring order
const MemberListItem = ({
  member,              // Object prop at wrong position
  onViewDetail,        // Function prop not at end
  isCurrentUser        // Boolean prop after function
}) => {
  // Component implementation
}
```

#### Real-World Example

```javascript
// ❌ Before - Wrong parameter order
const MemberListItem = ({ member, onViewDetail, isCurrentUser }) => {
  // Component logic
}

// ✅ After - Correct parameter order
const MemberListItem = ({ isCurrentUser, member, onViewDetail }) => {
  // Component logic
}
```

#### Benefits of Consistent Parameter Order

- 🎯 **Consistent API**: Same order across JSX usage, parameter destructuring, and PropTypes
- 🔍 **Better readability**: Developers can quickly scan and understand component interfaces
- 📦 **Maintainability**: Easy to add, remove, or modify props following established patterns
- 🧹 **Code review efficiency**: Reviewers can quickly identify props order violations

#### JSX Usage Examples

```javascript
// ✅ Correct props order in JSX
<Button
  className={styles.submitButton}
  style={{ marginTop: '10px' }}
  disabled={isLoading}
  visible={showButton}
  type="primary"
  placeholder="Enter text"
  id="submit-btn"
  size={24}
  maxWidth={300}
  tabIndex={0}
  config={buttonConfig}
  options={menuOptions}
  data={{ userId: 123 }}
  onClick={handleSubmit}
  onHover={handleMouseEnter}
  onFocus={handleFocus}
>
  Submit
</Button>

// ✅ Another correct example
<InputText
  className={styles.emailInput}
  required
  autoFocus
  type="email"
  placeholder="Enter your email"
  name="email"
  maxLength={100}
  minLength={5}
  validation={emailValidation}
  metadata={inputMetadata}
  onChange={handleEmailChange}
  onBlur={handleBlur}
  onKeyPress={handleKeyPress}
/>
```

#### Component Definition Examples

```javascript
// ✅ Correct props order in component parameter destructuring
const Button = ({
  className,           // Style-related props
  style,
  disabled,            // Boolean props
  visible,
  loading,
  type,                // String props
  variant,
  placeholder,
  id,
  size,                // Number props
  maxWidth,
  tabIndex,
  config,              // Object props
  options,
  data,
  onClick,             // Function props
  onHover,
  onFocus
}) => {
  return (
    <button
      className={className}
      style={style}
      disabled={disabled}
      onClick={onClick}
    >
      Button Content
    </button>
  )
}

// ✅ Another component definition example
const InputField = ({
  className,           // Style-related props
  required,            // Boolean props
  autoFocus,
  readOnly,
  type,                // String props
  placeholder,
  name,
  value,
  maxLength,           // Number props
  minLength,
  tabIndex,
  validation,          // Object props
  metadata,
  onChange,            // Function props
  onBlur,
  onFocus
}) => {
  // Component logic
}

// ❌ Wrong props order in component definition
const WrongComponent = ({
  onClick,             // Function prop at wrong position
  disabled,            // Boolean after function
  className,           // Style prop not at beginning
  type,                // String after boolean
  size,                // Number after string
  config               // Object after number
}) => {
  // Component logic
}

// ❌ Real-world wrong example
const MemberListItem = ({
  member,              // Object prop should come after boolean props
  onViewDetail,        // Function prop should be last
  isCurrentUser        // Boolean prop should come before object props
}) => {
  // Component logic
}

// ✅ Real-world correct example
const MemberListItem = ({
  isCurrentUser,       // Boolean props first (after style props if any)
  member,              // Object props after primitive types
  onViewDetail         // Function props always last
}) => {
  // Component logic
}
```

#### PropTypes and DefaultProps Examples

```javascript
import PropTypes from 'prop-types'

// ✅ Correct propTypes order
Button.propTypes = {
  // Style-related props
  className: PropTypes.string,
  style: PropTypes.object,

  // Boolean props
  disabled: PropTypes.bool,
  visible: PropTypes.bool,
  loading: PropTypes.bool,

  // String props
  type: PropTypes.oneOf(['primary', 'secondary', 'text']),
  variant: PropTypes.string,
  placeholder: PropTypes.string,
  id: PropTypes.string,

  // Number props
  size: PropTypes.number,
  maxWidth: PropTypes.number,
  tabIndex: PropTypes.number,

  // Object props
  config: PropTypes.object,
  options: PropTypes.object,
  data: PropTypes.object,

  // Function props
  onClick: PropTypes.func,
  onHover: PropTypes.func,
  onFocus: PropTypes.func
}

// ✅ Correct defaultProps order
Button.defaultProps = {
  // Style-related props
  className: '',
  style: {},

  // Boolean props
  disabled: false,
  visible: true,
  loading: false,

  // String props
  type: 'primary',
  variant: 'default',
  placeholder: '',
  id: '',

  // Number props
  size: 16,
  maxWidth: 300,
  tabIndex: 0,

  // Object props
  config: {},
  options: {},
  data: {},

  // Function props
  onClick: () => {},
  onHover: () => {},
  onFocus: () => {}
}

// ❌ Wrong propTypes/defaultProps order
Button.propTypes = {
  onClick: PropTypes.func,        // Function prop at wrong position
  disabled: PropTypes.bool,       // Boolean after function
  className: PropTypes.string,    // Style prop not at beginning
  type: PropTypes.string,         // String after boolean
  size: PropTypes.number,         // Number after string
  config: PropTypes.object        // Object after number
}
```

#### Wrong Examples

```javascript
// ❌ Wrong props order - Mixed types in JSX
<Button
  onClick={handleSubmit}           // Function prop at wrong position
  disabled={isLoading}            // Boolean after function
  className={styles.submitButton} // Style prop not at beginning
  type="primary"                  // String after boolean
  size={24}                       // Number after string
  config={buttonConfig}           // Object after number
>
  Submit
</Button>

// ❌ Wrong props order - Functions not at end in JSX
<InputText
  onChange={handleChange}         // Function not at end
  className={styles.input}        // Style prop not at beginning
  required                        // Boolean after function
  placeholder="Enter text"        // String after boolean
  maxLength={100}                 // Number after string
/>
```

#### Props Order Categories Explanation

##### 1. Style-related Props (First)
- `className` - CSS class names
- `style` - Inline styles
- Any styling-related props

##### 2. Boolean Props
- `disabled`, `enabled`
- `visible`, `hidden`
- `required`, `optional`
- `autoFocus`, `readOnly`
- `loading`, `error`

#### Boolean Props Simplification Rule

::alert{type="info"}
**Important Rule**: Boolean props with `true` values should omit the `={true}` assignment for cleaner code
::

```javascript
// ✅ Correct - Omit ={true} for boolean props
<Button
  disabled
  visible
  required
  autoFocus
  loading
  onClick={handleClick}
>
  Submit
</Button>

<InputText
  required
  autoFocus
  readOnly
  placeholder="Enter text"
  onChange={handleChange}
/>

// ❌ Wrong - Unnecessary ={true} assignments
<Button
  disabled={true}
  visible={true}
  required={true}
  autoFocus={true}
  loading={true}
  onClick={handleClick}
>
  Submit
</Button>

<InputText
  required={true}
  autoFocus={true}
  readOnly={true}
  placeholder="Enter text"
  onChange={handleChange}
/>
```

**When to use explicit values:**
- Only use explicit `={false}` when the boolean prop defaults to `true` and you need to override it
- Use explicit `={variable}` when the value comes from a variable or expression

```javascript
// ✅ Correct - Explicit false values when needed
<Button
  disabled={false}  // Only when default is true and needs override
  visible={isVisible}  // Variable value
  loading={isSubmitting && !hasError}  // Expression value
>
  Submit
</Button>
```

**Benefits:**
- 🧹 **Cleaner code**: Reduces visual clutter in JSX
- 🎯 **Industry standard**: Follows React best practices
- 🔍 **Better readability**: Easier to scan boolean props
- 🚀 **Faster development**: Less typing required

##### 3. String Props
- `type`, `variant`, `theme`
- `placeholder`, `title`, `label`
- `id`, `name`, `value`
- `src`, `alt`, `href`

##### 4. Number Props
- `size`, `width`, `height`
- `maxLength`, `minLength`
- `tabIndex`, `step`
- `min`, `max`, `value` (when numeric)

##### 5. Object Props
- `data`, `config`, `options`
- `settings`, `metadata`
- `user`, `product`, `item`
- Complex data structures

##### 6. Function Props (Last)
- `onClick`, `onSubmit`, `onCancel`
- `onChange`, `onInput`, `onBlur`
- `onFocus`, `onHover`, `onMouseEnter`
- `onKeyPress`, `onKeyDown`, `onKeyUp`
- All event handlers and callbacks

#### Benefits

- 🎯 **Consistent structure**: Easy to scan and find specific prop types
- 🔍 **Better readability**: Logical grouping makes code more understandable
- 📦 **Improved maintainability**: Easier to add, modify, or remove props
- 🧹 **Clean code**: Standardized organization across the codebase
- 🚀 **Enhanced productivity**: Developers know exactly where to look for specific props

#### Special Cases

```javascript
// ✅ data-test-id can be placed with style props or at the very end for testing
<Button
  className={styles.button}
  data-test-id="auth.login.submit-button"
  disabled={isSubmitting}
  type="submit"
  size={32}
  onClick={handleSubmit}
>
  Login
</Button>

// ✅ Or at the very end after functions (also acceptable)
<Button
  className={styles.button}
  disabled={isSubmitting}
  type="submit"
  size={32}
  onClick={handleSubmit}
  data-test-id="auth.login.submit-button"
>
  Login
</Button>
```

### Error Handling

```javascript
// ✅ Proper error handling
try {
  const result = await apiCall()
  setData(result)
} catch (error) {
  // eslint-disable-next-line no-console
  console.error('API call failed:', error)
  // Handle error appropriately
}
```

## 🌐 API Request Standards

::alert{type="warning"}
**Mandatory**: All direct service API requests must be written in `api-request.js` files
::

### API Request File Organization

All API service calls should be centralized in `api-request.js` files to maintain consistency and improve maintainability.

### Method Naming Convention

::alert{type="info"}
**Important Rule**: API request method names must start with the HTTP method type they use
::

#### Naming Rules

1. **GET requests**: Method names start with `get`
2. **POST requests**: Method names start with `post`
3. **PUT requests**: Method names start with `put`
4. **PATCH requests**: Method names start with `patch`
5. **DELETE requests**: Method names start with `delete` (not `del`)

#### Examples

```javascript
// ✅ Correct API method naming
// api-request.js
import { get, post, put, patch, del } from '../../common/utils/api-fetch'

// GET requests
export const getUserProfile = (userId) => get(`/api/users/${userId}`)

export const getOrderHistory = (params) => get('/api/orders', { searchParams: params })

export const getRewardsList = () => get('/api/rewards')

// POST requests
export const postUserLogin = (credentials) => post('/api/auth/login', { json: credentials })

export const postOrderCreate = (orderData) => post('/api/orders', { json: orderData })

export const postRewardRedeem = (rewardId) => post(`/api/rewards/${rewardId}/redeem`)

// PUT requests
export const putUserProfile = (userId, userData) => put(`/api/users/${userId}`, { json: userData })

export const putOrderUpdate = (orderId, orderData) => put(`/api/orders/${orderId}`, { json: orderData })

// PATCH requests
export const patchUserStatus = (userId, status) => patch(`/api/users/${userId}/status`, { json: { status } })

export const patchOrderStatus = (orderId, status) => patch(`/api/orders/${orderId}`, { json: { status } })

// DELETE requests - method names start with "delete" but call "del"
export const deleteUserAccount = (userId) => del(`/api/users/${userId}`)

export const deleteOrder = (orderId) => del(`/api/orders/${orderId}`)

export const deleteReward = (rewardId) => del(`/api/rewards/${rewardId}`)
```

#### Wrong Examples

```javascript
// ❌ Wrong - Method names don't start with HTTP method
export const userProfile = (userId) => get(`/api/users/${userId}`)  // Should be getUserProfile

export const createOrder = (orderData) => post('/api/orders', { json: orderData })  // Should be postOrderCreate

export const updateUser = (userId, userData) => put(`/api/users/${userId}`, { json: userData })  // Should be putUserProfile

export const delOrder = (orderId) => del(`/api/orders/${orderId}`)  // Should be deleteOrder (method name starts with delete)

// ❌ Wrong - Using wrong HTTP method prefix
export const postUserProfile = (userId) => get(`/api/users/${userId}`)  // Should be getUserProfile (it's a GET request)

export const getUserCreate = (userData) => post('/api/users', { json: userData })  // Should be postUserCreate (it's a POST request)

// ❌ Wrong - Using return await instead of direct arrow function
export const getUserProfile = async (userId) => {
  return await get(`/api/users/${userId}`)  // Should be: (userId) => get(`/api/users/${userId}`)
}
```

### File Structure Examples

#### General api-request.js
```javascript
// src/common/api-request.js
import { get, post, put, del } from '../../common/utils/api-fetch'

// User-related APIs
export const getUserProfile = (userId) => get(`/api/users/${userId}`)

export const postUserRegister = (userData) => post('/api/users/register', { json: userData })

export const putUserProfile = (userId, userData) => put(`/api/users/${userId}`, { json: userData })

export const deleteUserAccount = (userId) => del(`/api/users/${userId}`)

// Auth-related APIs
export const postUserLogin = (credentials) => post('/api/auth/login', { json: credentials })

export const postUserLogout = () => post('/api/auth/logout')
```

#### Module-specific api-request.js
```javascript
// src/ordering/api-request.js
import { get, post, put, patch, del } from '../../common/utils/api-fetch'

// Order-related APIs
export const getOrderHistory = (params) => get('/api/orders', { searchParams: params })

export const getOrderDetail = (orderId) => get(`/api/orders/${orderId}`)

export const postOrderCreate = (orderData) => post('/api/orders', { json: orderData })

export const putOrderUpdate = (orderId, orderData) => put(`/api/orders/${orderId}`, { json: orderData })

export const patchOrderStatus = (orderId, status) => patch(`/api/orders/${orderId}/status`, { json: { status } })

export const deleteOrder = (orderId) => del(`/api/orders/${orderId}`)
```

### Usage with Thunks

::alert{type="info"}
**Important**: API methods are typically used within thunks in the same file for Redux integration
::

```javascript
// api-request.js
import { get, post, put, del } from '../../common/utils/api-fetch'
import { createAsyncThunk } from '@reduxjs/toolkit'

// API methods
export const getUserProfile = (userId) => get(`/api/users/${userId}`)
export const putUserProfile = (userId, userData) => put(`/api/users/${userId}`, { json: userData })
export const deleteUserAccount = (userId) => del(`/api/users/${userId}`)

// Thunks in the same file
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (userId, { rejectWithValue }) => {
    try {
      return await getUserProfile(userId)
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async ({ userId, userData }, { rejectWithValue }) => {
    try {
      return await putUserProfile(userId, userData)
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)

export const removeUserAccount = createAsyncThunk(
  'user/removeAccount',
  async (userId, { rejectWithValue }) => {
    try {
      return await deleteUserAccount(userId)
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message)
    }
  }
)
```

### Usage in Components

```javascript
// ✅ Correct usage in components - using thunks
import { useDispatch } from 'react-redux'
import { fetchUserProfile, updateUserProfile, removeUserAccount } from '../../common/api-request'

const UserProfile = () => {
  const dispatch = useDispatch()
  const [userData, setUserData] = useState({})

  useEffect(() => {
    dispatch(fetchUserProfile(userId))
  }, [dispatch, userId])

  const handleUpdateProfile = (newData) => {
    dispatch(updateUserProfile({ userId, userData: newData }))
  }

  const handleDeleteAccount = () => {
    dispatch(removeUserAccount(userId))
  }

  // ... rest of component
}
```

### Benefits

- 🎯 **Centralized API management**: All API calls are organized in dedicated files
- 🔍 **Clear method identification**: HTTP method is immediately visible in function name
- 📦 **Better maintainability**: Easy to locate and update API endpoints
- 🧹 **Consistent naming**: Standardized naming convention across the project
- 🚀 **Improved debugging**: Easier to trace API calls and troubleshoot issues
- 📝 **Better documentation**: Self-documenting code with clear HTTP method usage

### Special Cases

#### Multiple HTTP Methods for Same Resource
```javascript
// ✅ Correct - Clear distinction between different operations
export const getUserList = (params) => get('/api/users', { searchParams: params })

export const postUserCreate = (userData) => post('/api/users', { json: userData })

export const getUserDetail = (userId) => get(`/api/users/${userId}`)

export const putUserUpdate = (userId, userData) => put(`/api/users/${userId}`, { json: userData })
```

#### Complex Operations
```javascript
// ✅ Correct - Descriptive names with HTTP method prefix
export const postUserPasswordReset = (email) => post('/api/auth/password-reset', { json: { email } })

export const putUserPasswordChange = (userId, passwords) => put(`/api/users/${userId}/password`, { json: passwords })

export const postOrderPaymentProcess = (orderId, paymentData) => post(`/api/orders/${orderId}/payment`, { json: paymentData })
```

::alert{type="success"}
Following these API request standards ensures consistent, maintainable, and easily identifiable API layer across the application
::

## ✅ Code Quality Checklist

Before submitting code, ensure:

- [ ] All clickable elements have `data-test-id`
- [ ] `data-test-id` follows the naming convention
- [ ] Console statements have ESLint disable comments
- [ ] No unnecessary modifications to existing code
- [ ] Imports are properly organized
- [ ] Constants and variables follow the definition order
- [ ] Component structure follows standards
- [ ] Props are organized in the correct order
- [ ] Component parameter destructuring follows the props organization order
- [ ] Error handling is implemented
- [ ] Code is properly formatted and linted
- [ ] All React components have `displayName` property set
- [ ] `displayName` matches the exact component name
- [ ] Components with external props have both `propTypes` and `defaultProps` defined
- [ ] `propTypes` includes all received props with appropriate types
- [ ] `defaultProps` provides sensible defaults for optional props
- [ ] PropTypes and defaultProps follow the correct props organization order
- [ ] All API requests are written in `api-request.js` files
- [ ] API method names start with the correct HTTP method prefix
- [ ] DELETE method names use `delete` prefix but call `del` from api-fetch

::alert{type="success"}
Following these rules ensures consistent, maintainable, and testable code across the project
::
::