---
description:
globs:
alwaysApply: true
---
# Styles Rules

## CSS Module Class Naming Convention

For all components that reference module.scss style files, follow these className rules:

1. **Component-based naming**: The name after `styles.` must always start with the component's name

### Examples

```javascript
// ✅ Correct - className starts with component name
// In MyButton.jsx
import styles from './MyButton.module.scss'

<div className={styles.MyButtonContainer}>
  <button className={styles.MyButtonPrimary}>
    Click me
  </button>
  <span className={styles.MyButtonLabel}>
    Button text
  </span>
</div>

// ✅ Correct - In UserProfile.jsx
import styles from './UserProfile.module.scss'

<div className={styles.UserProfileWrapper}>
  <img className={styles.UserProfileAvatar} />
  <h2 className={styles.UserProfileName}><PERSON></h2>
</div>
```

```javascript
// ❌ Wrong - className doesn't start with component name
// In MyButton.jsx
import styles from './MyButton.module.scss'

<div className={styles.container}>        // Should be MyButtonContainer
  <button className={styles.primary}>     // Should be MyButtonPrimary
    Click me
  </button>
  <span className={styles.label}>         // Should be MyButtonLabel
    Button text
  </span>
</div>
```

### Benefits

- 🎯 **Clear component association**: Immediately know which component a style belongs to
- 🔍 **Better debugging**: Easier to trace styles in DevTools
- 📦 **Avoid naming conflicts**: Prevents style collisions between components
- 🧹 **Maintainable code**: Easier to refactor and maintain styles

### Implementation

When creating a new component with styles:

1. Create component file: `ComponentName.jsx`
2. Create style file: `ComponentName.module.scss`
3. All CSS classes in the SCSS file should start with `ComponentName`
4. Import and use with `styles.ComponentNameClassName`

```scss
/* ComponentName.module.scss */
.ComponentNameContainer {
  display: flex;
  flex-direction: column;
}

.ComponentNameTitle {
  font-size: 1.5rem;
  font-weight: bold;
}

.ComponentNameContent {
  padding: 1rem;
}
```

```javascript
// ComponentName.jsx
import styles from './ComponentName.module.scss'

const ComponentName = () => {
  return (
    <div className={styles.ComponentNameContainer}>
      <h1 className={styles.ComponentNameTitle}>Title</h1>
      <div className={styles.ComponentNameContent}>Content</div>
    </div>
  )
}

ComponentName.displayName = 'ComponentName'
```

## CSS !important Usage Rules

::alert{type="danger"}
**Important Rule**: Do not use `!important` when automatically generating CSS code
::

### Guidelines

1. **Avoid !important**: Never use `!important` in automatically generated CSS
2. **Use proper specificity**: Rely on CSS specificity and proper selector structure instead
3. **Component isolation**: Use CSS modules to avoid specificity conflicts

### Examples

```scss
/* ✅ Correct - No !important usage */
.ComponentNameContainer {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: #ffffff;
}

.ComponentNameTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333333;
}

.ComponentNameButton {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}
```

```scss
/* ❌ Wrong - Using !important */
.ComponentNameContainer {
  display: flex !important;
  flex-direction: column !important;
  padding: 1rem !important;
  background-color: #ffffff !important;
}

.ComponentNameTitle {
  font-size: 1.5rem !important;
  font-weight: bold !important;
  color: #333333 !important;
}
```

### Why Avoid !important

- 🎯 **Better maintainability**: Easier to override styles when needed
- 🔍 **Cleaner debugging**: Avoid specificity wars in DevTools
- 📦 **CSS modules benefit**: CSS modules already provide isolation without !important
- 🧹 **Best practices**: Follows standard CSS best practices
- 🚀 **Performance**: Avoids unnecessary style recalculation

### Alternative Solutions

Instead of using `!important`, consider these approaches:

1. **Increase specificity naturally**:
```scss
.ComponentNameContainer .ComponentNameButton {
  background-color: #007bff; /* More specific than just .ComponentNameButton */
}
```

2. **Use CSS custom properties for dynamic styles**:
```scss
.ComponentNameButton {
  background-color: var(--button-bg-color, #007bff);
}
```

3. **Leverage CSS modules scoping**:
```scss
/* CSS modules automatically scope classes, providing natural isolation */
.ComponentNameContainer {
  /* This is already scoped to the component */
}
```

## Pseudo-Classes and Pseudo-Elements Usage Rules

::alert{type="info"}
**Important Rule**: In SCSS and module.scss files, pseudo-classes (:hover, :active, :focus, etc.) and pseudo-elements (::before, ::after, etc.) must be written using the `&` selector within the corresponding class
::

### Guidelines

1. **Use & for pseudo-classes**: Always use `&:hover`, `&:active`, `&:focus`, etc. within the target class
2. **Use & for pseudo-elements**: Always use `&::before`, `&::after`, etc. within the target class
3. **Nested structure**: Keep pseudo-classes and pseudo-elements nested inside their parent class
4. **Consistent indentation**: Maintain proper indentation for readability

### Examples

```scss
/* ✅ Correct - Pseudo-classes using & within the class */
.ComponentNameButton {
  @apply tw-bg-blue-500 tw-text-white tw-px-4 tw-py-2 tw-rounded;

  &:hover {
    @apply tw-bg-blue-600;
    transform: translateY(-1px);
  }

  &:active {
    @apply tw-bg-blue-700;
    transform: translateY(0);
  }

  &:focus {
    @apply tw-outline-none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
  }

  &:disabled {
    @apply tw-bg-gray-400 tw-cursor-not-allowed;
    opacity: 0.6;
  }
}

.ComponentNameCard {
  @apply tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid white;
  }

  &:hover {
    @apply tw-shadow-lg;
    transform: translateY(-2px);

    &::before {
      height: 6px;
    }
  }
}

.ComponentNameInput {
  @apply tw-border tw-border-gray-300 tw-px-3 tw-py-2 tw-rounded;

  &:focus {
    @apply tw-border-blue-500 tw-outline-none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  &:invalid {
    @apply tw-border-red-500;
  }

  &::placeholder {
    @apply tw-text-gray-400;
    font-style: italic;
  }
}
```

```scss
/* ❌ Wrong - Pseudo-classes written separately from their parent class */
.ComponentNameButton {
  @apply tw-bg-blue-500 tw-text-white tw-px-4 tw-py-2 tw-rounded;
}

.ComponentNameButton:hover {  /* Wrong - Should be nested with & */
  @apply tw-bg-blue-600;
}

.ComponentNameButton:active {  /* Wrong - Should be nested with & */
  @apply tw-bg-blue-700;
}

.ComponentNameCard {
  @apply tw-bg-white tw-rounded-lg tw-shadow-md tw-p-6;
}

.ComponentNameCard::before {  /* Wrong - Should be nested with & */
  content: '';
  position: absolute;
  /* ... other styles */
}

.ComponentNameCard::after {  /* Wrong - Should be nested with & */
  content: '';
  position: absolute;
  /* ... other styles */
}
```

### Common Pseudo-Classes Examples

```scss
.ComponentNameElement {
  /* Base styles */
  @apply tw-transition-all tw-duration-200;

  /* Interactive states */
  &:hover {
    /* Hover styles */
  }

  &:active {
    /* Active/pressed styles */
  }

  &:focus {
    /* Focus styles */
  }

  &:focus-visible {
    /* Focus visible styles */
  }

  &:disabled {
    /* Disabled styles */
  }

  &:checked {
    /* Checked styles (for inputs) */
  }

  &:invalid {
    /* Invalid styles (for form inputs) */
  }

  &:required {
    /* Required styles (for form inputs) */
  }

  &:first-child {
    /* First child styles */
  }

  &:last-child {
    /* Last child styles */
  }

  &:nth-child(even) {
    /* Even child styles */
  }

  &:nth-child(odd) {
    /* Odd child styles */
  }
}
```

### Common Pseudo-Elements Examples

```scss
.ComponentNameElement {
  /* Base styles */
  position: relative;

  &::before {
    content: '';
    position: absolute;
    /* Before element styles */
  }

  &::after {
    content: '';
    position: absolute;
    /* After element styles */
  }

  &::placeholder {
    /* Placeholder styles (for inputs) */
  }

  &::selection {
    /* Text selection styles */
  }

  &::first-line {
    /* First line styles */
  }

  &::first-letter {
    /* First letter styles */
  }
}
```

### Complex Nesting Examples

```scss
.ComponentNameComplexElement {
  @apply tw-bg-white tw-rounded-lg;

  &:hover {
    @apply tw-shadow-md;

    &::before {
      opacity: 1;
    }

    .ComponentNameNestedElement {
      transform: scale(1.05);
    }
  }

  &::before {
    content: '';
    position: absolute;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:focus-within {
    @apply tw-ring-2 tw-ring-blue-500;

    &::after {
      display: block;
    }
  }
}
```

### Benefits of This Approach

- 🎯 **Clear hierarchy**: Easy to see which pseudo-classes belong to which element
- 🔍 **Better organization**: All related styles are grouped together
- 📦 **SCSS advantage**: Leverages SCSS nesting capabilities properly
- 🧹 **Maintainable code**: Easier to modify and debug styles
- 🚀 **Performance**: More efficient CSS output with proper nesting
- 🎨 **Consistent structure**: Standardized way to handle interactive states

### Real-World Example

```scss
.CompleteProfileHeaderCloseButton {
  @apply tw-p-16 sm:tw-p-16px tw--mx-8 sm:tw--mx-8px tw-text-gray-50;

  &:hover {
    @apply tw-text-gray-50;
    opacity: 0.8;
  }

  &:active {
    @apply tw-text-gray-50;
    transform: scale(0.95);
  }

  &:focus {
    @apply tw-outline-none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
  }
}
```

::alert{type="warning"}
**Remember**: Always use the `&` selector for pseudo-classes and pseudo-elements to maintain proper SCSS structure and component organization
::

## ClassName Merging Rules

::alert{type="info"}
**Important Rule**: When merging multiple className values in components, always use `getClassName` from `../../common/utils/ui`
::

### Guidelines

1. **Use getClassName for merging**: Always use the `getClassName` utility function when combining multiple className values
2. **Import from correct location**: Import `getClassName` from `../../common/utils/ui`
3. **Pass array of classNames**: Provide all className values as an array to the function
4. **Automatic filtering**: The function automatically filters out falsy values (null, undefined, empty strings)

### Import Statement

```javascript
import { getClassName } from '../../common/utils/ui'
```

### Examples

```javascript
// ✅ Correct - Using getClassName for multiple classNames
import { getClassName } from '../../common/utils/ui'
import styles from './ComponentName.module.scss'

const ComponentName = ({ isActive, disabled, customClass }) => {
  return (
    <div className={getClassName([
      styles.ComponentNameContainer,
      isActive && styles.ComponentNameActive,
      disabled && styles.ComponentNameDisabled,
      customClass
    ])}>
      Content
    </div>
  )
}

ComponentName.displayName = 'ComponentName'

// ✅ Correct - With conditional classes
const buttonClassName = getClassName([
  styles.ComponentNameButton,
  type === 'primary' && styles.ComponentNamePrimary,
  type === 'secondary' && styles.ComponentNameSecondary,
  isLoading && styles.ComponentNameLoading,
  className // External className prop
])

<button className={buttonClassName}>
  Click Me
</button>

// ✅ Correct - Simple merge
const containerClass = getClassName([
  styles.ComponentNameWrapper,
  'external-class',
  props.className
])
```

```javascript
// ❌ Wrong - Manual string concatenation
const className = `${styles.ComponentNameContainer} ${isActive ? styles.ComponentNameActive : ''} ${customClass || ''}`

// ❌ Wrong - Using template literals with conditions
const className = `${styles.ComponentNameContainer} ${isActive && styles.ComponentNameActive} ${customClass}`

// ❌ Wrong - Array join without filtering
const className = [
  styles.ComponentNameContainer,
  isActive && styles.ComponentNameActive,
  customClass
].join(' ')

// ❌ Wrong - Using other className utility libraries
import classNames from 'classnames'
const className = classNames(styles.ComponentNameContainer, isActive && styles.ComponentNameActive)
```

### Function Implementation

The `getClassName` function implementation:

```javascript
export const getClassName = classList => classList.filter(className => !!className).join(' ')
```

### Benefits

- 🧹 **Automatic filtering**: Removes falsy values (null, undefined, empty strings, false)
- 🎯 **Consistent approach**: Standardized way to merge classNames across the project
- 🔍 **Clean output**: No extra spaces or empty className values
- 📦 **Project-specific**: Uses the project's own utility function
- 🚀 **Performance**: Lightweight and efficient implementation

### Common Use Cases

#### Conditional Styling
```javascript
const itemClass = getClassName([
  styles.ComponentNameItem,
  isSelected && styles.ComponentNameSelected,
  isHovered && styles.ComponentNameHovered,
  isDisabled && styles.ComponentNameDisabled
])
```

#### Theme-based Styling
```javascript
const buttonClass = getClassName([
  styles.ComponentNameButton,
  theme === 'primary' && styles.ComponentNamePrimary,
  theme === 'secondary' && styles.ComponentNameSecondary,
  theme === 'danger' && styles.ComponentNameDanger
])
```

#### External className Props
```javascript
const ComponentName = ({ className, variant, size }) => {
  const containerClass = getClassName([
    styles.ComponentNameContainer,
    variant && styles[`ComponentName${variant}`],
    size && styles[`ComponentNameSize${size}`],
    className // Allow external override
  ])

  return <div className={containerClass}>Content</div>
}

ComponentName.displayName = 'ComponentName'
```

### Why Use getClassName

- ✅ **Project consistency**: Follow the established pattern in the codebase
- ✅ **Automatic cleanup**: Handles falsy values without manual checks
- ✅ **Readable code**: Clear intent when merging multiple classes
- ✅ **Maintainable**: Single utility function to maintain
- ✅ **No external dependencies**: Uses project's own implementation

## Tailwind CSS @apply Usage Rules

::alert{type="warning"}
**Important Rule**: Do not write Tailwind unsupported style variable names after @apply
::

### Guidelines

1. **Only use supported utilities**: Only use Tailwind CSS utility classes that are officially supported after @apply
2. **Avoid custom CSS properties**: Do not mix custom CSS properties or non-Tailwind styles with @apply
3. **Check Tailwind documentation**: Verify that the utility class exists in Tailwind CSS before using it with @apply
4. **Use regular CSS for custom styles**: Write custom styles using standard CSS syntax instead of @apply

### Examples

```scss
/* ✅ Correct - Using supported Tailwind utilities with @apply */
.ComponentNameButton {
  @apply bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600;
}

.ComponentNameContainer {
  @apply flex flex-col items-center justify-center w-full h-full;
}

.ComponentNameCard {
  @apply bg-white shadow-lg rounded-lg p-6 border border-gray-200;
}
```

```scss
/* ❌ Wrong - Using unsupported or custom variable names with @apply */
.ComponentNameButton {
  @apply custom-blue-color text-white px-4 py-2; /* custom-blue-color is not a Tailwind utility */
}

.ComponentNameContainer {
  @apply flex flex-col my-custom-spacing; /* my-custom-spacing is not a Tailwind utility */
}

.ComponentNameCard {
  @apply bg-white shadow-custom rounded-lg; /* shadow-custom is not a standard Tailwind utility */
}
```

### Correct Approach for Custom Styles

```scss
/* ✅ Correct - Mix @apply with regular CSS */
.ComponentNameButton {
  @apply bg-blue-500 text-white px-4 py-2 rounded-md;
  /* Custom styles using regular CSS */
  font-family: 'Custom Font', sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ComponentNameContainer {
  @apply flex flex-col items-center;
  /* Custom spacing using CSS variables or regular values */
  gap: var(--custom-spacing, 1rem);
  min-height: calc(100vh - 4rem);
}
```

### Benefits of Following This Rule

- 🚀 **Build performance**: Avoids Tailwind CSS compilation errors
- 🔍 **Clear debugging**: Easy to identify which styles are Tailwind utilities vs custom CSS
- 📦 **Maintainability**: Consistent usage of Tailwind CSS features
- 🧹 **Code clarity**: Separation between utility classes and custom styles
- ⚡ **Development speed**: Prevents build-time errors and debugging issues

### Common Mistakes to Avoid

```scss
/* ❌ Wrong examples */
.ComponentName {
  @apply flex customColor; /* customColor is not a Tailwind utility */
  @apply bg-primary-500; /* bg-primary-500 might not exist in your Tailwind config */
  @apply text-custom-size; /* text-custom-size is not a standard Tailwind utility */
  @apply border-my-style; /* border-my-style is not a Tailwind utility */
}
```

### How to Handle Custom Values

```scss
/* ✅ Correct approach for custom values */
.ComponentName {
  /* Use @apply for standard Tailwind utilities */
  @apply flex items-center justify-between bg-white rounded-lg shadow-md;

  /* Use regular CSS for custom values */
  color: var(--primary-color, #3b82f6);
  font-size: var(--custom-font-size, 1rem);
  border: 2px solid var(--border-color, #e5e7eb);
  transition: transform 0.2s ease-in-out;
}
```

## Allowed tw- Prefixed Tailwind Utilities for @apply

::alert{type="danger"}
**Strict Rule**: When using @apply with tw- prefixed utilities, only use the following approved list from the existing codebase
::

### Allowed tw- Utility Classes

#### Layout Classes
```scss
/* ✅ Layout utilities */
tw-flex
tw-flex-col
tw-flex-row
tw-flex-grow
tw-flex-shrink-0
tw-flex-1
tw-block
tw-inline-block
tw-relative
tw-absolute
```

#### Sizing Classes
```scss
/* ✅ Width utilities */
tw-w-full
tw-w-3/10
tw-w-1/4
tw-w-0
tw-w-8

/* ✅ Height utilities */
tw-h-full
tw-h-0
tw-h-8
```

#### Alignment Classes
```scss
/* ✅ Flex alignment utilities */
tw-items-center
tw-items-stretch
tw-items-start
tw-justify-between
tw-justify-center
tw-justify-start

/* ✅ Text alignment utilities */
tw-text-center
tw-text-left
```

#### Color Classes
```scss
/* ✅ Background color utilities */
tw-bg-gray-200
tw-bg-gray-100
tw-bg-black
tw-bg-white
tw-bg-orange-light
tw-bg-transparent

/* ✅ Text color utilities */
tw-text-white
tw-text-gray-600
tw-text-gray-700
tw-text-gray-900
tw-text-gray-500
tw-text-gray
tw-text-yellow
tw-text-orange-dark

/* ✅ Opacity utilities */
tw-bg-opacity-50
tw-bg-opacity-30
```

#### Typography Classes
```scss
/* ✅ Font size utilities */
tw-text-base
tw-text-lg
tw-text-xl
tw-text-3xl
tw-text-sm
tw-text-xs

/* ✅ Font weight utilities */
tw-font-bold
tw-font-black

/* ✅ Line height utilities */
tw-leading-relaxed
tw-leading-loose
tw-leading-normal

/* ✅ Text utilities */
tw-truncate
tw-uppercase
tw-capitalize
tw-break-words
```

#### Spacing Classes - Padding
```scss
/* ✅ Padding utilities */
tw-p-0
tw-p-2
tw-p-12

/* ✅ Horizontal padding utilities */
tw-px-4
tw-px-12
tw-px-16

/* ✅ Vertical padding utilities */
tw-py-4
tw-py-6
tw-py-8
tw-py-12

/* ✅ Individual padding utilities */
tw-pt-4
tw-pt-16
tw-pb-2
tw-pb-8
tw-pb-12
tw-pr-4
tw-pr-8
tw-pl-16
```

#### Spacing Classes - Margin
```scss
/* ✅ Margin utilities */
tw-m-0
tw-m-4
tw-m-8

/* ✅ Horizontal margin utilities */
tw-mx-4
tw-mx-16
tw-mx-auto

/* ✅ Vertical margin utilities */
tw-my-2
tw-my-4
tw-my-6
tw-my-8
tw-my-16

/* ✅ Individual margin utilities */
tw-mt-2
tw-mt-4
tw-mt-6
tw-mt-16
tw-mb-2
tw-mr-4
tw-mr-6
tw-mr-12
tw-mr-16
tw-ml-6
tw-ml-12

/* ✅ Negative margin utilities */
tw--mt-4
tw--mx-12
tw--left-6
```

#### Spacing Classes - Gap & Space
```scss
/* ✅ Gap utilities */
tw-gap-x-16

/* ✅ Space utilities */
tw-space-y-4
```

#### Border Classes
```scss
/* ✅ Border utilities */
tw-border-0
tw-border-none
tw-border-solid
tw-border-b
tw-border-r
tw-border-gray-200
tw-border-l-transparent

/* ✅ Border radius utilities */
tw-rounded
tw-rounded-xl
tw-rounded-lg
tw-rounded-full
```

#### Utility Classes
```scss
/* ✅ Overflow utilities */
tw-overflow-hidden
tw-overflow-y-auto

/* ✅ Cursor utilities */
tw-cursor-pointer

/* ✅ Outline utilities */
tw-outline-none

/* ✅ Z-index utilities */
tw-z-10
tw-z-20

/* ✅ Position utilities */
tw-top-0
tw-left-0
tw-right-0

/* ✅ Object fit utilities */
tw-object-cover
tw-object-center

/* ✅ Flex wrap utilities */
tw-flex-wrap

/* ✅ Divide utilities */
tw-divide-x-0
tw-divide-y
tw-divide-solid
tw-divide-gray-200
```

#### Responsive Variants (sm: prefix)
```scss
/* ✅ All above utilities with sm: responsive prefix are allowed */
/* Examples: */
sm:tw-px-4px
sm:tw-py-8px
sm:tw-m-4px
sm:tw-p-12px
sm:tw-mx-16px
sm:tw-my-2px
sm:tw-pt-4px
sm:tw-pb-12px
sm:tw-pr-16px
sm:tw-pl-16px
sm:tw-mt-4px
sm:tw-mb-2px
sm:tw-mr-4px
sm:tw-ml-12px
sm:tw-gap-x-16px
sm:tw-space-y-4px
/* And all other responsive variants following the same pattern */
```

### ❌ Forbidden Examples
```scss
/* ❌ These are NOT allowed */
.ComponentName {
  @apply tw-custom-color; /* Custom classes not in approved list */
  @apply tw-bg-primary-500; /* Color variants not in approved list */
  @apply tw-text-custom-size; /* Custom text sizes not in approved list */
  @apply tw-border-my-style; /* Custom border styles not in approved list */
  @apply tw-shadow-custom; /* Shadow utilities not in approved list */
  @apply tw-animation-bounce; /* Animation utilities not in approved list */
}
```

### ✅ Correct Usage Examples
```scss
/* ✅ Correct - Only using approved tw- utilities */
.ComponentNameContainer {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-center tw-w-full tw-h-full tw-bg-white tw-rounded-lg;
}

.ComponentNameButton {
  @apply tw-inline-block tw-px-4 tw-py-2 tw-text-white tw-bg-gray-200 tw-rounded tw-cursor-pointer;
}

.ComponentNameText {
  @apply tw-text-base tw-font-bold tw-leading-relaxed tw-text-gray-700 tw-truncate;
}

/* ✅ Correct - Mixing with responsive variants */
.ComponentNameResponsive {
  @apply tw-p-12 sm:tw-p-12px tw-mx-16 sm:tw-mx-16px tw-text-center;
}
```

### Benefits of This Restriction

- 🎯 **Consistency**: Ensures all tw- utilities are from the existing, tested codebase
- 🔍 **Predictable**: Developers know exactly which utilities are available
- 📦 **Maintainable**: Easy to track and update the approved utility list
- 🚀 **Performance**: Prevents unused utilities from being generated
- 🧹 **Clean code**: Standardized utility usage across the project

## 📱 Mobile-First Tailwind Spacing Rules

> **Critical Rule:**
> Application uses mobile-first approach for Tailwind spacing utilities with specific responsive patterns

### 📋 Mobile-First Spacing Guidelines

#### Base Principle
- **Mobile-first approach**: Base styles target mobile devices (< 420px)
- **Desktop enhancement**: Use `sm:` prefix for screens ≥ 420px
- **No @media queries**: Use Tailwind responsive prefixes instead of custom media queries
- **Spacing consistency**: All Tailwind spacing utilities follow this pattern

#### Spacing Pattern Rules

1. **Base spacing**: Use utilities without `px` suffix for mobile (< 420px)
2. **Desktop spacing**: Add `sm:` prefix with `px` suffix for desktop (≥ 420px)
3. **Responsive pairing**: Always pair base mobile styles with desktop equivalents
4. **Single @apply per selector**: Use only one `@apply` statement within each SCSS selector
5. **Immediate pairing**: Each mobile utility should be immediately followed by its desktop equivalent

#### 🎯 Correct Pairing Order Rules

```scss
/* ✅ Correct - Immediate pairing pattern */
@apply tw-gap-x-16 sm:tw-gap-x-16px tw-space-y-4 sm:tw-space-y-4px;

/* ❌ Wrong - All mobile utilities first, then all desktop utilities */
@apply tw-gap-x-16 tw-space-y-4 tw-p-12 sm:tw-gap-x-16px sm:tw-space-y-4px sm:tw-p-12px;

/* ❌ Wrong - Multiple @apply statements in one selector */
@apply tw-p-12 sm:tw-p-12px;
@apply tw-mx-8 sm:tw-mx-8px;
```

### ✅ Correct Mobile-First Spacing Examples

#### Padding Utilities
```scss
.ComponentNameContainer {
  /* Mobile-first: Base padding for mobile devices */
  @apply tw-p-12 sm:tw-p-12px;
}

.ComponentNameButton {
  /* Mobile and desktop padding */
  @apply tw-px-4 tw-py-2 sm:tw-px-4px sm:tw-py-2px;
}

.ComponentNameCard {
  /* Mobile and desktop padding */
  @apply tw-p-16 sm:tw-p-16px;
}
```

#### Margin Utilities
```scss
.ComponentNameHeader {
  /* Mobile and desktop margins */
  @apply tw-mx-8 sm:tw-mx-8px tw-my-4 sm:tw-my-4px;
}

.ComponentNameSection {
  /* Mobile and desktop margins */
  @apply tw-mt-6 tw-mb-12 sm:tw-mt-6px sm:tw-mb-12px;
}

.ComponentNameSidebar {
  /* Mobile and desktop negative margins */
  @apply tw--mx-4 tw--mt-2 sm:tw--mx-4px sm:tw--mt-2px;
}
```

#### Gap and Space Utilities
```scss
.ComponentNameGrid {
  /* Mobile and desktop gap */
  @apply tw-gap-x-16 sm:tw-gap-x-16px tw-space-y-4 sm:tw-space-y-4px;
}

.ComponentNameList {
  /* Mobile and desktop spacing */
  @apply tw-space-y-8 sm:tw-space-y-8px;
}
```

### ❌ Wrong Spacing Patterns

#### Avoid These Approaches

```scss
/* ❌ Wrong - Using @media queries instead of Tailwind responsive prefixes */
.ComponentNameContainer {
  @apply tw-p-12;

  @media (min-width: 420px) {
    padding: 12px;
  }
}

/* ❌ Wrong - Not following mobile-first approach */
.ComponentNameButton {
  @apply sm:tw-px-4 sm:tw-py-2; /* Missing base mobile styles */
}

/* ❌ Wrong - Inconsistent px suffix usage */
.ComponentNameCard {
  @apply tw-p-12px sm:tw-p-12; /* Should be: tw-p-12 sm:tw-p-12px */
}

/* ❌ Wrong - Missing responsive pairing */
.ComponentNameHeader {
  @apply tw-mx-8; /* Missing sm: desktop equivalent */
}
```

### 🎯 Complete Responsive Spacing Examples

#### Complex Component Spacing
```scss
.CompleteProfileHeaderContainer {
  /* Mobile and desktop spacing */
  @apply tw-p-16 tw-mx-8 tw-my-4 sm:tw-p-16px sm:tw-mx-8px sm:tw-my-4px;

  /* Additional non-spacing styles */
  @apply tw-flex tw-flex-col tw-items-center tw-bg-white tw-rounded-lg;
}

.CompleteProfileFormSection {
  /* Mobile and desktop spacing */
  @apply tw-px-12 tw-py-8 tw-mt-6 tw-mb-16 sm:tw-px-12px sm:tw-py-8px sm:tw-mt-6px sm:tw-mb-16px;

  /* Layout and appearance */
  @apply tw-border tw-border-gray-200 tw-rounded;
}
```

#### Grid and Flexbox with Spacing
```scss
.ComponentNameGridContainer {
  /* Mobile and desktop layout and spacing */
  @apply tw-flex tw-flex-col tw-gap-x-16 tw-space-y-4 tw-p-12 sm:tw-gap-x-16px sm:tw-space-y-4px sm:tw-p-12px;

  /* Non-spacing utilities */
  @apply tw-w-full tw-bg-gray-100;
}
```

### 📏 Spacing Scale Reference

#### Common Mobile-First Patterns
```scss
/* Small spacing */
tw-p-2 sm:tw-p-2px
tw-m-4 sm:tw-m-4px
tw-gap-x-8 sm:tw-gap-x-8px

/* Medium spacing */
tw-p-12 sm:tw-p-12px
tw-mx-16 sm:tw-mx-16px
tw-space-y-6 sm:tw-space-y-6px

/* Large spacing */
tw-p-24 sm:tw-p-24px
tw-my-32 sm:tw-my-32px
tw-gap-y-20 sm:tw-gap-y-20px
```

### 🔧 Implementation Checklist

- [ ] Use mobile-first approach (base styles without px)
- [ ] Add desktop styles with `sm:` prefix and `px` suffix
- [ ] Apply pattern to all spacing utilities (padding, margin, gap, space)
- [ ] Use only ONE @apply statement per SCSS selector
- [ ] Immediately pair each mobile utility with its desktop equivalent
- [ ] Separate spacing utilities from non-spacing utilities in different @apply statements
- [ ] Avoid custom @media queries for spacing
- [ ] Pair every base spacing with responsive equivalent
- [ ] Maintain consistent spacing scale across components
- [ ] Follow Tailwind's responsive design philosophy

### 📱 Responsive Breakpoint Reference

- **Mobile (base)**: `< 420px` - Use utilities without px suffix
- **Desktop (sm:)**: `≥ 420px` - Use `sm:` prefix with px suffix

### 🚀 Benefits of This Approach

- 🎯 **Mobile-optimized**: Prioritizes mobile user experience
- 📦 **Consistent pattern**: Standardized spacing approach across app
- 🔍 **Easy maintenance**: Clear responsive spacing rules
- ⚡ **Performance**: Leverages Tailwind's optimized responsive utilities
- 🧹 **Clean code**: No custom media queries needed

> **Success:**
> Following these mobile-first spacing rules ensures consistent, responsive design that prioritizes mobile users while enhancing desktop experience

## ⚙️ Tailwind Configuration Constraint Rules

> **Critical Rule:**
> All Tailwind utilities must strictly follow the predefined configuration in `tailwind.config.js` to maintain design consistency and prevent style conflicts

### 📋 Configuration-Only Properties

The following properties are strictly controlled by `tailwind.config.js` configuration and cannot use arbitrary values:

#### 🚫 Restricted Properties (Config-Only)
- **border-radius**: Only use configured rounded values
- **font-size**: Only use configured text size values
- **font-weight**: Only use configured font weight values
- **line-height**: Only use configured leading values
- **letter-spacing**: Only use configured tracking values
- **box-shadow**: Only use configured shadow values
- **spacing**: Only use configured padding/margin values
- **colors**: Only use configured color values
- **zIndex**: Only use configured z-index values
- **screens**: Only use configured responsive breakpoints

#### ✅ Flexible Property
- **width**: Can use arbitrary values when needed (e.g., `tw-w-[320px]`)

### 📱 Responsive Design Architecture

#### Mobile-First Approach (414px Breakpoint)
- **Mobile (base)**: `< 414px` - Primary target for beep-v1-webapp
- **Desktop/Tablet (sm:)**: `≥ 414px` - Combined breakpoint for larger screens
- **No tablet-specific**: No separate tablet breakpoint - desktop and tablet share the same styles

#### Current Implementation Pattern
```scss
/* ✅ Correct - Standard mobile-first pattern */
@apply tw-px-12 sm:tw-px-12px tw-py-8 sm:tw-py-8px;

/* Mobile-first principle: Keep desktop/tablet styles identical to mobile when possible */
```

### ✅ Configuration-Compliant Examples

#### Border Radius (Config-Only)
```scss
/* ✅ Correct - Using configured rounded values */
.ComponentNameCard {
  @apply tw-rounded-lg sm:tw-rounded-lg; /* From config */
}

.ComponentNameButton {
  @apply tw-rounded sm:tw-rounded tw-rounded-full sm:tw-rounded-full; /* From config */
}

/* ❌ Wrong - Arbitrary border radius values */
.ComponentNameCard {
  @apply tw-rounded-[12px]; /* Not allowed - must use config values */
}
```

#### Typography (Config-Only)
```scss
/* ✅ Correct - Using configured typography values */
.ComponentNameTitle {
  @apply tw-text-xl sm:tw-text-xl tw-font-bold sm:tw-font-bold tw-leading-normal sm:tw-leading-normal;
}

.ComponentNameText {
  @apply tw-text-base sm:tw-text-base tw-font-normal sm:tw-font-normal tw-tracking-normal sm:tw-tracking-normal;
}

/* ❌ Wrong - Arbitrary typography values */
.ComponentNameTitle {
  @apply tw-text-[18px] tw-font-[600] tw-leading-[1.2]; /* Not allowed */
}
```

#### Colors (Config-Only)
```scss
/* ✅ Correct - Using configured color values */
.ComponentNameContainer {
  @apply tw-bg-white sm:tw-bg-white tw-text-gray-900 sm:tw-text-gray-900;
}

.ComponentNameAccent {
  @apply tw-bg-orange-light sm:tw-bg-orange-light tw-text-orange-dark sm:tw-text-orange-dark;
}

/* ❌ Wrong - Arbitrary color values */
.ComponentNameContainer {
  @apply tw-bg-[#ffffff] tw-text-[#1a1a1a]; /* Not allowed */
}
```

#### Shadows (Config-Only)
```scss
/* ✅ Correct - Using configured shadow values */
.ComponentNameCard {
  @apply tw-shadow-md sm:tw-shadow-md;
}

.ComponentNameModal {
  @apply tw-shadow-lg sm:tw-shadow-lg;
}

/* ❌ Wrong - Arbitrary shadow values */
.ComponentNameCard {
  @apply tw-shadow-[0_4px_6px_rgba(0,0,0,0.1)]; /* Not allowed */
}
```

#### Z-Index (Config-Only)
```scss
/* ✅ Correct - Using configured z-index values */
.ComponentNameModal {
  @apply tw-z-20 sm:tw-z-20;
}

.ComponentNameTooltip {
  @apply tw-z-10 sm:tw-z-10;
}

/* ❌ Wrong - Arbitrary z-index values */
.ComponentNameModal {
  @apply tw-z-[999]; /* Not allowed */
}
```

#### Width (Flexible Property)
```scss
/* ✅ Correct - Width can use arbitrary values when needed */
.ComponentNameContainer {
  @apply tw-w-full sm:tw-w-full; /* Configured value preferred */
}

.ComponentNameSpecific {
  @apply tw-w-[320px] sm:tw-w-[320px]; /* Arbitrary values allowed for width */
}

.ComponentNameCustom {
  @apply tw-w-3/10 sm:tw-w-3/10; /* Custom fraction if configured */
}
```

### 🎯 Design Consistency Guidelines

#### Keep Desktop/Tablet Identical to Mobile
```scss
/* ✅ Correct - Maintaining consistency across breakpoints */
.ComponentNameButton {
  /* Same styles for mobile and desktop/tablet */
  @apply tw-px-12 sm:tw-px-12px tw-py-8 sm:tw-py-8px tw-rounded sm:tw-rounded tw-font-bold sm:tw-font-bold;
}

.ComponentNameCard {
  /* Consistent design across all screen sizes */
  @apply tw-bg-white sm:tw-bg-white tw-shadow-md sm:tw-shadow-md tw-rounded-lg sm:tw-rounded-lg;
}
```

#### Responsive Layout Adjustments
```scss
/* ✅ Correct - Only adjust layout, not design properties */
.ComponentNameGrid {
  /* Layout changes for larger screens */
  @apply tw-flex tw-flex-col sm:tw-flex-row tw-gap-x-16 sm:tw-gap-x-16px;

  /* Keep design properties consistent */
  @apply tw-bg-white sm:tw-bg-white tw-rounded-lg sm:tw-rounded-lg;
}
```

### ❌ Configuration Violation Examples

#### Don't Use Arbitrary Values for Restricted Properties
```scss
/* ❌ Wrong - Arbitrary values for config-controlled properties */
.ComponentNameWrong {
  @apply tw-text-[14px]; /* Use configured text sizes */
  @apply tw-rounded-[8px]; /* Use configured border radius */
  @apply tw-shadow-[0_2px_4px_rgba(0,0,0,0.1)]; /* Use configured shadows */
  @apply tw-bg-[#f8f9fa]; /* Use configured colors */
  @apply tw-font-[500]; /* Use configured font weights */
  @apply tw-leading-[1.4]; /* Use configured line heights */
  @apply tw-tracking-[0.5px]; /* Use configured letter spacing */
  @apply tw-z-[15]; /* Use configured z-index values */
}
```

#### Don't Create Inconsistent Responsive Designs
```scss
/* ❌ Wrong - Different design between mobile and desktop */
.ComponentNameInconsistent {
  /* Mobile */
  @apply tw-bg-white tw-rounded sm:tw-bg-gray-100 sm:tw-rounded-lg; /* Different backgrounds/radius */

  /* This creates inconsistent user experience */
}
```

### 🔧 Implementation Guidelines

#### Before Writing Styles
1. **Check tailwind.config.js**: Verify available values for the property
2. **Use configured values**: Only use predefined options for restricted properties
3. **Maintain consistency**: Keep mobile and desktop/tablet styles identical when possible
4. **Width exceptions**: Use arbitrary width values only when necessary

#### Property Usage Checklist
- [ ] **Border radius**: Use only configured `rounded-*` values
- [ ] **Typography**: Use only configured `text-*`, `font-*`, `leading-*`, `tracking-*` values
- [ ] **Colors**: Use only configured color palette
- [ ] **Shadows**: Use only configured `shadow-*` values
- [ ] **Spacing**: Use only configured spacing scale
- [ ] **Z-index**: Use only configured `z-*` values
- [ ] **Responsive**: Maintain mobile-first approach with 414px breakpoint
- [ ] **Consistency**: Keep desktop/tablet identical to mobile when possible

### 📱 Mobile App Design Priorities

#### Core Principles
1. **Mobile-first**: Primary focus on mobile user experience (< 414px)
2. **Consistency**: Desktop/tablet should enhance, not redesign the mobile experience
3. **Configuration compliance**: Strict adherence to design token system
4. **Performance**: Leverage Tailwind's optimized configuration for faster builds

#### Design Token Benefits
- 🎯 **Design consistency**: Unified visual language across the app
- 🔍 **Easier maintenance**: Centralized design decisions in config
- 📦 **Reduced bundle size**: Only configured values are included
- 🚀 **Better performance**: Optimized CSS output
- 🧹 **Cleaner code**: No arbitrary values cluttering the codebase

### 🔍 Configuration Verification

#### How to Check Available Values
1. **Review tailwind.config.js**: Check the theme configuration
2. **Use IntelliSense**: IDE should show available configured values
3. **Test locally**: Verify that utilities work as expected
4. **Follow patterns**: Look at existing components for reference

#### Common Configuration Sections
```javascript
// Example configuration structure
module.exports = {
  theme: {
    borderRadius: {
      // Only these values are allowed
    },
    fontSize: {
      // Only these values are allowed
    },
    colors: {
      // Only these values are allowed
    },
    spacing: {
      // Only these values are allowed
    },
    screens: {
      sm: '414px' // Mobile-first breakpoint
    }
  }
}
```

> **Success:**
> Following these Tailwind configuration constraints ensures design consistency, performance optimization, and maintainable code across the mobile-first beep-v1-webapp