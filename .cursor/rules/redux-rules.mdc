---
description:
globs:
alwaysApply: false
---
# Redux Development Rules

::alert{type="info"}
This document defines the rules and best practices for Redux development in the project
::

## 🔧 Selector Development Rules

::alert{type="warning"}
**Mandatory**: When creating selectors that reference other selectors, you must use `createSelector` from `@reduxjs/toolkit`
::

### createSelector Usage Requirements

#### 1. When to Use createSelector

**Required scenarios:**
- When a selector depends on other selectors
- When a selector performs computations or transformations
- When combining multiple pieces of state
- When creating derived data from existing selectors

#### 2. Import Requirements

```javascript
// ✅ Correct - Import createSelector from @reduxjs/toolkit
import { createSelector } from '@reduxjs/toolkit';
import { getIsMobile, getIsWeb } from '../../../redux/common/selectors';
```

#### 3. Selector Implementation Patterns

##### ✅ Correct - Using createSelector for composed selectors

```javascript
// ✅ Correct - Selector that depends on other selectors
export const getShouldShowDownloadBanner = createSelector(
  getIsMobile,
  getIsWeb,
  (isMobile, isWeb) => isMobile || isWeb
);

// ✅ Correct - Complex selector with multiple dependencies
export const getUserDisplayInfo = createSelector(
  getUserProfile,
  getUserPreferences,
  getIsLoggedIn,
  (profile, preferences, isLoggedIn) => {
    if (!isLoggedIn) return null;

    return {
      name: profile?.name || 'Guest',
      avatar: profile?.avatar,
      theme: preferences?.theme || 'light',
      displayName: preferences?.showFullName ? profile?.fullName : profile?.firstName
    };
  }
);

// ✅ Correct - Selector with computation
export const getFilteredItems = createSelector(
  getItems,
  getSearchQuery,
  getActiveFilters,
  (items, searchQuery, activeFilters) => {
    return items
      .filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
      .filter(item =>
        activeFilters.length === 0 ||
        activeFilters.includes(item.category)
      );
  }
);
```

##### ❌ Wrong - Direct selector calls without createSelector

```javascript
// ❌ Wrong - Directly calling other selectors without createSelector
export const getShouldShowDownloadBanner = (state) => {
  const isMobile = getIsMobile(state);
  const isWeb = getIsWeb(state);
  return isMobile || isWeb;
};

// ❌ Wrong - Using function without createSelector for composed logic
export const getUserDisplayInfo = (state) => {
  const profile = getUserProfile(state);
  const preferences = getUserPreferences(state);
  const isLoggedIn = getIsLoggedIn(state);

  if (!isLoggedIn) return null;

  return {
    name: profile?.name || 'Guest',
    avatar: profile?.avatar,
    theme: preferences?.theme || 'light'
  };
};
```

#### 4. Simple State Access (createSelector Not Required)

```javascript
// ✅ Correct - Simple state access doesn't need createSelector
export const getLoginDrawerShow = state => state.membershipsPage.loginDrawer.show;

export const getCompleteDrawerShow = state => state.membershipsPage.completeDrawer.show;

export const getUserName = state => state.user.profile.name;

export const getIsLoading = state => state.app.loading;
```

### Benefits of Using createSelector

#### 1. Performance Optimization
- **Memoization**: Results are cached and only recalculated when dependencies change
- **Shallow equality**: Prevents unnecessary re-renders in React components
- **Computation efficiency**: Expensive operations are only performed when needed

#### 2. Composability
- **Reusable selectors**: Can combine existing selectors to create new ones
- **Modular design**: Break complex logic into smaller, testable pieces
- **Dependency management**: Clear declaration of selector dependencies

#### 3. Maintainability
- **Centralized logic**: Business logic is centralized in selectors
- **Easy testing**: Selectors can be easily unit tested
- **Clear dependencies**: Easy to understand what state each selector depends on

### Real-World Examples

#### User Authentication Flow
```javascript
// Base selectors
export const getUser = state => state.user;
export const getAuthToken = state => state.auth.token;
export const getUserPreferences = state => state.user.preferences;

// Composed selectors using createSelector
export const getIsAuthenticated = createSelector(
  getUser,
  getAuthToken,
  (user, token) => !!(user && token)
);

export const getShouldShowOnboarding = createSelector(
  getIsAuthenticated,
  getUserPreferences,
  (isAuthenticated, preferences) =>
    isAuthenticated && !preferences?.hasCompletedOnboarding
);
```

#### E-commerce Cart Logic
```javascript
// Base selectors
export const getCartItems = state => state.cart.items;
export const getProductPrices = state => state.products.prices;
export const getUserDiscounts = state => state.user.discounts;

// Composed selectors
export const getCartTotal = createSelector(
  getCartItems,
  getProductPrices,
  (items, prices) => {
    return items.reduce((total, item) => {
      const price = prices[item.productId] || 0;
      return total + (price * item.quantity);
    }, 0);
  }
);

export const getCartTotalWithDiscounts = createSelector(
  getCartTotal,
  getUserDiscounts,
  (cartTotal, discounts) => {
    const totalDiscount = discounts.reduce((sum, discount) => sum + discount.amount, 0);
    return Math.max(0, cartTotal - totalDiscount);
  }
);
```

### Selector File Organization

#### File Structure
```javascript
// selectors.js
import { createSelector } from '@reduxjs/toolkit';

// Base selectors (direct state access)
export const getUser = state => state.user;
export const getProfile = state => state.user.profile;
export const getPreferences = state => state.user.preferences;

// Composed selectors (using createSelector)
export const getUserDisplayName = createSelector(
  getProfile,
  getPreferences,
  (profile, preferences) => {
    // Computation logic here
  }
);
```

### Testing createSelector

```javascript
// selectors.test.js
import { getUserDisplayInfo } from './selectors';

describe('getUserDisplayInfo', () => {
  it('should return correct display info for logged in user', () => {
    const mockState = {
      user: {
        profile: { name: 'John', fullName: 'John Doe' },
        preferences: { showFullName: true, theme: 'dark' }
      },
      auth: { isLoggedIn: true }
    };

    const result = getUserDisplayInfo(mockState);

    expect(result).toEqual({
      name: 'John',
      displayName: 'John Doe',
      theme: 'dark'
    });
  });

  it('should return null for non-logged in user', () => {
    const mockState = {
      user: { profile: {}, preferences: {} },
      auth: { isLoggedIn: false }
    };

    const result = getUserDisplayInfo(mockState);
    expect(result).toBeNull();
  });
});
```

## 📋 Development Checklist

### Before Writing Selectors
- [ ] Determine if selector needs to reference other selectors
- [ ] Check if computation or transformation is involved
- [ ] Plan selector dependencies and composition

### When Using createSelector
- [ ] Import `createSelector` from `@reduxjs/toolkit`
- [ ] List all dependent selectors as first parameters
- [ ] Write computation function as the last parameter
- [ ] Ensure computation function is pure (no side effects)
- [ ] Add appropriate JSDoc comments for complex selectors

### When NOT Using createSelector
- [ ] Selector only accesses direct state properties
- [ ] No computation or transformation is needed
- [ ] No dependencies on other selectors

## 🚫 Common Mistakes to Avoid

```javascript
// ❌ Wrong - Calling selectors without createSelector
export const getBadSelector = (state) => {
  return getOtherSelector(state) + getAnotherSelector(state);
};

// ❌ Wrong - Using createSelector for simple state access
export const getSimpleValue = createSelector(
  state => state.simple.value,
  value => value
);

// ❌ Wrong - Not importing createSelector
import { getDepSelector } from './otherSelectors';
export const getBadComposed = (state) => {
  return getDepSelector(state) * 2;
};
```

## ✅ Best Practices Summary

1. **🎯 Use createSelector when composing**: Always use `createSelector` when referencing other selectors
2. **⚡ Performance first**: Leverage memoization for expensive computations
3. **🧩 Modular design**: Break complex logic into smaller, reusable selectors
4. **📝 Clear naming**: Use descriptive names that indicate the selector's purpose
5. **🔍 Easy testing**: Write selectors that are easy to unit test
6. **📚 Document complex logic**: Add JSDoc comments for complex selector logic

::alert{type="success"}
Following these Redux selector rules ensures optimal performance, maintainability, and consistency across the application
::
