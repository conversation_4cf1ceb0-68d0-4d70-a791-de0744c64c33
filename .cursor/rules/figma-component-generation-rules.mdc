---
description:
globs:
alwaysApply: true
---
# Figma Component Generation Rules

> **Info:** This document defines the rules and standards for generating React components from Figma designs

## 🎨 Figma to Component Generation Standards

> **Best Practice:** Always analyze Figma design structure before generating components to ensure proper component hierarchy and organization

## 📁 Component Type Classification

### 🏠 Page Components (Container Components)

> **⚠️ Mandatory Rule:** Components in `src/containers/{folder}/index.jsx` are considered page components and must include Frame wrapper

#### Page Component Requirements

1. **File Location**: Must be located in `src/containers/{folderName}/index.jsx`
2. **Frame Wrapper**: Must use `Frame` component as the outermost container
3. **Page Structure**: Follow the standard page component structure
4. **Import Requirements**: Must import Frame from common components

#### Page Component Template

```javascript
// Third-party packages
import React, { useState, useEffect, useCallback } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'

// Static resources
import BackgroundImage from '../../../images/page-background.png'

// Constants
import { API_ENDPOINTS } from '../../../common/constants'

// Utils
import { formatDate } from '../../../utils/helpers'

// Redux
import { getPageData } from '../../../redux/selectors'

// Components
import Frame from '../../../common/components/Frame'
import PageHeader from '../../../common/components/PageHeader'
import PageFooter from '../../../common/components/PageFooter'
import Button from '../../../common/components/Button'
import { toast } from '../../../common/utils/feedback'

// Local components
import LocalComponent from './components/LocalComponent'

// Styles
import styles from './PageName.module.scss'

const PageName = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const pageData = useSelector(getPageData)
  const hasData = pageData && pageData.length > 0
  const [localState, setLocalState] = useState({})
  const handleAction = useCallback(() => {
    // Action logic
    toast(t('action.success'))
  }, [t])

  useEffect(() => {
    // Page initialization logic
  }, [])

  return (
    <Frame className={styles.PageNameContainer}>
      <PageHeader
        title={t('page.title')}
        showBackButton
      />

      <div className={styles.PageNameContent}>
        {/* Page content goes here */}
        <LocalComponent
          data={pageData}
          onAction={handleAction}
        />
      </div>

      <PageFooter />
    </Frame>
  )
}

PageName.displayName = 'PageName'

export default PageName
```

### 🧩 Regular Components

Components not in containers/index.jsx files are considered regular components and should follow standard component structure without Frame wrapper.

```javascript
// Regular component structure (no Frame wrapper)
const ComponentName = ({ className, ...props }) => {
  return (
    <div className={styles.ComponentNameContainer}>
      {/* Component content */}
    </div>
  )
}

ComponentName.displayName = 'ComponentName'
```

## 🎯 Figma Design Analysis Guidelines

### 🔍 Design Structure Recognition

When analyzing Figma designs, identify:

1. **Page Layout**: Full-screen designs are likely page components
2. **Component Hierarchy**: Nested components and their relationships
3. **Interactive Elements**: Buttons, inputs, clickable areas
4. **Content Sections**: Headers, content areas, footers
5. **Responsive Behavior**: Different breakpoints and adaptations

### 📐 Design-to-Code Mapping

#### Layout Analysis
```javascript
// Figma Frame → React Component mapping
Figma Frame "Page Layout" → <Frame> wrapper (for page components)
Figma Frame "Section" → <div> or <section>
Figma Component "Button" → <Button> component
Figma Auto Layout → CSS Flexbox or Grid
```

#### Styling Extraction
```scss
// Extract from Figma design:
// - Colors → CSS custom properties or Tailwind classes
// - Typography → Font size, weight, line height
// - Spacing → Padding, margin, gap
// - Border radius → Rounded corners
// - Shadows → Box shadow values
```

## 🎨 Component Generation Rules by Design Type

### 📱 Mobile-First Designs

```javascript
// For mobile-focused Figma designs
const MobileComponent = () => {
  return (
    <Frame className={styles.MobileComponentContainer}>
      {/* Mobile-optimized layout */}
      <div className={styles.MobileComponentContent}>
        {/* Content with mobile spacing and typography */}
      </div>
    </Frame>
  )
}

MobileComponent.displayName = 'MobileComponent'
```

### 🖥️ Desktop-First Designs

```javascript
// For desktop-focused Figma designs
const DesktopComponent = () => {
  return (
    <Frame className={styles.DesktopComponentContainer}>
      {/* Desktop layout with responsive adaptations */}
      <div className={styles.DesktopComponentContent}>
        {/* Content with desktop spacing and typography */}
      </div>
    </Frame>
  )
}

DesktopComponent.displayName = 'DesktopComponent'
```

### 📐 Responsive Designs

```scss
// Generate responsive styles based on Figma breakpoints
.ComponentNameContainer {
  @apply tw-p-12 sm:tw-p-16 tw-w-full;

  @media (min-width: 768px) {
    // Tablet styles
  }

  @media (min-width: 1024px) {
    // Desktop styles
  }
}
```

## 🛠️ Asset Management from Figma

### 🖼️ Image Asset Handling

```javascript
// When Figma design contains images:
// 1. Download images using Figma MCP
// 2. Place in appropriate images folder
// 3. Import with proper naming convention

import HeroImage from '../../../images/hero-banner.png'
import IconImage from '../../../images/icons/star-icon.svg'

// Use ObjectFitImage for dynamic images
import { ObjectFitImage } from '../../../common/components/Image'

<ObjectFitImage
  src={HeroImage}
  alt="Hero banner"
  className={styles.ComponentNameHeroImage}
/>
```

### 🎨 Color and Typography Extraction

```scss
// Extract design tokens from Figma
:root {
  // Colors from Figma design
  --primary-color: #3B82F6;
  --secondary-color: #10B981;
  --text-primary: #111827;
  --text-secondary: #6B7280;

  // Typography from Figma design
  --font-size-h1: 2rem;
  --font-size-h2: 1.5rem;
  --font-size-body: 1rem;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
}
```

## 📋 Component Generation Checklist

### ✅ Page Component Checklist

- [ ] Located in `src/containers/{folder}/index.jsx`
- [ ] Uses `Frame` component as outermost wrapper
- [ ] Includes `PageHeader` if design shows header
- [ ] Includes `PageFooter` if design shows footer
- [ ] Follows page component import order
- [ ] Has proper `displayName` set
- [ ] All clickable elements have `data-test-id` attributes
- [ ] Uses proper responsive design patterns

### ✅ Regular Component Checklist

- [ ] Located outside containers or not in index.jsx
- [ ] Does not use `Frame` wrapper
- [ ] Follows standard component structure
- [ ] Has proper `displayName` set
- [ ] All clickable elements have `data-test-id` attributes
- [ ] Props are organized in correct order
- [ ] Uses appropriate common components

### ✅ Style Generation Checklist

- [ ] SCSS file named matching component name
- [ ] All CSS classes start with component name
- [ ] Uses approved tw- utilities with @apply
- [ ] Pseudo-classes use `&` selector properly
- [ ] No `!important` usage
- [ ] Responsive design implemented correctly

## 🔄 Figma Update Workflow

### 📝 Design Changes Handling

1. **Compare Figma versions**: Check what changed in the design
2. **Update components**: Modify only affected parts
3. **Preserve functionality**: Keep existing logic and state management
4. **Test responsive behavior**: Ensure all breakpoints work correctly
5. **Update documentation**: Reflect design changes in component docs

### 🚀 Iterative Development

```javascript
// When updating components from Figma changes:
// 1. Preserve existing state and logic
// 2. Update only visual/layout changes
// 3. Maintain backward compatibility
// 4. Test all interactive features

const UpdatedComponent = ({ existingProps }) => {
  // Keep existing functionality
  const [existingState, setExistingState] = useState()

  return (
    <Frame className={styles.UpdatedComponentContainer}>
      {/* Apply new design while preserving functionality */}
    </Frame>
  )
}

UpdatedComponent.displayName = 'UpdatedComponent'
```

## 🎯 Advanced Generation Features

### 🔧 Interactive Element Detection

```javascript
// Automatically detect and implement interactive elements from Figma
const InteractiveComponent = () => {
  const handleButtonClick = useCallback(() => {
    // Auto-generated click handler
  }, [])

  return (
    <Frame className={styles.InteractiveComponentContainer}>
      <Button
        onClick={handleButtonClick}
        data-test-id="page.section.component-name.action-button"
      >
        {t('button.text')}
      </Button>
    </Frame>
  )
}

InteractiveComponent.displayName = 'InteractiveComponent'
```

### 📊 Data Integration

```javascript
// Generate components with proper data integration patterns
const DataDrivenComponent = () => {
  const dispatch = useDispatch()
  const data = useSelector(getComponentData)

  useEffect(() => {
    dispatch(fetchComponentData())
  }, [dispatch])

  return (
    <Frame className={styles.DataDrivenComponentContainer}>
      {data.map(item => (
        <div key={item.id} className={styles.DataDrivenComponentItem}>
          {/* Render data items based on Figma design */}
        </div>
      ))}
    </Frame>
  )
}

DataDrivenComponent.displayName = 'DataDrivenComponent'
```

## 🌟 Best Practices Summary

1. **🏗️ Structure**: Always use Frame for page components in containers/index.jsx
2. **🎨 Design Fidelity**: Match Figma design as closely as possible
3. **📱 Responsive**: Implement responsive behavior based on design breakpoints
4. **♿ Accessibility**: Add proper ARIA labels and semantic HTML
5. **🧪 Testing**: Include data-test-id for all interactive elements
6. **🌐 Internationalization**: Use translation keys for all text content
7. **⚡ Performance**: Optimize images and minimize bundle size
8. **🔧 Maintainability**: Follow project coding standards and conventions

> **Success:** Following these Figma component generation rules ensures consistent, maintainable, and high-quality components that match the design specifications while adhering to project standards
