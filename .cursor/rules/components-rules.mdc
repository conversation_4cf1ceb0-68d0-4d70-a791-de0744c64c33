---
description:
globs:
alwaysApply: true
---
# Component Usage Rules

::alert{type="info"}
This rule defines the usage guidelines for components in `src/common/components/`
::

## 🧩 Common Components Usage Guide

::alert{type="success"}
**Best Practice**: Always use components from `src/common/components/` for consistent UI and better maintainability
::

## 📋 Component Import Methods

::alert{type="warning"}
**Important Rule**: Follow the correct import syntax for each component - some use default exports while others require named exports
::

### 🔸 Components Requiring Named Exports

The following components must be imported using **named export** syntax:

#### Image Components
```javascript
// ✅ Correct - Named exports for Image components
import { ObjectFitImage, Image } from '../../common/components/Image'

<ObjectFitImage src={imageUrl} alt="Description" />
<ObjectFitImage src={staticImage} alt="Static" noCompression />
```

#### PhoneNumber Components
```javascript
// ✅ Correct - Named export for PhoneNumberLabelInside
import { PhoneNumberLabelInside } from '../../common/components/Input/PhoneNumber'

<PhoneNumberLabelInside
  placeholder="Enter phone number"
  defaultCountry="MY"
  onChange={handlePhoneChange}
/>
```

#### SpaceOccupationContext Components
```javascript
// ✅ Correct - Named export for SpaceOccupationContextProvider
import { SpaceOccupationContextProvider } from '../../common/components/SpaceOccupationContext'

<SpaceOccupationContextProvider>
  {children}
</SpaceOccupationContextProvider>
```

#### Icons Components (Deprecated - Use phosphor-react instead)
```javascript
// ⚠️ Deprecated - Use phosphor-react icons instead
import { ActivePointIcon, MapPinIcon, RewardsPoint } from '../../common/components/Icons'

// ✅ Recommended - Use phosphor-react icons
import { Star, MapPin, MagnifyingGlass } from 'phosphor-react'
```

### 🔸 Components Using Default Exports (Most Components)

The majority of components use **default export** syntax:

#### Input Components
```javascript
// ✅ Correct - Default exports for Input components
import InputText from '../../common/components/Input/Text'
import InputNumber from '../../common/components/Input/Number'
import InputEmail from '../../common/components/Input/Email'
import InputBirthday from '../../common/components/Input/Birthday'
import Search from '../../common/components/Input/Search'

<InputText placeholder="Enter text" />
<InputNumber min={0} max={100} />
<InputEmail placeholder="Enter email" />
<Search placeholder="Search..." />
```

#### UI Components
```javascript
// ✅ Correct - Default exports for UI components
import Button from '../../common/components/Button'
import Badge from '../../common/components/Badge'
import Card from '../../common/components/Card'
import CheckBox from '../../common/components/CheckBox'
import Radio from '../../common/components/Radio'
import Tag from '../../common/components/Tag'

<Button type="primary" theme="danger">Delete</Button>
<Card>Content</Card>
<CheckBox checked={isChecked} onChange={handleChange} />
```

#### Layout Components
```javascript
// ✅ Correct - Default exports for Layout components
import Frame from '../../common/components/Frame'
import FullScreenFrame from '../../common/components/FullScreenFrame'
import PageHeader from '../../common/components/PageHeader'
import PageFooter from '../../common/components/PageFooter'
import Drawer from '../../common/components/Drawer'
import Modal from '../../common/components/Modal'

<Frame>Page content</Frame>
<Modal show={isOpen}>Modal content</Modal>
```

#### Content & Utility Components
```javascript
// ✅ Correct - Default exports for Content & Utility components
import Article from '../../common/components/Article'
import Ticket from '../../common/components/Ticket'
import Loader from '../../common/components/Loader'
import PageLoader from '../../common/components/PageLoader'
import PageToast from '../../common/components/PageToast'
import QuantityAdjuster from '../../common/components/QuantityAdjuster'
import ReCAPTCHA from '../../common/components/ReCAPTCHA'
import Hint from '../../common/components/Hint'
import MemberIcon from '../../common/components/MemberIcon'
import DownloadBanner from '../../common/components/DownloadBanner'
import ReactLoadingSkeleton from '../../common/components/ReactLoadingSkeleton'
import SquareSkeleton from '../../common/components/SquareSkeleton'
import Result from '../../common/components/Result'
import TermsAndPrivacy from '../../common/components/TermsAndPrivacy'
import SmartIframe from '../../common/components/SmartIframe'
import Slider from '../../common/components/Slider'

<Loader />
<QuantityAdjuster value={quantity} onChange={handleQuantityChange} />
```

### ❌ Common Import Mistakes

```javascript
// ❌ Wrong - Using named exports for default export components
import Button from '../../common/components/Button'
import { InputText } from '../../common/components/Input/Text'

// ❌ Wrong - Using default exports for named export components
import ObjectFitImage from '../../common/components/Image'
import PhoneNumberLabelInside from '../../common/components/Input/PhoneNumber'

// ❌ Wrong - Incorrect path or mixed syntax
import { InputText, InputNumber, InputEmail, Search } from '../../common/components/Input'
```

### ✅ Complete Import Example

```javascript
// Named exports (specific components only)
import { ObjectFitImage } from '../../common/components/Image'
import { PhoneNumberLabelInside } from '../../common/components/Input/PhoneNumber'
import { SpaceOccupationContextProvider } from '../../common/components/SpaceOccupationContext'
// Default exports (majority of components)
import Button from '../../common/components/Button'
import InputText from '../../common/components/Input/Text'
import InputEmail from '../../common/components/Input/Email'
import Search from '../../common/components/Input/Search'
import Modal from '../../common/components/Modal'
import Card from '../../common/components/Card'
import Loader from '../../common/components/Loader'
// Feedback components (via utils - not direct import)
import { alert, confirm, toast } from '../../common/utils/feedback'
// Icons from phosphor-react (recommended)
import { Star, MapPin, MagnifyingGlass } from 'phosphor-react'
```

### 📋 Quick Reference Table

| Component Category | Import Method | Example |
|-------------------|---------------|---------|
| **Image** | Named Export | `import { ObjectFitImage } from '../../common/components/Image'` |
| **PhoneNumberLabelInside** | Named Export | `import { PhoneNumberLabelInside } from '../../common/components/Input/PhoneNumber'` |
| **SpaceOccupationContext** | Named Export | `import { SpaceOccupationContextProvider } from '../../common/components/SpaceOccupationContext'` |
| **All Input Components** | Default Export | `import InputText from '../../common/components/Input/Text'` |
| **All UI Components** | Default Export | `import Button from '../../common/components/Button'` |
| **All Layout Components** | Default Export | `import Modal from '../../common/components/Modal'` |
| **All Utility Components** | Default Export | `import Loader from '../../common/components/Loader'` |
| **Feedback Components** | Utils Import | `import { alert } from '../../common/utils/feedback'` |
| **Icons** | Phosphor React | `import { Star } from 'phosphor-react'` |

### 📢 Feedback Components

#### 🚨 Alert Component
- **Purpose**: Warning/notification component for error messages
- **Usage**: Don't use directly, use `alert` exported from `src/common/utils/feedback/index.js`

#### ✅ Confirm Component
- **Purpose**: Confirmation dialog for user actions
- **Usage**: Don't use directly, use `confirm` exported from `src/common/utils/feedback/index.js`

#### 🍞 Toast Component
- **Purpose**: Brief notification messages
- **Usage**: Don't use directly, use `toast` exported from `src/common/utils/feedback/index.js`

### 🎨 UI Components

#### 🔘 Button Component
- **Purpose**: All clickable elements
- **Types**:
  - `primary`: Filled background button
  - `secondary`: Bordered button
  - `text`: Text or icon only button
- **Themes**:
  - `danger`: Red button
  - `info`: Blue button
  - `ghost`: Gray background button

```javascript
import Button from '../../common/components/Button'

<Button type="primary" theme="danger">Delete</Button>
<Button type="secondary" theme="info">Info</Button>
<Button type="text">Cancel</Button>
```

#### 🏷️ Badge Component
- **Purpose**: Display item count and shopping cart quantity indicators

#### 🃏 Card Component
- **Purpose**: Container with orange border

#### ☑️ CheckBox Component
- **Purpose**: Multiple selection input

#### 📻 Radio Component
- **Purpose**: Single selection input

#### 🏷️ Tag Component
- **Purpose**: Labels in red, pink, cyan, and green colors

### 📝 Input Components

#### ✏️ InputText Component
- **Purpose**: Text input fields, also used for OTP

#### 🔢 InputNumber Component
- **Purpose**: Numeric input fields

#### 🎂 InputBirthday Component
- **Purpose**: Birthday date input

#### 📱 PhoneNumber/PhoneNumberLabelInside Components
- **Purpose**: Phone number input with validation

#### 📧 InputEmail Component
- **Purpose**: Email input with validation

#### 🔍 Search Component
- **Purpose**: Search input fields

```javascript
import InputText from '../../common/components/Input/Text'
import InputNumber from '../../common/components/Input/Number'
import InputEmail from '../../common/components/Input/Email'
import Search from '../../common/components/Input/Search'

<InputText placeholder="Enter text" />
<InputNumber min={0} max={100} />
<InputEmail placeholder="Enter email" />
<Search placeholder="Search..." />
```

### 🖼️ Media Components

#### 🖼️ Image Component
- **Purpose**: All image references should use `ObjectFitImage`
- **Note**: Add `noCompression` attribute for static images

```javascript
import { ObjectFitImage } from '../../common/components/Image'

<ObjectFitImage src={imageUrl} alt="Description" />
<ObjectFitImage src={staticImage} alt="Static" noCompression />
```

#### 🎠 Slider Component
- **Purpose**: Carousels and horizontal scrolling items
- **Examples**: Rewards horizontal list in `rewards/MembershipDetailV2`

### 📄 Layout Components

#### 🖼️ Frame Component
- **Purpose**: Page container component
- **Usage**: Only used in `containers/` folder `index.jsx` files as the outermost container

#### 🖼️ FullScreenFrame Component
- **Purpose**: Full-screen layout container

#### 📄 PageHeader Component
- **Purpose**: Page header for page components

#### 📄 PageFooter Component
- **Purpose**: Page footer for page components

#### 🗂️ Drawer Component
- **Purpose**: Bottom-up sliding overlay content

#### 🪟 Modal Component
- **Purpose**: Popup notifications (e.g., store legal drinking age notice)

### 📰 Content Components

#### 📰 Article Component
- **Purpose**: Article content display
- **Usage**: Mainly used in `RewardDetail`, `UniquePromoDetail`

#### 🎫 Ticket Component
- **Purpose**: Promo lists and voucher lists display

### ⚙️ Utility Components

#### ⏳ Loader Component
- **Purpose**: Processing display, often used with `PageToast`

#### 📄 PageLoader Component
- **Purpose**: Full page loading state

#### 🍞 PageToast Component
- **Purpose**: Page-level notifications, often used with `Loader` for processing display

#### 🔢 QuantityAdjuster Component
- **Purpose**: Quantity controls for shopping cart and product detail pages

#### 🤖 ReCAPTCHA Component
- **Purpose**: Verification code for login processes

#### 💡 Hint Component
- **Purpose**: Info icon that shows popup on click

#### 👤 MemberIcon Component
- **Purpose**: Display membership level icons

#### 📱 DownloadBanner Component
- **Purpose**: App download promotion banner

### 🎯 Special Components

#### 🔳 ReactLoadingSkeleton Component
- **Purpose**: Used when writing `SkeletonLoader`

#### 🔳 SquareSkeleton Component
- **Purpose**: Square-shaped skeleton loader

#### 📊 Result Component
- **Purpose**: Empty state or failed load notifications
- **Note**: Contains nested `ResultContent`, follow existing examples

#### 📋 TermsAndPrivacy Component
- **Purpose**: All terms and privacy policy components

#### 🌐 SmartIframe Component
- **Purpose**: Content placeholder using GrowthBook

#### 📏 SpaceOccupationContext Component
- **Purpose**: Space occupation context management

### 🚫 Deprecated Components

#### ⚠️ Icons Component
- **Status**: No longer recommended
- **Alternative**: Use icons from `phosphor-react` package instead

```javascript
// ❌ Avoid using Icons component
import { Icons } from '../../common/components/Icons'

// ✅ Use phosphor-react icons instead
import { Star, MapPin, MagnifyingGlass } from 'phosphor-react'
```

::alert{type="success"}
Following these component usage rules ensures consistent UI/UX and better code maintainability across the application
::
