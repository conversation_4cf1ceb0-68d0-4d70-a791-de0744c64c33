---
description:
globs:
alwaysApply: true
---
# Environment Variables Synchronization Rules

::alert{type="info"}
This rule defines how to handle environment variables synchronization between .env files and .env.example templates
::

## 🎯 React Environment Variables Naming Rules

::alert{type="danger"}
**Strict Rule**: All environment variables used in React application code must start with `REACT_APP_` prefix. Build-time and server-side environment variables do not require this prefix.
::

### REACT_APP_ Prefix Requirements

#### ✅ Requires REACT_APP_ Prefix (Used in React Code)

Environment variables that are accessed in React components, hooks, or any frontend JavaScript code:

```bash
# API endpoints used in React components
export REACT_APP_API_BASE_URL="https://api.example.com"
export REACT_APP_GRAPHQL_ENDPOINT="https://graphql.example.com"

# Feature flags checked in React code
export REACT_APP_ENABLE_ANALYTICS="true"
export REACT_APP_ENABLE_DEBUG_MODE="false"

# Public configuration used in frontend
export REACT_APP_GOOGLE_MAPS_API_KEY="your_google_maps_key"
export REACT_APP_STRIPE_PUBLIC_KEY="pk_live_xxxxxxxxxxxxxxxx"

# Environment detection in React
export REACT_APP_ENVIRONMENT="production"
export REACT_APP_VERSION="1.0.0"
```

#### ❌ Does NOT Require REACT_APP_ Prefix (Build/Server Only)

Environment variables used only for build processes, server configuration, or external tools:

```bash
# MCP Server configurations (not used in React code)
export FIGMA_ACCESS_TOKEN="figd_xxxxxxxxxxxxxxxx"
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxx"
export JIRA_API_TOKEN="ATATT3xFfGF0xxxxxxxxxxxxxxxx"
export LARK_APP_SECRET="your_lark_app_secret_here"

# Build-time configurations
export NODE_ENV="production"
export BUILD_PATH="./dist"
export GENERATE_SOURCEMAP="false"

# Server-side only configurations
export DATABASE_URL="postgresql://user:pass@localhost:5432/db"
export JWT_SECRET="your_jwt_secret_here"
export PORT="3000"

# Tool-specific configurations
export READ_ONLY_MODE="true"
export ENABLED_TOOLS="jira_get_issue,jira_search"
```

### How to Determine Which Prefix to Use

```javascript
// ✅ Needs REACT_APP_ prefix - Used in React components
const apiUrl = process.env.REACT_APP_API_BASE_URL;
const isDebugMode = process.env.REACT_APP_ENABLE_DEBUG_MODE === 'true';

// ❌ Does NOT need REACT_APP_ prefix - Used only in build/server
const jiraToken = process.env.JIRA_API_TOKEN; // Only used in MCP server
const dbUrl = process.env.DATABASE_URL; // Only used in backend
```

## 📂 Environment Variables Organization Rules

::alert{type="warning"}
**Mandatory**: All environment variables must be organized into appropriate categories. New variables should be placed under existing categories or under "Others" if no category fits.
::

### Defined Categories

Based on the current `.mcp_env` structure, organize variables under these categories:

#### 1. Figma Integration
```bash
# Figma Integration
export FIGMA_ACCESS_TOKEN="figd_xxxxxxxxxxxxxxxx"
# Add new Figma-related variables here
```

#### 2. GitHub Integration
```bash
# GitHub Integration
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxx"
export GITHUB_REPOSITORIES="repo1,repo2"
# Add new GitHub-related variables here
```

#### 3. Jira Integration
```bash
# Jira Integration (using sooperset/mcp-atlassian)
export JIRA_URL="https://company.atlassian.net"
export JIRA_USERNAME="<EMAIL>"
export JIRA_API_TOKEN="ATATT3xFfGF0xxxxxxxxxxxxxxxx"
export READ_ONLY_MODE="true"
export ENABLED_TOOLS="jira_get_issue,jira_search"
# Add new Jira-related variables here
```

#### 4. Lark/Feishu Integration
```bash
# Lark/Feishu Integration
export LARK_APP_ID="cli_xxxxxxxxxxxxxxxx"
export LARK_APP_SECRET="xxxxxxxxxxxxxxxx"
export LARK_BASE_URL="https://open.feishu.cn"
# Add new Lark-related variables here
```

#### 5. React Application Variables
```bash
# React Application Configuration
export REACT_APP_API_BASE_URL="https://api.example.com"
export REACT_APP_ENVIRONMENT="production"
export REACT_APP_VERSION="1.0.0"
# Add new React app variables here
```

#### 6. Database Configuration
```bash
# Database Configuration
export DATABASE_URL="postgresql://user:pass@localhost:5432/db"
export DB_HOST="localhost"
export DB_PORT="5432"
# Add new database-related variables here
```

#### 7. Authentication & Security
```bash
# Authentication & Security
export JWT_SECRET="your_jwt_secret_here"
export ENCRYPTION_KEY="your_encryption_key_here"
export SESSION_SECRET="your_session_secret_here"
# Add new auth/security variables here
```

#### 8. Build & Deployment
```bash
# Build & Deployment Configuration
export NODE_ENV="production"
export BUILD_PATH="./dist"
export PORT="3000"
export GENERATE_SOURCEMAP="false"
# Add new build-related variables here
```

#### 9. Third-Party Services
```bash
# Third-Party Services
export STRIPE_SECRET_KEY="sk_live_xxxxxxxxxxxxxxxx"
export SENDGRID_API_KEY="SG.xxxxxxxxxxxxxxxx"
export AWS_ACCESS_KEY_ID="AKIAXXXXXXXXXXXXXXXX"
# Add new third-party service variables here
```

#### 10. Others
```bash
# Others
export CUSTOM_VARIABLE="value"
export MISC_CONFIGURATION="setting"
# Add variables that don't fit other categories here
```

### Category Assignment Rules

When adding new environment variables:

1. **Check existing categories first**: Place under the most relevant existing category
2. **Use "Others" as fallback**: If no category fits, place under "Others" section
3. **Maintain alphabetical order**: Within each category, sort variables alphabetically
4. **Keep related variables together**: Group related configuration variables in the same category

## 🔄 Environment Variables Sync Management

::alert{type="warning"}
**Mandatory**: When adding new environment variables to any .env file, they must be synchronized to the corresponding .env.example file with appropriate value sanitization
::

### Synchronization Rules

#### 1. File Pattern Matching

Environment variable changes should be synchronized between these file pairs:

```
.env ↔ .env.example
.mcp_env ↔ .mcp_env.example
.env.local ↔ .env.local.example
.env.production ↔ .env.production.example
.env.development ↔ .env.development.example
```

#### 2. Value Sanitization for Sensitive Data

When copying environment variables to `.example` files, sensitive values must be replaced with appropriate placeholders:

### ✅ Correct Value Sanitization

#### API Tokens and Keys
```bash
# .env (actual values)
export FIGMA_ACCESS_TOKEN="*********************************************"
export GITHUB_PERSONAL_ACCESS_TOKEN="****************************************"
export JIRA_API_TOKEN="ATATT3xFfGF0ZOIfn3bX0uCVa1fvd8RF9RVXnz6w..."

# .env.example (sanitized values)
export FIGMA_ACCESS_TOKEN="figd_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
export JIRA_API_TOKEN="ATATT3xFfGF0xxxxxxxxxxxxxxxxxxxxxxxx"
```

### 🔍 Sensitive Data Patterns

The following patterns should be considered sensitive and require sanitization:

#### Token Patterns
- API keys: `*_API_KEY`, `*_ACCESS_TOKEN`, `*_TOKEN`
- Secrets: `*_SECRET`, `*_PRIVATE_KEY`
- Authentication: `*_PASSWORD`, `*_PASS`, `*_AUTH_*`

#### Personal Information
- Email addresses containing real domains
- Real usernames or personal identifiers
- Phone numbers and addresses

#### Company-Specific Information
- Internal URLs and endpoints
- Database connection strings with real credentials
- Service-specific identifiers

### 📋 Non-Sensitive Data (Copy As-Is)

The following types can be copied directly without sanitization:

```bash
# Configuration flags and modes
export NODE_ENV="production"
export READ_ONLY_MODE="true"
export DEBUG_MODE="false"

# Public endpoints and URLs (general format)
export PUBLIC_API_URL="https://api.example.com"

# Feature flags and tool configurations
export ENABLED_TOOLS="jira_get_issue,jira_search"
export LOGGING_LEVEL="info"

# Non-sensitive service configurations
export PORT="3000"
export TIMEOUT="30000"
```

### 🛠️ Implementation Guidelines

#### When Adding New Environment Variables

1. **Determine prefix requirement**: Check if variable is used in React code
2. **Add to actual .env file first** with real values
3. **Choose appropriate category**: Place under existing category or "Others"
4. **Immediately add to .env.example** with sanitized values
5. **Add descriptive comments** explaining the purpose
6. **Follow naming conventions** from existing variables

### 🔄 Synchronization Checklist

When modifying environment variables, ensure:

- [ ] Correct REACT_APP_ prefix usage (if used in React code)
- [ ] Proper category placement
- [ ] Real values added to actual .env file
- [ ] Sanitized values added to .env.example file
- [ ] Comments explaining how to obtain values
- [ ] Consistent variable naming
- [ ] Proper grouping with related variables
- [ ] No sensitive data exposed in .example files
- [ ] Both files follow the same structure and order

### ❌ Common Mistakes to Avoid

```bash
# ❌ Wrong - Missing REACT_APP_ prefix for React variables
export API_BASE_URL="https://api.example.com" // Used in React components

# ❌ Wrong - Unnecessary REACT_APP_ prefix for build variables
export REACT_APP_JIRA_API_TOKEN="secret_token" // Only used in MCP server

# ❌ Wrong - Poor category organization
export GITHUB_TOKEN="token"
export REACT_APP_VERSION="1.0.0"
export JIRA_URL="https://jira.com"
export FIGMA_TOKEN="token"

# ✅ Correct - Proper prefix and organization
# GitHub Integration
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxx"

# React Application Configuration
export REACT_APP_API_BASE_URL="https://api.example.com"
export REACT_APP_VERSION="1.0.0"

# Jira Integration
export JIRA_URL="https://storehub.atlassian.net"

# Figma Integration
export FIGMA_ACCESS_TOKEN="figd_xxxxxxxxxxxxxxxx"
```

::alert{type="success"}
Following these rules ensures that environment variables are properly synchronized, correctly prefixed, and well-organized while maintaining security and clarity for all team members
::
