# For build
# NOTIFY_BFF_CACHE_HTML_API=http://scan.beep.test.shub.us/api/cache/beep-html-file
# For build: serve build directory (No current configuration required locally)
# PUBLIC_URL=https://d2rvjtzg4qar8t.cloudfront.net/test
NODE_ENV=development
# generate .map file or not
GENERATE_SOURCEMAP=false
# For build
# EXTEND_ESLINT=true

# For build: i18n
I18N_CLI_AWS_ACCESS_KEY_ID=
I18N_CLI_AWS_SECRET_ACCESS_KEY=
I18N_CLI_POEDITOR_TOKEN=

# For build: aws
AWS_S3_BUCKET=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# re-write the test environment variables
# login auth api url
REACT_APP_AUTH_API_URL=https://auth-api.test.shub.us/authorize
# sse api url
REACT_APP_SSE_API_URL=https://event-broker.test17.shub.us/sse/subscription
# scanner (This needs to be changed to local configuration, please ask mentor)
REACT_APP_QR_SCAN_DOMAINS=www.beep.local.shub.us
REACT_APP_STOREHUB_PAYMENT_RESPONSE_URL=https://%business%.beep.test.shub.us/payment/completed
REACT_APP_STOREHUB_PAYMENT_BACKEND_RESPONSE_URL=https://%business%.beep.test.shub.us/payment/webhook
# merchant store url (This needs to be changed to local configuration, please ask mentor)
REACT_APP_MERCHANT_STORE_URL=http://%business%.beep.local.shub.us:3000

# payment
REACT_APP_STOREHUB_PAYMENT_SCRIPT_SRC=https://demo2.2c2p.com/2C2PFrontEnd/SecurePayment/api/my2c2p.1.6.9.min.js
REACT_APP_PAYMENT_STRIPE_SG_KEY=
REACT_APP_PAYMENT_STRIPE_MY_KEY=

# reCAPTCHA
REACT_APP_GOOGLE_RECAPTCHA_SITE_KEY=
REACT_APP_RECAPTCHA_ENABLED=false

# Intercom
REACT_APP_INTERCOM_SCRIPT_URL=https://widget.intercom.io/widget/roi2dek9
REACT_APP_INTERCOM_APP_ID=roi2dek9
REACT_APP_INTERCOM_APP_BASE=https://api-iam.intercom.io

# GrowthBook
REACT_APP_GROWTHBOOK_ENABLED=false
REACT_APP_GROWTHBOOK_API_HOST=https://cdn.growthbook.io
REACT_APP_GROWTHBOOK_CLIENT_KEY=

# Beep download deep link
REACT_APP_BEEP_DOWNLOAD_DEEP_LINK=https://dl.beepit.com/kVmT

# Beep TNG deep link
REACT_APP_TNG_APP_DEEP_LINK_DOMAIN=tngdwallet://client/dl/mp
REACT_APP_TNG_MPID=
REACT_APP_TNG_DOWNLOAD_DEEP_LINK=https://onelink.tngd.my/8mmV/beepTNG

# monitor
REACT_APP_GOOGLE_TAG_MANAGER_ID=
# clevertap (This needs to be changed to local configuration, get from beep-v1-webapp of Apollo)
REACT_APP_CLEVER_TAP_ID=
REACT_APP_CLEVER_TAP_SCRIPT_HTTPS_URL=https://d2r1yp2w7bby2u.cloudfront.net/js/a.js
REACT_APP_CLEVER_TAP_SCRIPT_HTTP_URL=http://static.clevertap.com/js/a.js
REACT_APP_CLEVER_TAP_SCRIPT_REGION=sg1
# log service
REACT_APP_LOG_SERVICE_URL=https://fe-logservice.k8s.shub.us/logs/raw
REACT_APP_LOG_SERVICE_TOKEN=bdd0666f-a955-4de9-81ec-9aa7fb670554
# Must contain beep-web and the env name, should not contain spaces
REACT_APP_LOG_SERVICE_TAG=beep-web,local
# Sentry
REACT_APP_SENTRY_DSN=
SENTRY_ORG=
SENTRY_PROJECT=
SENTRY_AUTH_TOKEN=
# New Relic
REACT_APP_NEW_RELIC_ENABLED=false

# Others
# google maps (This needs to be changed to local configuration, get from beep-v1-webapp of Apollo)
REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyAA4ChLITkR7pIWrK38dLqmQH9EYaSjz7c
# Maintenance url for redirecting on api request
REACT_APP_MAINTENANCE_PAGE_URL=
# app name for meta
REACT_APP_META_NAME=Beep_Fat

# local development, replacing the proxy setting in package.json
PROXY=https://coffee.beep.test.shub.us
# The url that mock server will forward the request to, if there's no match in mock-server.js.
# MOCK_ORIGINAL_SERVER_PROXY=https://coffee.beep.test.shub.us
