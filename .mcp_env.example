# MCP Environment Variables Example
# Copy this file to .mcp_env and update with your actual credentials
# Run: cp .mcp_env.example .mcp_env

# Figma Integration
# Get token from: https://figma.com → Settings → Security → Personal access tokens
# Required scopes: file_dev_resources:read, file_content:read, file_metadata:read, library_assets:read, library_content:read, projects:read, team_library_content:read
export FIGMA_ACCESS_TOKEN="figd_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# GitHub Integration
# Get token from: https://github.com/settings/tokens
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
# Specify repositories to monitor (comma-separated format: owner/repo)
export GITHUB_REPOSITORIES="storehubnet/beep-v1-webapp,storehubnet/beep-mobile-app,your-org/your-repo"

# Jira Integration (using sooperset/mcp-atlassian)
# Get token from: https://id.atlassian.com/manage-profile/security/api-tokens
export JIRA_URL="https://storehub.atlassian.net"
export JIRA_USERNAME="<EMAIL>"
export JIRA_API_TOKEN="ATATT3xFfGF0xxxxxxxxxxxxxxxxxxxxxxxx"
# Filter by project keys (comma-separated, e.g., "WB,DEV,SUPPORT")
export JIRA_PROJECTS_FILTER="WB"
# Enable specific Jira tools (supports both read and write operations)
export ENABLED_TOOLS="jira_get_issue,jira_search,jira_create_issue,jira_update_issue,jira_transition_issue,jira_add_comment"

# Lark/Feishu Integration
# Get credentials from: https://open.feishu.cn (China) or https://open.larksuite.com (International)
export LARK_APP_ID="cli_xxxxxxxxxxxxxxxx"
export LARK_APP_SECRET="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
export LARK_BASE_URL="https://open.feishu.cn"

# Usage: Run 'source ./.mcp_env' to load these environment variables