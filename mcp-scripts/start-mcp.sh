#!/bin/bash

# =================================
# MCP Services Startup Script
# =================================
# This script loads environment variables and starts Cursor with MCP services
# Supports: Figma MCP, mcp-atlassian, GitHub MCP, Lark MCP

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Get script directory and project directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"

print_status "Starting MCP Services for beep-v1-webapp..."
echo "Project Directory: $PROJECT_DIR"
echo

# Step 1: Check prerequisites
print_step "1. Checking prerequisites..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker Desktop first."
    exit 1
fi
print_status "✓ Docker is running"

# Check if Cursor is installed
if ! command -v cursor >/dev/null 2>&1; then
    print_error "Cursor is not installed or not in PATH."
    exit 1
fi
print_status "✓ Cursor is available"

# Check if npm/npx is available
if ! command -v npx >/dev/null 2>&1; then
    print_error "npm/npx is not installed. Please install Node.js first."
    exit 1
fi
print_status "✓ Node.js/npm is available"

# Step 2: Load environment variables
print_step "2. Loading environment variables..."

if [[ -f "$PROJECT_DIR/.mcp_env" ]]; then
    source "$PROJECT_DIR/.mcp_env"
    print_status "✓ Environment variables loaded from .mcp_env"
else
    print_error ".mcp_env file not found!"
    exit 1
fi

# Step 3: Verify required environment variables
print_step "3. Verifying environment variables..."

check_env_var() {
    local var_name=$1
    local var_value=${!var_name}
    if [[ -z "$var_value" ]]; then
        print_error "Environment variable $var_name is not set!"
        return 1
    else
        print_status "✓ $var_name is set"
        return 0
    fi
}

# Check required variables
check_env_var "FIGMA_ACCESS_TOKEN" || exit 1
check_env_var "JIRA_URL" || exit 1
check_env_var "JIRA_USERNAME" || exit 1
check_env_var "JIRA_API_TOKEN" || exit 1
check_env_var "JIRA_PROJECTS_FILTER" || exit 1
check_env_var "ENABLED_TOOLS" || exit 1

# Step 4: Test Docker image availability
print_step "4. Checking mcp-atlassian Docker image..."

if docker image inspect ghcr.io/sooperset/mcp-atlassian:latest >/dev/null 2>&1; then
    print_status "✓ mcp-atlassian Docker image is available"
else
    print_warning "mcp-atlassian Docker image not found locally. Pulling..."
    if docker pull ghcr.io/sooperset/mcp-atlassian:latest; then
        print_status "✓ Successfully pulled mcp-atlassian Docker image"
    else
        print_error "Failed to pull mcp-atlassian Docker image"
        exit 1
    fi
fi

# Step 5: Test MCP services (optional quick test)
print_step "5. Quick MCP services test..."

# Test Figma MCP availability
if command -v figma-developer-mcp >/dev/null 2>&1; then
    print_status "✓ Figma MCP is globally available"
else
    print_warning "Figma MCP not globally installed, will use npx"
fi

# Test mcp-atlassian connection
print_status "Testing mcp-atlassian connectivity..."
if timeout 10s docker run --rm \
    -e JIRA_URL="$JIRA_URL" \
    -e JIRA_USERNAME="$JIRA_USERNAME" \
    -e JIRA_API_TOKEN="$JIRA_API_TOKEN" \
    -e JIRA_PROJECTS_FILTER="$JIRA_PROJECTS_FILTER" \
    -e ENABLED_TOOLS="jira_get_issue" \
    ghcr.io/sooperset/mcp-atlassian:latest \
    echo '{"method": "tools/list", "params": {}}' 2>/dev/null | grep -q "jira_get_issue"; then
    print_status "✓ mcp-atlassian connection test passed"
else
    print_warning "mcp-atlassian connection test failed (may be network/auth issue)"
fi

# Step 6: Check MCP configuration file
print_step "6. Verifying MCP configuration..."

if [[ -f "$PROJECT_DIR/.cursor/mcp.json" ]]; then
    print_status "✓ MCP configuration file exists"

    # Validate JSON syntax
    if python3 -m json.tool "$PROJECT_DIR/.cursor/mcp.json" >/dev/null 2>&1; then
        print_status "✓ MCP configuration JSON is valid"
    else
        print_error "MCP configuration JSON is invalid!"
        exit 1
    fi
else
    print_error ".cursor/mcp.json configuration file not found!"
    exit 1
fi

# Step 7: Start Cursor with MCP
print_step "7. Starting Cursor with MCP services..."

print_status "Active MCP Services:"
echo "  • Figma MCP (figma-developer-mcp)"
echo "  • mcp-atlassian (Docker: ghcr.io/sooperset/mcp-atlassian:latest)"
echo "  • GitHub MCP (@modelcontextprotocol/server-github)"
echo "  • Lark MCP (lark-mcp)"
echo

print_status "Starting Cursor..."
print_warning "Note: MCP services will be initialized when Cursor starts"
print_warning "Check Cursor's output panel for MCP connection status"

# Change to project directory and start Cursor
cd "$PROJECT_DIR"
cursor .

print_status "Cursor started successfully!"
echo
print_status "=== MCP Startup Complete ==="
print_status "Your Cursor session now has access to:"
echo "  ✓ Figma designs via Figma MCP"
echo "  ✓ Jira tickets (WB project) via mcp-atlassian"
echo "  ✓ GitHub repositories via GitHub MCP"
echo "  ✓ Lark/Feishu integration via Lark MCP"
echo
print_status "Happy coding! 🚀"