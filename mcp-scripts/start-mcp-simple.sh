#!/bin/bash

# Simple MCP Startup - Just load env and start Cursor
# Cursor will handle the rest using .cursor/mcp.json

# Load environment variables
if [[ -f "../.mcp_env" ]]; then
    source ../.mcp_env
    echo "✓ Environment loaded from .mcp_env"
else
    echo "❌ .mcp_env not found!"
    exit 1
fi

# Export variables for Cursor
export FIGMA_ACCESS_TOKEN JIRA_URL JIRA_USERNAME JIRA_API_TOKEN
export JIRA_PROJECTS_FILTER ENABLED_TOOLS GITHUB_PERSONAL_ACCESS_TOKEN
export GITHUB_REPOSITORIES LARK_APP_ID LARK_APP_SECRET LARK_BASE_URL

echo "🚀 Starting Cursor with MCP services..."
echo "   Services will be loaded from .cursor/mcp.json"

cd .. && cursor .

echo "✅ Done!"