@echo off
REM =================================
REM MCP Services Startup (Windows)
REM =================================

echo Starting MCP Services for beep-v1-webapp...
echo.

REM Check if .mcp_env exists in parent directory
cd ..
if not exist ".mcp_env" (
    echo ERROR: .mcp_env file not found!
    pause
    exit /b 1
)

REM Load environment variables (Windows doesn't support source, so we'll use a workaround)
echo Loading environment variables...

REM Create a temporary batch file to set environment variables
(
echo @echo off
for /f "tokens=1,2 delims==" %%a in ('type .mcp_env ^| findstr /v "^#" ^| findstr /v "^$"') do (
    set "line=%%a=%%b"
    setlocal enabledelayedexpansion
    set "line=!line:export =!"
    echo set "!line!"
    endlocal
)
) > temp_env.bat

call temp_env.bat
del temp_env.bat

echo Environment variables loaded.
echo.

REM Check Docker
echo Checking Docker...
docker info >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)
echo Docker is running.
echo.

REM Check Cursor
echo Checking Cursor...
where cursor >nul 2>&1
if errorlevel 1 (
    echo ERROR: Cursor is not installed or not in PATH.
    pause
    exit /b 1
)
echo Cursor is available.
echo.

echo Starting Cursor with MCP services...
echo Active services:
echo   - Figma MCP
echo   - mcp-atlassian (Jira)
echo   - GitHub MCP
echo   - Lark MCP
echo.

cursor .

echo.
echo MCP startup complete!
pause