#!/bin/bash
set -e

# NOTE: This script should be run from the project root, e.g., bash scripts/setup-and-run.sh
# beep-v1-webapp one-click setup & run script
# For macOS (darwin)

NODE_VERSION="18.19.0"
NVM_DIR="$HOME/.nvm"
BUSINESS_DOMAINS=(jw coffee hcbeep)

cat <<EOF
=========================================
 beep-v1-webapp One-Click Setup & Run
=========================================
This script will automatically set up your local development environment and let you choose a business domain to start.
If prompted for sudo password or interactive input, please follow the instructions.
=========================================
EOF

# All file paths are relative to the project root
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
cd "$PROJECT_ROOT"

# 1. Check and install nvm
if ! command -v nvm >/dev/null 2>&1; then
  echo "nvm is not installed. Installing nvm..."
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
fi
export NVM_DIR="$NVM_DIR"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# 2. Check and install Node.js
if ! nvm ls | grep -q "v$NODE_VERSION"; then
  echo "Installing Node.js $NODE_VERSION..."
  nvm install $NODE_VERSION
fi
nvm use $NODE_VERSION

# 3. Check and install yarn
echo "Checking yarn..."
if ! command -v yarn >/dev/null 2>&1; then
  echo "yarn is not installed. Installing yarn..."
  npm install -g yarn
fi

# 4. Configure private npm registry
echo "Configuring StoreHub private npm registry..."
npm config set registry https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/
npm config set ca ""
if ! command -v npm-cli-login >/dev/null 2>&1; then
  echo "Installing npm-cli-login..."
  npm install -g npm-cli-login
fi
npm-cli-login -u storehub -e <EMAIL> -p "a[RF8zZy" -r https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/

# 5. Generate .env file (prompt to overwrite if exists)
if [ -f .env ]; then
  read -p ".env already exists. Do you want to regenerate it? (y/N): " regen
  if [[ "$regen" =~ ^[Yy]$ ]]; then
    yarn pre:env subenv=17
  else
    echo "Skipping .env generation."
  fi
else
  yarn pre:env subenv=17
fi

# 6. Install dependencies
echo "Installing dependencies..."
yarn

# 7. Check/complete environment variables
if [ ! -f .env ]; then
  echo "[ERROR] .env file not found. Please run 'yarn pre:env' again."
  exit 1
fi
echo "If any variables are missing, please refer to .env.example and add them manually."

# 8. Configure hosts
echo "Configuring local hosts for all business domains..."
HOSTS_FILE="/etc/hosts"
for business in "${BUSINESS_DOMAINS[@]}"; do
  DOMAIN_LINE="127.0.0.1 $business.beep.local.shub.us"
  if ! grep -q "$business.beep.local.shub.us" $HOSTS_FILE; then
    echo "Adding $DOMAIN_LINE to $HOSTS_FILE (sudo required)"
    echo "$DOMAIN_LINE" | sudo tee -a $HOSTS_FILE
  fi
done
if ! grep -q "www.beep.local.shub.us" $HOSTS_FILE; then
  echo "127.0.0.1 www.beep.local.shub.us" | sudo tee -a $HOSTS_FILE
fi

# 9. Ensure commit-msg hook is executable
if [ -f .husky/commit-msg ]; then
  chmod +x .husky/commit-msg
fi

# 10. Self-check diagnostics
echo "Node version: $(node -v)"
echo "Yarn version: $(yarn -v)"
echo "Checking .env..."
if [ ! -f .env ]; then
  echo "[ERROR] .env is missing!"
else
  echo ".env is present."
fi
echo "Checking hosts..."
for business in "${BUSINESS_DOMAINS[@]}"; do
  if grep -q "$business.beep.local.shub.us" /etc/hosts; then
    echo "$business.beep.local.shub.us OK"
  else
    echo "[ERROR] $business.beep.local.shub.us is missing in /etc/hosts"
  fi
done
if grep -q "www.beep.local.shub.us" /etc/hosts; then
  echo "www.beep.local.shub.us OK"
else
  echo "[ERROR] www.beep.local.shub.us is missing in /etc/hosts"
fi

echo "========================================="
echo "All setup steps are complete!"
echo "========================================="

# 11. Business domain selection and start
echo "Please select the business domain to start:"
select business in "${BUSINESS_DOMAINS[@]}"; do
  if [[ " ${BUSINESS_DOMAINS[@]} " =~ " $business " ]]; then
    echo "You selected: $business.beep.local.shub.us"
    break
  else
    echo "Invalid selection, please try again."
  fi
done

echo "Starting yarn start ..."
yarn start &
START_PID=$!
sleep 3
URL="http://$business.beep.local.shub.us:3000"
echo "-----------------------------------------"
echo "The project is starting..."
echo "Please wait a moment, then open the following URL in your browser:"
echo "  $URL"
echo "(The browser will open automatically. You can copy the address above if needed.)"
echo "To stop the service, run: kill $START_PID"
echo "-----------------------------------------"
sleep 10
open "$URL"
wait $START_PID