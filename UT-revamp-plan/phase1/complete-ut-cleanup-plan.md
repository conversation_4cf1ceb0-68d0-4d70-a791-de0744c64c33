# 完整单元测试(UT)清理计划

根据我们的讨论，以下是更新后的完整单元测试清理建议，包括UI测试相关内容。

## 文件清单概览

详细的文件清单请参考 `file-lists/` 文件夹中的以下文件：

- **`file-lists/files-to-delete.md`** - 需要删除的文件详细清单
- **`file-lists/files-to-keep.md`** - 需要保留的文件详细清单
- **`file-lists/files-to-evaluate.md`** - 需要评估的文件详细清单

## 清理策略概要

### 删除的文件类型
1. **Redux相关测试文件** - 所有Redux状态管理相关的测试
2. **UI组件测试文件** - 所有React组件和容器的测试
3. **JSX测试文件** - 所有.test.jsx和.spec.jsx文件
4. **快照文件夹** - 所有__snapshots__目录
5. **Mock数据和Fixtures** - 测试用的模拟数据文件夹
6. **测试辅助工具文件** - 专门为React/Redux测试设计的辅助文件

### 保留的文件类型
1. **独立工具函数测试** - 不依赖React/Redux的纯函数测试
2. **业务逻辑测试** - 独立的业务逻辑函数测试（需要评估）
3. **基础配置文件** - 简化后的测试配置文件

## 执行清理

### 自动化清理脚本

使用提供的自动化脚本执行清理：

```bash
# 给脚本执行权限
chmod +x cleanup-script.sh

# 执行清理
./cleanup-script.sh
```

### 手动清理命令

如果需要手动执行，详细的删除命令请参考：
- **`file-lists/files-to-delete.md`** - 包含完整的删除命令列表
- **`cleanup-script.sh`** - 可执行的自动化清理脚本

## 更新测试配置

详细的配置更新指南请参考：
- **`config-update-guide.md`** - 包含完整的配置更新说明和代码示例

### 主要配置更新
1. **jest-config-overrides.js** - 更新测试覆盖率配置
2. **src/setupTests.js** - 简化测试环境设置
3. **package.json** - 清理不必要的测试依赖

## 验证清理后的测试

### 快速验证
执行以下命令验证剩余测试是否正常运行：

```bash
yarn test
```

### 完整验证和调试
详细的测试验证和调试流程请参考：
- **`test-validation-plan.md`** - 完整的测试验证和调试计划

### 验证步骤概要
1. **建立基线** - 记录清理前的测试状态
2. **执行清理** - 按计划删除测试文件
3. **初步验证** - 快速检查剩余测试是否能运行
4. **深度调试** - 逐个验证测试文件，修复发现的问题
5. **配置验证** - 确保Jest配置和setupTests.js正确更新
6. **最终验证** - 运行完整测试套件并生成报告

如果有失败的测试，按照验证计划中的故障排除指南进行调试和修复。

## 总结

1. **删除**：
   - 所有Redux相关测试文件
   - 所有UI组件测试文件
   - 所有JSX测试文件
   - 所有快照文件夹
   - 所有mock数据和fixtures文件夹
   - 所有测试辅助工具文件

2. **保留**：
   - 独立的工具函数测试（不依赖Redux和UI组件的）
   - 配置和常量相关测试（如果独立）

3. **更新**：
   - 测试配置文件
   - 测试设置文件

通过这种方式，我们可以彻底清理所有过时的测试文件，只保留那些真正独立且有价值的工具函数测试，为未来的测试策略奠定基础。
