# 测试配置更新指南

## 1. jest-config-overrides.js 更新

### 当前配置
```javascript
const collectCoverageFrom = [
  // Freedom area
  '!src/**/__fixtures__/**/*',

  // Root
  // << do not collection root folder's coverage for now >>
  'src/config.js',

  // UI Components
  // << do not collect UI coverage for now >>

  // Utils
  'src/utils/**/*.js',
  '!src/utils/url.js',
  '!src/utils/propTypes.js',

  // Redux
  'src/redux/**/*.js',
  'src/{cashback,ordering,stores}/redux/**/*.js',
];
```

### 建议的新配置
```javascript
const collectCoverageFrom = [
  // Freedom area
  '!src/**/__fixtures__/**/*',
  '!src/**/__mocks__/**/*',
  '!src/**/__snapshots__/**/*',

  // Root
  'src/config.js',

  // Utils - 只保留工具函数的覆盖率收集
  'src/utils/**/*.js',
  '!src/utils/url.js',
  '!src/utils/propTypes.js',
  '!src/utils/testHelper.js',
  '!src/utils/test-utils.js',
  '!src/utils/redux-test-utils.js',
  '!src/utils/mock-store.js',
  '!src/utils/test-fixtures.js',

  // 移除Redux相关覆盖率收集
  '!src/redux/**/*.js',
  '!src/{cashback,ordering,stores,rewards,voucher,site}/redux/**/*.js',
  
  // 移除组件相关覆盖率收集
  '!src/components/**/*.js',
  '!src/containers/**/*.js',
  '!src/pages/**/*.js',
  '!src/common/components/**/*.js',
  '!src/*/components/**/*.js',
  '!src/*/containers/**/*.js',
];
```

## 2. src/setupTests.js 更新

### 当前配置
```javascript
import { TextEncoder, TextDecoder } from 'util';
import { configure } from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';
import '@testing-library/jest-dom/extend-expect';
require('jest-fetch-mock').enableMocks();

global.fetch = require('jest-fetch-mock');
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

if (!AbortSignal.prototype.throwIfAborted) {
  AbortSignal.prototype.throwIfAborted = function() {
    if (this.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
  };
}

configure({ adapter: new Adapter() });
```

### 建议的新配置
```javascript
import { TextEncoder, TextDecoder } from 'util';

// 只保留基本的全局设置
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

if (!AbortSignal.prototype.throwIfAborted) {
  AbortSignal.prototype.throwIfAborted = function() {
    if (this.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
  };
}

// 移除以下内容：
// - enzyme相关配置
// - @testing-library/jest-dom
// - jest-fetch-mock
// - 其他React/Redux测试相关设置
```

## 3. package.json 依赖清理

### 可能需要移除的依赖
```json
{
  "devDependencies": {
    "enzyme": "^3.x.x",
    "enzyme-adapter-react-16": "^1.x.x",
    "enzyme-adapter-utils": "^1.x.x",
    "enzyme-shallow-equal": "^1.x.x",
    "@testing-library/jest-dom": "^5.x.x",
    "@testing-library/react": "^12.x.x",
    "@testing-library/user-event": "^14.x.x",
    "jest-fetch-mock": "^3.x.x",
    "redux-mock-store": "^1.x.x"
  }
}
```

### 保留的测试相关依赖
```json
{
  "devDependencies": {
    "@testing-library/jest-dom": "^5.x.x", // 如果工具函数测试需要
    "jest": "^27.x.x", // 核心测试框架
    "@craco/craco": "^6.x.x" // 如果使用craco配置
  }
}
```

## 4. craco.config.js 更新

### 当前Jest配置部分
```javascript
jest: {
  configure: jestConfig => {
    jestConfig.moduleNameMapping = {
      '\\.(css|scss)$': 'identity-obj-proxy',
      '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
    };
    jestConfig.transform = {
      '^.+\\.[t|j]sx?$': 'babel-jest',
    };
    jestConfig.setupFilesAfterEnv = [
      '<rootDir>/src/setupTests.js',
    ];
    // ...其他配置
  }
}
```

### 建议的新配置
```javascript
jest: {
  configure: jestConfig => {
    // 简化模块映射，只保留必要的
    jestConfig.moduleNameMapping = {
      '\\.(css|scss)$': 'identity-obj-proxy',
      '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
    };
    jestConfig.transform = {
      '^.+\\.[t|j]sx?$': 'babel-jest',
    };
    jestConfig.setupFilesAfterEnv = [
      '<rootDir>/src/setupTests.js',
    ];
    // 移除React/Redux相关的测试环境配置
    return jestConfig;
  }
}
```

## 5. 更新步骤

1. **备份当前配置**
   ```bash
   cp jest-config-overrides.js jest-config-overrides.js.backup
   cp src/setupTests.js src/setupTests.js.backup
   cp package.json package.json.backup
   ```

2. **执行清理脚本**
   ```bash
   chmod +x UT-revamp-plan/cleanup-script.sh
   ./UT-revamp-plan/cleanup-script.sh
   ```

3. **手动更新配置文件**
   - 更新 jest-config-overrides.js
   - 更新 src/setupTests.js
   - 检查并更新 craco.config.js

4. **清理依赖**
   ```bash
   npm uninstall enzyme enzyme-adapter-react-16 enzyme-adapter-utils enzyme-shallow-equal
   npm uninstall @testing-library/react @testing-library/user-event
   npm uninstall jest-fetch-mock redux-mock-store
   ```

5. **验证配置**
   ```bash
   yarn test
   yarn test:coverage
   ```

## 6. 验证清单

- [ ] 所有Redux测试文件已删除
- [ ] 所有UI组件测试文件已删除
- [ ] 测试辅助工具文件已删除
- [ ] jest-config-overrides.js 已更新
- [ ] src/setupTests.js 已简化
- [ ] 不必要的依赖已移除
- [ ] 剩余测试可以正常运行
- [ ] 测试覆盖率报告正常生成
