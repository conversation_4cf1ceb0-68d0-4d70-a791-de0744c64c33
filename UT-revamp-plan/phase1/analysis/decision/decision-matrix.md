# 测试文件决策矩阵

| 标准 | 权重 | 说明 |
|------|------|------|
| 独立性 | 高 | 不依赖Redux/React/UI框架 |
| 测试对象 | 高 | 测试纯函数或独立业务逻辑 |
| 测试质量 | 中 | 断言充分、覆盖率合理 |
| 维护成本 | 中 | 依赖简单、易于理解 |

## 评分系统
每个文件按以下标准评分：
- 0分：不符合标准
- 1分：部分符合标准
- 2分：完全符合标准

## 决策规则
- 总分 ≥ 6分：保留
- 总分 ≤ 3分：删除
- 总分 4-5分：需要进一步评估

## 评分示例

### 示例1：典型的工具函数测试
```javascript
// src/utils/datetime-lib.test.js
import { formatDate, parseDate } from '../utils/datetime-lib';

describe('datetime-lib', () => {
  it('should format date correctly', () => {
    const date = new Date(2023, 0, 1);
    expect(formatDate(date, 'YYYY-MM-DD')).toBe('2023-01-01');
  });
  
  it('should parse date correctly', () => {
    const dateStr = '2023-01-01';
    const date = parseDate(dateStr, 'YYYY-MM-DD');
    expect(date.getFullYear()).toBe(2023);
    expect(date.getMonth()).toBe(0);
    expect(date.getDate()).toBe(1);
  });
});
```

**评分**:
- 独立性: 2分 (完全独立，无外部依赖)
- 测试对象: 2分 (测试纯函数)
- 测试质量: 2分 (断言充分，覆盖率高)
- 维护成本: 2分 (依赖简单，易于理解)
- **总分**: 8分 → **决策**: 保留

### 示例2：Redux相关测试
```javascript
// src/redux/actions/user.test.js
import configureMockStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { fetchUser } from './user';
import * as types from '../constants/actionTypes';

const middlewares = [thunk];
const mockStore = configureMockStore(middlewares);

describe('user actions', () => {
  it('creates FETCH_USER_SUCCESS when fetching user has been done', () => {
    // ...mock API calls
    const expectedActions = [
      { type: types.FETCH_USER_REQUEST },
      { type: types.FETCH_USER_SUCCESS, payload: { name: 'John' } }
    ];
    const store = mockStore({ user: {} });
    
    return store.dispatch(fetchUser()).then(() => {
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
```

**评分**:
- 独立性: 0分 (依赖Redux)
- 测试对象: 0分 (测试Redux action)
- 测试质量: 1分 (有断言，但覆盖不全面)
- 维护成本: 0分 (依赖复杂，难以维护)
- **总分**: 1分 → **决策**: 删除

### 示例3：边缘情况
```javascript
// src/ordering/containers/payments/utils/utils.test.js
import { calculateTotal, formatCurrency } from './utils';
import { getDefaultState } from '../../../redux/reducers/payment';

describe('payment utils', () => {
  it('should calculate total correctly', () => {
    const items = [
      { price: 10, quantity: 2 },
      { price: 15, quantity: 1 }
    ];
    expect(calculateTotal(items)).toBe(35);
  });
  
  it('should format currency correctly', () => {
    expect(formatCurrency(1000)).toBe('$1,000.00');
  });
  
  it('should work with default state', () => {
    const state = getDefaultState();
    const items = state.items;
    expect(calculateTotal(items)).toBe(0);
  });
});
```

**评分**:
- 独立性: 1分 (部分依赖Redux)
- 测试对象: 1分 (混合测试)
- 测试质量: 2分 (断言充分)
- 维护成本: 1分 (部分依赖复杂)
- **总分**: 5分 → **决策**: 需要进一步评估