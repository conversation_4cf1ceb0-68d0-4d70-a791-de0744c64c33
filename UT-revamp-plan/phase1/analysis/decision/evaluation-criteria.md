# 测试文件评估标准

## 保留标准
测试文件满足以下所有条件时应该保留：

1. **独立性**
   - 不依赖Redux状态管理
   - 不依赖React组件
   - 不依赖UI测试框架（如Enzyme、React Testing Library）

2. **测试对象**
   - 测试纯函数或工具函数
   - 测试独立的业务逻辑（不涉及UI或状态管理）

3. **测试质量**
   - 有足够的断言（平均每个测试块至少1个断言）
   - 覆盖率合理（至少70%）
   - 没有过多的跳过测试（跳过测试不超过20%）

4. **维护成本**
   - 依赖简单，主要依赖Jest
   - Mock使用合理，不过度mock
   - 测试逻辑清晰，易于理解

## 删除标准
测试文件满足以下任一条件时应该删除：

1. **依赖复杂**
   - 依赖Redux状态管理
   - 依赖React组件渲染
   - 使用Enzyme或React Testing Library

2. **测试类型不适合**
   - 快照测试
   - UI交互测试
   - 组件状态变化测试

3. **测试质量问题**
   - 没有有效断言
   - 全部或大部分测试被跳过
   - 测试逻辑混乱，难以维护

## 需要评估的边缘情况
以下情况需要进一步评估：

1. **混合测试文件**
   - 同时包含工具函数测试和UI/Redux测试
   - 可能需要拆分或重构

2. **业务逻辑测试**
   - 测试业务逻辑但有少量UI/Redux依赖
   - 可能需要重构为纯函数测试

3. **特殊用途测试**
   - 配置文件测试
   - 常量测试
   - 特定环境测试