# 测试文件评估表格

| 文件路径 | 独立性(0-2) | 测试对象(0-2) | 测试质量(0-2) | 维护成本(0-2) | 总分 | 决策 | 备注 |
|---------|------------|-------------|-------------|------------|------|------|------|
| src/utils/datetime-lib.test.js | 2 | 2 | 2 | 2 | 8 | 保留 | 纯工具函数测试 |
| src/utils/time-lib.test.js | 2 | 2 | 2 | 2 | 8 | 保留 | 纯工具函数测试 |
| src/utils/form-validate.test.js | 2 | 2 | 2 | 2 | 8 | 保留 | 纯工具函数测试 |
| src/utils/request.test.js | 2 | 2 | 1 | 1 | 6 | 保留 | 有网络请求mock |
| src/utils/monitoring/logger.test.js | 2 | 2 | 2 | 1 | 7 | 保留 | 有console mock |
| src/utils/monitoring/utils.test.js | 2 | 2 | 2 | 2 | 8 | 保留 | 纯工具函数测试 |
| src/common/utils/tests/onceUserAgent.test.js | 2 | 2 | 2 | 2 | 8 | 保留 | 已在新位置 |
| src/common/utils/tests/utils.test.js | 2 | 2 | 2 | 2 | 8 | 保留 | 已在新位置 |
| src/ordering/containers/payments/utils/utils.test.js | 1 | 1 | 2 | 1 | 5 | 评估 | 部分依赖Redux |
| src/e-invoice/utils/tests/index.test.js | 1 | 2 | 1 | 1 | 5 | 评估 | 业务逻辑测试 |