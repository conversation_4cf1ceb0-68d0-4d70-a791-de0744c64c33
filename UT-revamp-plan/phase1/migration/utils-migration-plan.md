# 工具函数迁移计划：从 `src/utils/` 到 `src/common/utils/`

## 概述

本计划旨在将项目中的工具函数从旧版 `src/utils/` 目录完全迁移到新版 `src/common/utils/` 目录，同时保持项目功能稳定并与单元测试清理计划保持一致。

## 1. 迁移步骤和阶段划分

### 阶段一：准备和分析（1-2周）

1. **代码分析**
   - 分析 `src/utils/` 中所有文件的功能和依赖关系
   - 识别已经在 `src/common/utils/` 中有对应实现的函数
   - 识别仍需迁移的独特功能

2. **依赖图构建**
   - 构建工具函数的依赖关系图
   - 识别项目中使用这些工具函数的所有位置

3. **测试覆盖率分析**
   - 分析现有测试对工具函数的覆盖情况
   - 确定需要新增测试的函数

### 阶段二：核心功能迁移（2-3周）

1. **基础工具函数迁移**
   - 迁移独立性强、依赖少的工具函数
   - 为新迁移的函数编写测试

2. **更新导入路径**
   - 在 `src/utils/utils.js` 中添加从新位置重新导出的函数
   - 保持向后兼容性

3. **验证和测试**
   - 运行测试确保功能正常
   - 进行手动验证

### 阶段三：复杂功能迁移（2-3周）

1. **复杂工具函数迁移**
   - 迁移具有复杂依赖关系的工具函数
   - 重构代码以适应新的架构

2. **更新导入路径**
   - 继续更新 `src/utils/utils.js` 中的重新导出
   - 开始直接修改部分使用这些函数的代码，改为从新位置导入

3. **验证和测试**
   - 运行测试确保功能正常
   - 进行更广泛的手动验证

### 阶段四：完全迁移和清理（1-2周）

1. **完成所有函数迁移**
   - 确保所有功能都已迁移到新位置
   - 所有测试都已更新并通过

2. **导入路径更新**
   - 更新所有直接从 `src/utils/` 导入的代码，改为从 `src/common/utils/` 导入
   - 使用代码搜索和替换工具批量更新

3. **弃用旧目录**
   - 在 `src/utils/` 中的文件添加弃用警告
   - 保留 `src/utils/utils.js` 作为兼容层

### 阶段五：最终清理（待定）

1. **移除兼容层**
   - 当确认所有代码都已更新后，移除 `src/utils/` 目录
   - 或保留最小化的兼容层，仅重新导出常用函数

## 2. 需要迁移的文件清单和优先级

### 高优先级（先迁移）

1. `src/utils/datetime-lib.js` - 日期时间处理函数
2. `src/utils/time-lib.js` - 时间处理函数
3. `src/utils/form-validate.js` - 表单验证函数
4. `src/utils/request.js` - 网络请求函数
5. `src/utils/monitoring/logger.js` - 日志记录函数
6. `src/utils/monitoring/utils.js` - 监控工具函数

### 中优先级

7. `src/utils/store-utils.js` - 存储相关工具函数
8. `src/utils/constants.js` - 常量定义（需要合并到 `src/common/utils/constants.js`）
9. `src/utils/url.js` - URL处理函数
10. 其他独立工具函数文件

### 低优先级（最后迁移）

11. `src/utils/testHelper.js` - 测试辅助函数（根据测试清理计划可能会删除）
12. `src/utils/utils.js` - 主工具文件（作为兼容层保留到最后）

## 3. 依赖关系和向后兼容性处理

### 依赖关系处理

1. **内部依赖**
   - 识别工具函数之间的依赖关系
   - 按依赖顺序迁移，从最基础的函数开始
   - 更新迁移后函数的导入路径

2. **外部依赖**
   - 识别依赖第三方库的函数
   - 确保 `src/common/utils/` 中已安装相同的依赖
   - 考虑减少不必要的外部依赖

### 向后兼容性处理

1. **兼容层策略**
   - 保留 `src/utils/utils.js` 作为兼容层
   - 在迁移每个函数后，更新兼容层以从新位置导入并重新导出

2. **渐进式更新**
   - 在迁移过程中，不立即删除原始文件
   - 在确认所有使用该函数的代码都已更新后，才考虑删除

3. **弃用警告**
   - 在旧文件中添加弃用警告注释
   - 考虑添加运行时警告（在非生产环境）

## 4. 迁移过程中的测试策略

### 与单元测试清理计划结合

1. **保留工具函数测试**
   - 根据之前的单元测试清理计划，保留所有独立工具函数测试
   - 将这些测试也迁移到与函数相对应的新位置

2. **测试迁移步骤**
   - 为每个迁移的函数创建或更新测试
   - 确保测试覆盖率不降低
   - 将测试文件从 `src/utils/*.test.js` 迁移到 `src/common/utils/tests/*.test.js`

3. **测试策略**
   - 先编写测试，再迁移函数（测试驱动开发）
   - 确保新旧实现的行为一致
   - 使用快照测试捕获函数输出的变化

### 测试文件处理

1. **测试文件迁移**
   - 将 `src/utils/datetime-lib.test.js` 迁移到 `src/common/utils/tests/datetime-lib.test.js`
   - 将 `src/utils/time-lib.test.js` 迁移到 `src/common/utils/tests/time-lib.test.js`
   - 以此类推

2. **测试导入路径更新**
   - 更新测试文件中的导入路径，指向新位置
   - 确保测试仍然可以正常运行

## 5. 迁移完成后的验证

### 功能验证

1. **单元测试**
   - 确保所有测试通过
   - 检查测试覆盖率是否维持或提高

2. **集成测试**
   - 运行现有的集成测试
   - 确保应用的主要功能正常工作

3. **手动验证**
   - 测试关键用户流程
   - 检查控制台是否有错误

### 导入验证

1. **代码搜索**
   - 搜索项目中是否还有直接从 `src/utils/` 导入的代码
   - 确保所有导入都已更新或通过兼容层处理

2. **构建验证**
   - 确保项目可以正常构建
   - 检查构建后的包大小是否有异常变化

## 6. 迁移后的清理工作

### 代码清理

1. **移除重复函数**
   - 识别并移除 `src/utils/` 和 `src/common/utils/` 中的重复函数
   - 保留更好的实现

2. **更新文档**
   - 更新项目文档，说明新的工具函数位置
   - 添加迁移指南，帮助开发者理解新结构

3. **弃用旧目录**
   - 在 `src/utils/` 目录添加 README.md，说明该目录已弃用
   - 考虑在未来版本中完全移除该目录

### 最终清理

1. **移除兼容层**
   - 当确认所有代码都已更新后，考虑移除 `src/utils/utils.js` 兼容层
   - 或保留最小化的兼容层，仅重新导出最常用的函数

2. **更新构建配置**
   - 更新 webpack 或其他构建工具的配置
   - 确保不再包含已弃用的文件

## 执行计划示例

```
# 阶段一：准备和分析
Week 1-2:
- 分析所有工具函数
- 构建依赖关系图
- 分析测试覆盖率

# 阶段二：核心功能迁移
Week 3:
- 迁移 datetime-lib.js 和 time-lib.js
- 更新测试
- 更新兼容层

Week 4:
- 迁移 form-validate.js 和 request.js
- 更新测试
- 更新兼容层

# 阶段三：复杂功能迁移
Week 5-6:
- 迁移 store-utils.js 和 constants.js
- 更新测试
- 更新兼容层
- 开始更新部分导入路径

Week 7:
- 迁移剩余工具函数
- 更新测试
- 更新兼容层
- 继续更新导入路径

# 阶段四：完全迁移和清理
Week 8:
- 完成所有函数迁移
- 批量更新导入路径
- 添加弃用警告
- 全面测试验证

# 阶段五：最终清理（待定）
Future:
- 移除兼容层
- 完全移除旧目录
```

## 与单元测试清理计划的协调

1. **保留工具函数测试**
   - 按照单元测试清理计划，保留所有独立工具函数测试
   - 将这些测试也迁移到新位置

2. **测试配置更新**
   - 更新 jest-config-overrides.js，包含新的测试路径
   - 确保测试覆盖率报告包含新位置的文件

3. **测试文件命名一致性**
   - 保持测试文件命名一致，如 `*.test.js`
   - 确保测试发现机制能找到新位置的测试

---

**创建时间**: 2024年12月
**版本**: 1.0
**状态**: 待执行