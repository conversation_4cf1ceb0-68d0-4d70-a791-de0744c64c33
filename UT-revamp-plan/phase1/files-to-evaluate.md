# 需要评估的UT相关文件清单

## 1. 业务逻辑测试文件

### src/ordering/containers/payments/utils/utils.test.js

**位置**: `src/ordering/containers/payments/utils/utils.test.js`

**评估标准**:
- ✅ 如果测试独立的支付工具函数（如金额计算、格式化等）
- ❌ 如果依赖React组件或Redux状态
- ❌ 如果测试支付流程的UI交互

**建议评估方法**:
```bash
# 查看文件内容
cat src/ordering/containers/payments/utils/utils.test.js

# 检查导入的依赖
grep -n "import\|require" src/ordering/containers/payments/utils/utils.test.js
```

**决策标准**:
- 如果只测试纯函数（如价格计算、货币格式化、支付验证等），则保留
- 如果涉及组件渲染或Redux操作，则删除

### src/e-invoice/utils/tests/index.test.js

**位置**: `src/e-invoice/utils/tests/index.test.js`

**当前内容**（已知）:
```javascript
import { isEInvoicePathname } from '../index';

describe('isEInvoicePathname', () => {
  it('should return true when pathname starts with "/e-invoice"', () => {
    const pathname = '/e-invoice/some-path';
    const result = isEInvoicePathname(pathname);
    expect(result).toBe(true);
  });

  it('should return false when pathname is an empty string', () => {
    const pathname = '';
    const result = isEInvoicePathname(pathname);
    expect(result).toBe(false);
  });
});
```

**评估结果**: ✅ **建议保留**
- 测试独立的路径判断函数
- 无外部依赖
- 纯函数测试
- 逻辑简单明确

## 2. 配置相关文件

### src/setupTests.js

**位置**: `src/setupTests.js`

**当前内容**（部分）:
```javascript
import { TextEncoder, TextDecoder } from 'util';
import { configure } from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';
import '@testing-library/jest-dom/extend-expect';
require('jest-fetch-mock').enableMocks();

global.fetch = require('jest-fetch-mock');
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// ... 其他配置
```

**评估结果**: ⚠️ **需要大幅简化**
- 保留基本的全局设置（TextEncoder, TextDecoder, AbortSignal）
- 删除Enzyme相关配置
- 删除jest-fetch-mock配置
- 删除@testing-library相关配置

**建议的简化版本**:
```javascript
import { TextEncoder, TextDecoder } from 'util';

// 只保留基本的全局设置
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

if (!AbortSignal.prototype.throwIfAborted) {
  AbortSignal.prototype.throwIfAborted = function() {
    if (this.aborted) {
      throw new DOMException('Aborted', 'AbortError');
    }
  };
}
```

## 3. Mock文件

### __mocks__/fileMock.js

**位置**: `__mocks__/fileMock.js`

**当前内容**:
```javascript
module.exports = 'test-file-stub';
```

**评估结果**: ✅ **建议保留**
- 用于处理静态资源导入
- 工具函数测试可能需要
- 不特定于React/Redux

### src/utils/test.mock.js

**位置**: `src/utils/test.mock.js`

**评估标准**:
- 如果包含通用的mock工具函数，可能保留
- 如果专门为React/Redux测试设计，则删除

**建议评估方法**:
```bash
# 查看文件内容
cat src/utils/test.mock.js

# 检查文件用途
grep -r "test.mock.js" src/ --include="*.js" --include="*.test.js"
```

## 4. 可能存在的其他文件

### 需要扫描确认的文件类型

```bash
# 扫描所有可能的测试相关文件
find src -name "*test*" -type f
find src -name "*spec*" -type f
find src -name "*mock*" -type f
find src -name "*fixture*" -type f
```

### 可能需要评估的文件模式

```
src/**/utils/**/*.test.js (业务工具函数测试)
src/**/helpers/**/*.test.js (辅助函数测试)
src/**/lib/**/*.test.js (库函数测试)
src/**/services/**/*.test.js (服务函数测试)
```

## 评估流程

### 第一步：文件内容检查
```bash
# 对每个待评估文件执行
cat [文件路径]
```

### 第二步：依赖分析
```bash
# 检查导入的依赖
grep -n "import\|require" [文件路径]
```

### 第三步：使用情况检查
```bash
# 检查文件是否被其他地方引用
grep -r "[文件名]" src/ --include="*.js" --include="*.test.js"
```

### 第四步：决策标准应用

**保留条件**（必须全部满足）:
- ✅ 测试独立的纯函数
- ✅ 无React/Redux依赖
- ✅ 无复杂的外部依赖
- ✅ 测试逻辑清晰简单
- ✅ 有实际业务价值

**删除条件**（满足任一条件）:
- ❌ 依赖React组件
- ❌ 依赖Redux状态
- ❌ 使用Enzyme或React Testing Library
- ❌ 测试UI交互
- ❌ 需要复杂的mock设置
- ❌ 快照测试

## 评估结果记录

### 建议保留
- [ ] src/e-invoice/utils/tests/index.test.js ✅
- [ ] __mocks__/fileMock.js ✅

### 需要简化
- [ ] src/setupTests.js ⚠️

### 待进一步评估
- [ ] src/ordering/containers/payments/utils/utils.test.js ❓
- [ ] src/utils/test.mock.js ❓

### 评估完成后的行动

1. **保留的文件**: 确保它们在清理后仍能正常运行
2. **简化的文件**: 按照建议进行修改
3. **删除的文件**: 添加到删除清单中
4. **文档更新**: 更新相关文档说明保留的原因
