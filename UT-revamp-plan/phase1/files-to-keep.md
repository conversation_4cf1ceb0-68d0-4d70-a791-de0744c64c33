# 需要保留的UT相关文件清单

## 1. 独立工具函数测试文件

### 确认保留的文件（基于项目扫描）
```
src/utils/datetime-lib.test.js ✓
src/utils/form-validate.test.js ✓
src/utils/store-utils.test.js ✓
src/utils/time-lib.test.js ✓
src/utils/monitoring/logger.test.js ✓
src/utils/monitoring/utils.test.js ✓
src/utils/request.test.js ✓
src/common/utils/tests/onceUserAgent.test.js ✓
src/common/utils/tests/utils.test.js ✓
```

### 保留原因
这些测试文件满足以下条件：
- 测试独立的工具函数
- 不依赖Redux状态管理
- 不依赖React组件
- 不依赖UI框架（如Enzyme）
- 纯函数测试，易于维护

## 2. 业务逻辑测试文件（已评估完成）

### 确认保留的文件
```
src/ordering/containers/payments/utils/utils.test.js ✓ (信用卡检测工具函数)
src/e-invoice/utils/tests/index.test.js ✓ (路径判断工具函数)
```

### 评估结果
这些文件都符合保留标准：
- ✅ 测试独立的业务逻辑函数
- ✅ 不依赖Redux或React组件
- ✅ 只有最小的必要依赖（常量文件）
- ✅ 纯函数测试，有实际业务价值

## 3. 配置和常量测试文件

### 可能保留的文件（如果存在且独立）
```
src/config.test.js (如果存在且不依赖Redux/React)
src/constants.test.js (如果存在且独立)
src/**/constants.test.js (如果存在且独立)
```

### 当前状态
- 项目扫描中未发现这类文件
- 如果将来添加，需要确保独立性

## 4. 测试配置文件（简化保留）

### 需要简化但保留的文件
```
src/setupTests.js ✓ (需要大幅简化)
```

### 简化内容
- 移除Enzyme配置
- 移除@testing-library/jest-dom
- 移除jest-fetch-mock
- 只保留基本的全局设置

## 5. Mock文件（选择性保留）

### 可能需要保留的Mock文件
```
__mocks__/fileMock.js ✓ (项目根目录，用于静态资源)
```

### 保留原因
- 用于处理静态资源导入
- 不特定于React/Redux测试
- 工具函数测试可能需要

## 保留文件的特征

### ✅ 应该保留的测试特征
- 测试纯函数或工具函数
- 无外部依赖（除了被测试的函数）
- 不需要DOM环境
- 不需要React/Redux环境
- 测试逻辑简单明确
- 易于维护和理解

### ❌ 不应该保留的测试特征
- 依赖Redux store
- 依赖React组件渲染
- 使用Enzyme或React Testing Library
- 需要mock大量外部依赖
- 测试UI交互
- 测试组件状态变化
- 快照测试

## 保留文件验证清单

在清理完成后，验证保留的测试文件：

### 1. 运行测试
```bash
yarn test
```

### 2. 检查测试覆盖率
```bash
yarn test:coverage
```

### 3. 验证每个保留的测试文件
- [ ] src/utils/datetime-lib.test.js - 独立日期时间工具函数
- [ ] src/utils/form-validate.test.js - 独立表单验证函数（使用DOM）
- [ ] src/utils/store-utils.test.js - 独立存储工具函数
- [ ] src/utils/time-lib.test.js - 独立时间处理函数
- [ ] src/utils/monitoring/logger.test.js - 独立日志工具函数
- [ ] src/utils/monitoring/utils.test.js - 独立监控工具函数
- [ ] src/utils/request.test.js - 独立请求工具函数
- [ ] src/common/utils/tests/onceUserAgent.test.js - 独立用户代理检测
- [ ] src/common/utils/tests/utils.test.js - 独立通用工具函数
- [ ] src/ordering/containers/payments/utils/utils.test.js - 信用卡检测工具函数
- [ ] src/e-invoice/utils/tests/index.test.js - 路径判断工具函数

## 预期保留的文件数量

- 确认保留: 11个测试文件（9个工具函数 + 2个业务逻辑）
- 配置文件: 1个（简化的setupTests.js）
- Mock文件: 1个（fileMock.js）
- 总计: 13个文件

## 保留文件的维护建议

1. **定期审查**: 每季度审查保留的测试文件，确保它们仍然有价值
2. **保持独立**: 确保测试不会逐渐引入外部依赖
3. **文档化**: 为每个保留的测试文件添加清晰的注释说明其用途
4. **标准化**: 建立工具函数测试的编写标准和最佳实践
