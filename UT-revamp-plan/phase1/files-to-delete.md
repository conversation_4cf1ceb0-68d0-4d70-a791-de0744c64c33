# 需要删除的UT相关文件清单

## 1. Redux相关测试文件

### 模式匹配
```
src/redux/**/*.test.js
src/redux/**/*.spec.js
src/redux/__tests__/**/*.js
src/stores/redux/**/*.test.js
src/stores/redux/**/*.spec.js
src/stores/redux/__tests__/**/*.js
src/cashback/redux/**/*.test.js
src/cashback/redux/**/*.spec.js
src/cashback/redux/__tests__/**/*.js
src/ordering/redux/**/*.test.js
src/ordering/redux/**/*.spec.js
src/ordering/redux/__tests__/**/*.js
src/rewards/redux/**/*.test.js
src/rewards/redux/**/*.spec.js
src/rewards/redux/__tests__/**/*.js
src/voucher/redux/**/*.test.js
src/voucher/redux/**/*.spec.js
src/voucher/redux/__tests__/**/*.js
src/site/redux/**/*.test.js
src/site/redux/**/*.spec.js
src/site/redux/__tests__/**/*.js
```

### 实际存在的文件（基于项目扫描）
```
src/stores/redux/store.test.js
src/stores/redux/modules/appReducers.test.js
src/stores/redux/modules/appActions.test.js
src/stores/redux/modules/homeReducers.test.js
src/cashback/redux/modules/appReducer.test.js
src/cashback/redux/modules/appActions.test.js
src/redux/modules/entities/loyaltyHistories.test.js
src/redux/modules/entities/orders.test.js
src/redux/modules/entities/businesses.test.js
src/redux/modules/entities/products.test.js
src/redux/modules/entities/onlineStores.test.js
src/redux/modules/entities/categories.test.js
src/redux/modules/entities/stores.test.js
```

## 2. UI组件测试文件

### 模式匹配
```
src/components/**/*.test.js
src/components/**/*.spec.js
src/components/__tests__/**/*.js
src/containers/**/*.test.js
src/containers/**/*.spec.js
src/containers/__tests__/**/*.js
src/pages/**/*.test.js
src/pages/**/*.spec.js
src/pages/__tests__/**/*.js
src/common/components/**/*.test.js
src/common/components/**/*.spec.js
src/common/components/__tests__/**/*.js
src/*/components/**/*.test.js
src/*/components/**/*.spec.js
src/*/components/__tests__/**/*.js
src/*/containers/**/*.test.js
src/*/containers/**/*.spec.js
src/*/containers/__tests__/**/*.js
```

### 实际存在的文件（需要进一步扫描确认）
```
# 目前扫描未发现，但可能存在于以下位置：
# src/components/
# src/containers/
# src/common/components/
# src/ordering/components/
# src/ordering/containers/
# src/cashback/components/
# src/cashback/containers/
# src/rewards/containers/
# src/voucher/components/
# src/voucher/containers/
# src/site/components/
# src/site/containers/
# src/stores/containers/
# src/user/components/
# src/user/containers/
# src/e-invoice/components/
# src/e-invoice/containers/
```

## 3. JSX测试文件

### 模式匹配
```
src/**/*.test.jsx
src/**/*.spec.jsx
```

## 4. 快照文件夹

### 模式匹配
```
src/**/__snapshots__/
```

## 5. Mock数据和Fixtures文件夹

### 模式匹配
```
src/**/__fixtures__/
src/**/__mocks__/
src/**/redux/__fixture__/
src/**/redux/__fixtures__/
src/**/redux/__mocks__/
src/components/__mocks__/
src/containers/__mocks__/
src/pages/__mocks__/
```

### 实际存在的文件夹（基于项目扫描）
```
src/redux/__fixtures__/ (如果存在)
src/utils/__fixtures__/ (如果存在)
```

## 6. 测试辅助工具文件

### 需要删除的文件
```
src/utils/testHelper.js ✓ (确认存在)
src/utils/test-utils.js
src/utils/redux-test-utils.js
src/utils/mock-store.js
src/utils/test-fixtures.js
src/utils/test-setup.js
```

## 7. 其他测试相关文件

### 需要评估的文件
```
src/utils/test.mock.js (可能需要删除)
```

## 删除命令汇总

```bash
# 1. 删除Redux相关测试文件
find src -path "*/redux/*" -name "*.test.js" -delete
find src -path "*/redux/*" -name "*.spec.js" -delete
find src -path "*/redux/__tests__/*" -type f -delete

# 2. 删除UI组件测试文件
find src/components -name "*.test.js" -delete 2>/dev/null || true
find src/components -name "*.spec.js" -delete 2>/dev/null || true
find src/containers -name "*.test.js" -delete 2>/dev/null || true
find src/containers -name "*.spec.js" -delete 2>/dev/null || true
find src/pages -name "*.test.js" -delete 2>/dev/null || true
find src/pages -name "*.spec.js" -delete 2>/dev/null || true
find src/common/components -name "*.test.js" -delete 2>/dev/null || true
find src/common/components -name "*.spec.js" -delete 2>/dev/null || true
find src -path "*/components/*" -name "*.test.js" -delete
find src -path "*/components/*" -name "*.spec.js" -delete
find src -path "*/containers/*" -name "*.test.js" -delete
find src -path "*/containers/*" -name "*.spec.js" -delete

# 3. 删除JSX测试文件
find src -name "*.test.jsx" -delete
find src -name "*.spec.jsx" -delete

# 4. 删除快照文件夹
find src -type d -name "__snapshots__" -exec rm -rf {} + 2>/dev/null || true

# 5. 删除mock数据和fixtures文件夹
find src -type d -name "__fixtures__" -exec rm -rf {} + 2>/dev/null || true
find src -type d -name "__mocks__" -exec rm -rf {} + 2>/dev/null || true
find src -path "*/redux/__fixture__" -exec rm -rf {} + 2>/dev/null || true
find src -path "*/redux/__fixtures__" -exec rm -rf {} + 2>/dev/null || true

# 6. 删除测试辅助工具文件
rm -f src/utils/testHelper.js
rm -f src/utils/test-utils.js
rm -f src/utils/redux-test-utils.js
rm -f src/utils/mock-store.js
rm -f src/utils/test-fixtures.js
rm -f src/utils/test.mock.js
```

## 预计删除的文件数量

- Redux测试文件: 13个
- UI组件测试文件: 0个（当前扫描）
- 测试辅助文件: 1-6个
- Mock/Fixtures文件夹: 待确认
- 总计: 约14-20个文件
