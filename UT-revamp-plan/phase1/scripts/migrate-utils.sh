#!/bin/bash
# 工具函数迁移脚本 - 第一阶段高优先级文件

# 确保脚本在错误时停止
set -e

echo "开始迁移高优先级工具函数..."

# 1. 创建目标目录结构
echo "创建目标目录结构..."
mkdir -p src/common/utils/tests
mkdir -p src/common/utils/monitoring
mkdir -p src/common/utils/tests/monitoring

# 2. 备份原始文件
echo "备份原始文件..."
mkdir -p backups/utils
mkdir -p backups/utils/monitoring

# 备份工具函数文件
cp src/utils/datetime-lib.js backups/utils/
cp src/utils/time-lib.js backups/utils/
cp src/utils/form-validate.js backups/utils/
cp src/utils/request.js backups/utils/
cp src/utils/monitoring/logger.js backups/utils/monitoring/
cp src/utils/monitoring/utils.js backups/utils/monitoring/

# 备份测试文件
cp src/utils/datetime-lib.test.js backups/utils/
cp src/utils/time