#!/bin/bash

# 完整单元测试清理脚本
# 执行前请确保已备份重要文件

echo "开始执行UT清理..."

# 1. 删除Redux相关测试文件
echo "删除Redux相关测试文件..."
find src -path "*/redux/*" -name "*.test.js" -delete
find src -path "*/redux/*" -name "*.spec.js" -delete
find src -path "*/redux/__tests__/*" -type f -delete

# 2. 删除UI组件测试文件
echo "删除UI组件测试文件..."
find src/components -name "*.test.js" -delete 2>/dev/null || true
find src/components -name "*.spec.js" -delete 2>/dev/null || true
find src/containers -name "*.test.js" -delete 2>/dev/null || true
find src/containers -name "*.spec.js" -delete 2>/dev/null || true
find src/pages -name "*.test.js" -delete 2>/dev/null || true
find src/pages -name "*.spec.js" -delete 2>/dev/null || true
find src/common/components -name "*.test.js" -delete 2>/dev/null || true
find src/common/components -name "*.spec.js" -delete 2>/dev/null || true
find src -path "*/components/*" -name "*.test.js" -delete
find src -path "*/components/*" -name "*.spec.js" -delete
find src -path "*/containers/*" -name "*.test.js" -delete
find src -path "*/containers/*" -name "*.spec.js" -delete

# 3. 删除JSX测试文件
echo "删除JSX测试文件..."
find src -name "*.test.jsx" -delete
find src -name "*.spec.jsx" -delete

# 4. 删除快照文件夹
echo "删除快照文件夹..."
find src -type d -name "__snapshots__" -exec rm -rf {} + 2>/dev/null || true

# 5. 删除mock数据和fixtures文件夹
echo "删除mock数据和fixtures文件夹..."
find src -type d -name "__fixtures__" -exec rm -rf {} + 2>/dev/null || true
find src -type d -name "__mocks__" -exec rm -rf {} + 2>/dev/null || true
find src -path "*/redux/__fixture__" -exec rm -rf {} + 2>/dev/null || true
find src -path "*/redux/__fixtures__" -exec rm -rf {} + 2>/dev/null || true

# 6. 删除测试辅助工具文件
echo "删除测试辅助工具文件..."
rm -f src/utils/testHelper.js
rm -f src/utils/test-utils.js
rm -f src/utils/redux-test-utils.js
rm -f src/utils/mock-store.js
rm -f src/utils/test-fixtures.js

echo "UT清理完成！"
echo "请手动更新以下文件："
echo "1. jest-config-overrides.js"
echo "2. src/setupTests.js"
echo ""
echo "然后运行 'yarn test' 验证剩余测试是否正常运行"
