# Phase 1A: 基础迁移阶段执行计划

## 概述
Phase 1A专注于迁移2个完全独立的工具函数，建立稳定的迁移流程和兼容层策略。

## 迁移文件清单

### 确定迁移的文件（2个）
1. **time-lib.js** - 基础时间处理工具，被datetime-lib依赖
2. **form-validate.js** - 表单验证工具，完全独立

### 暂不迁移的文件（留到Phase 1B）
- **request.js** - 被monitoring/logger.test.js依赖，需要特殊处理
- **monitoring/logger.js** - 依赖request.js
- **monitoring/utils.js** - 相对独立但属于monitoring模块
- **datetime-lib.js** - 依赖time-lib和logger，最复杂

## 详细执行步骤

### Task 1: 建立基线和准备工作 (0.5天)

#### 1.1 建立测试基线
```bash
# 运行当前测试套件
yarn test --verbose --coverage > phase1a-baseline-test-results.log 2>&1

# 记录当前测试状态
echo "Phase 1A 基线测试结果:" > phase1a-baseline-summary.txt
echo "测试时间: $(date)" >> phase1a-baseline-summary.txt
echo "通过的测试: $(grep -c "PASS" phase1a-baseline-test-results.log)" >> phase1a-baseline-summary.txt
echo "失败的测试: $(grep -c "FAIL" phase1a-baseline-test-results.log)" >> phase1a-baseline-summary.txt
```

#### 1.2 创建目标目录结构
```bash
# 确保目标目录存在
mkdir -p src/common/utils/tests

# 备份重要文件
cp src/utils/time-lib.js src/utils/time-lib.js.backup
cp src/utils/time-lib.test.js src/utils/time-lib.test.js.backup
cp src/utils/form-validate.js src/utils/form-validate.js.backup
cp src/utils/form-validate.test.js src/utils/form-validate.test.js.backup
```

#### 1.3 准备迁移脚本
创建通用的迁移脚本模板，用于后续迁移。

### Task 2: 迁移 time-lib.js (1天)

#### 2.1 迁移工具函数文件
```bash
# 复制文件到目标位置
cp src/utils/time-lib.js src/common/utils/time-lib.js
```

#### 2.2 迁移测试文件
```bash
# 复制测试文件到目标位置
cp src/utils/time-lib.test.js src/common/utils/tests/time-lib.test.js

# 更新测试文件中的导入路径
sed -i.bak "s|from './time-lib'|from '../time-lib'|g" src/common/utils/tests/time-lib.test.js
```

#### 2.3 创建兼容层
```bash
# 在原位置创建重导出文件
cat > src/utils/time-lib.js << 'EOF'
// 兼容层：重导出迁移后的time-lib
// TODO: 在Phase 2中移除此兼容层
export * from '../common/utils/time-lib';
export { default } from '../common/utils/time-lib';
EOF
```

#### 2.4 验证迁移结果
```bash
# 运行time-lib相关测试
yarn test src/common/utils/tests/time-lib.test.js
yarn test src/utils/time-lib.test.js

# 运行依赖time-lib的测试
yarn test src/utils/datetime-lib.test.js
yarn test src/utils/store-utils.test.js
```

#### 2.5 提交并等待review
```bash
git add .
git commit -m "Phase 1A: 迁移time-lib.js到src/common/utils/

- 迁移src/utils/time-lib.js到src/common/utils/time-lib.js
- 迁移测试文件到src/common/utils/tests/time-lib.test.js
- 创建兼容层保持向后兼容
- 所有相关测试通过"
```

### Task 3: 迁移 form-validate.js (1天)

#### 3.1 迁移工具函数文件
```bash
# 复制文件到目标位置
cp src/utils/form-validate.js src/common/utils/form-validate.js
```

#### 3.2 迁移测试文件
```bash
# 复制测试文件到目标位置
cp src/utils/form-validate.test.js src/common/utils/tests/form-validate.test.js

# 更新测试文件中的导入路径
sed -i.bak "s|from './form-validate'|from '../form-validate'|g" src/common/utils/tests/form-validate.test.js
```

#### 3.3 创建兼容层
```bash
# 在原位置创建重导出文件
cat > src/utils/form-validate.js << 'EOF'
// 兼容层：重导出迁移后的form-validate
// TODO: 在Phase 2中移除此兼容层
export * from '../common/utils/form-validate';
export { default } from '../common/utils/form-validate';
EOF
```

#### 3.4 验证迁移结果
```bash
# 运行form-validate相关测试
yarn test src/common/utils/tests/form-validate.test.js
yarn test src/utils/form-validate.test.js

# 运行完整测试套件确保没有破坏其他功能
yarn test
```

#### 3.5 提交并等待review
```bash
git add .
git commit -m "Phase 1A: 迁移form-validate.js到src/common/utils/

- 迁移src/utils/form-validate.js到src/common/utils/form-validate.js
- 迁移测试文件到src/common/utils/tests/form-validate.test.js
- 创建兼容层保持向后兼容
- 所有测试通过"
```

### Task 4: 验证和文档更新 (0.5-1.5天)

#### 4.1 完整验证
```bash
# 运行完整测试套件
yarn test --verbose --coverage > phase1a-final-test-results.log 2>&1

# 对比基线结果
echo "Phase 1A 完成后测试对比:" > phase1a-completion-summary.txt
echo "基线通过测试: $(grep -c "PASS" phase1a-baseline-test-results.log)" >> phase1a-completion-summary.txt
echo "完成后通过测试: $(grep -c "PASS" phase1a-final-test-results.log)" >> phase1a-completion-summary.txt
```

#### 4.2 更新文档
- 更新迁移日志
- 记录遇到的问题和解决方案
- 准备Phase 1B的执行计划

#### 4.3 创建Phase 1A报告
生成详细的Phase 1A执行报告，包括：
- 迁移的文件清单
- 遇到的问题和解决方案
- 测试结果对比
- 兼容层策略验证
- Phase 1B的建议

## 成功标准

### Phase 1A完成标准：
- [ ] time-lib.js成功迁移到src/common/utils/
- [ ] form-validate.js成功迁移到src/common/utils/
- [ ] 所有相关测试通过
- [ ] 兼容层正常工作
- [ ] 没有破坏现有功能
- [ ] 建立了可重复的迁移流程
- [ ] 完成详细的执行报告

### 验证清单：
- [ ] 新位置的测试文件正常运行
- [ ] 原位置的兼容层正常工作
- [ ] 依赖这些工具函数的其他文件正常工作
- [ ] 完整测试套件通过率不下降
- [ ] 没有新的测试失败

## 风险控制

### 回滚计划：
如果迁移过程中出现问题：
```bash
# 恢复原始文件
cp src/utils/time-lib.js.backup src/utils/time-lib.js
cp src/utils/time-lib.test.js.backup src/utils/time-lib.test.js
cp src/utils/form-validate.js.backup src/utils/form-validate.js
cp src/utils/form-validate.test.js.backup src/utils/form-validate.test.js

# 删除新创建的文件
rm -f src/common/utils/time-lib.js
rm -f src/common/utils/form-validate.js
rm -f src/common/utils/tests/time-lib.test.js
rm -f src/common/utils/tests/form-validate.test.js
```

### 每步验证：
- 每个文件迁移后立即运行相关测试
- 发现问题立即停止，分析原因
- 每次提交前运行完整测试套件
