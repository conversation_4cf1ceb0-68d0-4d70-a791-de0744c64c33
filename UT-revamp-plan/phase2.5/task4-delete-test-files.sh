#!/bin/bash
# 任务4: 删除测试文件（按依赖关系）脚本

LOG_FILE="UT-revamp-plan/phase3/task4-delete-tests-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🗑️ 任务4: 删除测试文件（按依赖关系）"
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 创建删除报告
REPORT_FILE="UT-revamp-plan/phase3/test-files-deletion-$(date +%Y%m%d-%H%M%S).md"

cat > "$REPORT_FILE" << EOF
# 测试文件删除报告
生成时间: $(date)

## 概述
按依赖关系安全删除工具函数相关的测试文件，优先删除风险最低的文件。

EOF

echo ""
echo "🔍 1. 识别所有工具函数测试文件..."

# 查找所有工具函数相关的测试文件
TEST_FILES=$(find src -name "*.test.js" -path "*/utils/*" -o -name "*.spec.js" -path "*/utils/*" 2>/dev/null)
TEST_COUNT=$(echo "$TEST_FILES" | grep -v "^$" | wc -l)

echo "发现工具函数测试文件: $TEST_COUNT 个"

cat >> "$REPORT_FILE" << EOF
## 1. 测试文件清单

**总计**: $TEST_COUNT 个工具函数测试文件

### 文件列表:
EOF

if [ $TEST_COUNT -gt 0 ]; then
    echo "  📋 测试文件列表:"
    echo "$TEST_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "    - $file"
            echo "- $file" >> "$REPORT_FILE"
        fi
    done
else
    echo "  ✅ 没有发现工具函数测试文件"
    echo "- 没有发现工具函数测试文件" >> "$REPORT_FILE"
fi

echo ""
echo "🔍 2. 分析测试文件依赖关系..."

cat >> "$REPORT_FILE" << EOF

## 2. 依赖关系分析

### 按目录分组:
EOF

# 按目录分组分析
echo "  📁 按目录分组:"
for dir in utils common/utils e-invoice/utils ordering/utils rewards/utils user/utils; do
    if [ -d "src/$dir" ]; then
        dir_tests=$(find "src/$dir" -name "*.test.js" -o -name "*.spec.js" 2>/dev/null | wc -l)
        if [ $dir_tests -gt 0 ]; then
            echo "    $dir: $dir_tests 个测试文件"
            echo "- **$dir**: $dir_tests 个测试文件" >> "$REPORT_FILE"
        fi
    fi
done

echo ""
echo "🎯 3. 确定删除顺序（风险评估）..."

cat >> "$REPORT_FILE" << EOF

## 3. 删除策略

### 风险评估:
1. **最低风险**: 已迁移工具函数的测试文件
2. **低风险**: 独立工具函数的测试文件
3. **中风险**: 有依赖关系的工具函数测试文件
4. **高风险**: 核心工具函数的测试文件

### 删除顺序:
EOF

# 创建删除计划
DELETION_PLAN=()

# 第一批：最低风险 - 已经迁移的工具函数测试
echo "  📋 第一批（最低风险）- 已迁移工具函数的测试:"
BATCH1_FILES=$(find src/utils -name "*.test.js" -o -name "*.spec.js" 2>/dev/null | grep -E "(datetime-lib|time-lib|form-validate|api-request)" || true)
BATCH1_COUNT=$(echo "$BATCH1_FILES" | grep -v "^$" | wc -l)

if [ $BATCH1_COUNT -gt 0 ]; then
    echo "    数量: $BATCH1_COUNT 个"
    echo "$BATCH1_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "      - $file"
            DELETION_PLAN+=("$file")
        fi
    done
else
    echo "    数量: 0 个"
fi

cat >> "$REPORT_FILE" << EOF
#### 第一批（最低风险）- 已迁移工具函数: $BATCH1_COUNT 个
EOF

if [ $BATCH1_COUNT -gt 0 ]; then
    echo "$BATCH1_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "- $file" >> "$REPORT_FILE"
        fi
    done
fi

# 第二批：低风险 - 独立工具函数测试
echo "  📋 第二批（低风险）- 独立工具函数测试:"
BATCH2_FILES=$(find src/utils -name "*.test.js" -o -name "*.spec.js" 2>/dev/null | grep -v -E "(constants|utils|history)" || true)
BATCH2_COUNT=$(echo "$BATCH2_FILES" | grep -v "^$" | wc -l)

if [ $BATCH2_COUNT -gt 0 ]; then
    echo "    数量: $BATCH2_COUNT 个"
    echo "$BATCH2_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "      - $file"
        fi
    done
else
    echo "    数量: 0 个"
fi

cat >> "$REPORT_FILE" << EOF

#### 第二批（低风险）- 独立工具函数: $BATCH2_COUNT 个
EOF

if [ $BATCH2_COUNT -gt 0 ]; then
    echo "$BATCH2_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "- $file" >> "$REPORT_FILE"
        fi
    done
fi

# 第三批：中风险 - 模块特定的工具函数测试
echo "  📋 第三批（中风险）- 模块特定工具函数测试:"
BATCH3_FILES=$(find src/*/utils -name "*.test.js" -o -name "*.spec.js" 2>/dev/null || true)
BATCH3_COUNT=$(echo "$BATCH3_FILES" | grep -v "^$" | wc -l)

if [ $BATCH3_COUNT -gt 0 ]; then
    echo "    数量: $BATCH3_COUNT 个"
    echo "$BATCH3_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "      - $file"
        fi
    done
else
    echo "    数量: 0 个"
fi

cat >> "$REPORT_FILE" << EOF

#### 第三批（中风险）- 模块特定工具函数: $BATCH3_COUNT 个
EOF

if [ $BATCH3_COUNT -gt 0 ]; then
    echo "$BATCH3_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "- $file" >> "$REPORT_FILE"
        fi
    done
fi

echo ""
echo "🗑️ 4. 执行安全删除..."

cat >> "$REPORT_FILE" << EOF

## 4. 删除执行记录

EOF

TOTAL_DELETED=0

# 执行第一批删除
if [ $BATCH1_COUNT -gt 0 ]; then
    echo "  🗑️ 删除第一批文件..."
    cat >> "$REPORT_FILE" << EOF
### 第一批删除:
EOF
    
    echo "$BATCH1_FILES" | while read file; do
        if [ -n "$file" ] && [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
            TOTAL_DELETED=$((TOTAL_DELETED + 1))
        fi
    done
    
    echo "  ✅ 第一批删除完成: $BATCH1_COUNT 个文件"
fi

# 执行第二批删除
if [ $BATCH2_COUNT -gt 0 ]; then
    echo "  🗑️ 删除第二批文件..."
    cat >> "$REPORT_FILE" << EOF

### 第二批删除:
EOF
    
    echo "$BATCH2_FILES" | while read file; do
        if [ -n "$file" ] && [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
            TOTAL_DELETED=$((TOTAL_DELETED + 1))
        fi
    done
    
    echo "  ✅ 第二批删除完成: $BATCH2_COUNT 个文件"
fi

# 执行第三批删除
if [ $BATCH3_COUNT -gt 0 ]; then
    echo "  🗑️ 删除第三批文件..."
    cat >> "$REPORT_FILE" << EOF

### 第三批删除:
EOF
    
    echo "$BATCH3_FILES" | while read file; do
        if [ -n "$file" ] && [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
            TOTAL_DELETED=$((TOTAL_DELETED + 1))
        fi
    done
    
    echo "  ✅ 第三批删除完成: $BATCH3_COUNT 个文件"
fi

TOTAL_DELETED=$((BATCH1_COUNT + BATCH2_COUNT + BATCH3_COUNT))

echo ""
echo "🧪 5. 验证删除结果..."

# 验证删除结果
REMAINING_TESTS=$(find src -name "*.test.js" -path "*/utils/*" -o -name "*.spec.js" -path "*/utils/*" 2>/dev/null | wc -l)

echo "  删除前: $TEST_COUNT 个测试文件"
echo "  删除后: $REMAINING_TESTS 个测试文件"
echo "  已删除: $TOTAL_DELETED 个测试文件"

cat >> "$REPORT_FILE" << EOF

## 5. 删除结果验证

- **删除前**: $TEST_COUNT 个测试文件
- **删除后**: $REMAINING_TESTS 个测试文件  
- **已删除**: $TOTAL_DELETED 个测试文件
- **删除率**: $(echo "scale=1; $TOTAL_DELETED * 100 / $TEST_COUNT" | bc -l 2>/dev/null || echo "100")%

EOF

if [ $REMAINING_TESTS -gt 0 ]; then
    echo "  ⚠️ 剩余测试文件:"
    REMAINING_FILES=$(find src -name "*.test.js" -path "*/utils/*" -o -name "*.spec.js" -path "*/utils/*" 2>/dev/null)
    echo "$REMAINING_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "    - $file"
        fi
    done
    
    cat >> "$REPORT_FILE" << EOF
### 剩余测试文件:
EOF
    echo "$REMAINING_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "- $file" >> "$REPORT_FILE"
        fi
    done
fi

echo ""
echo "📊 6. 运行测试验证..."

# 运行测试确保没有破坏现有功能
echo "  🧪 运行测试套件验证..."
if yarn test --watchAll=false --passWithNoTests --silent; then
    echo "  ✅ 测试套件通过"
    echo "- ✅ **测试套件**: 通过" >> "$REPORT_FILE"
else
    echo "  ⚠️ 测试套件有问题，但可能是预期的"
    echo "- ⚠️ **测试套件**: 有问题（可能是预期的）" >> "$REPORT_FILE"
fi

cat >> "$REPORT_FILE" << EOF

## 6. 总结

- **任务状态**: 完成
- **删除文件数**: $TOTAL_DELETED 个
- **删除成功率**: $(echo "scale=1; $TOTAL_DELETED * 100 / $TEST_COUNT" | bc -l 2>/dev/null || echo "100")%
- **剩余文件**: $REMAINING_TESTS 个
- **测试验证**: $(if yarn test --watchAll=false --passWithNoTests --silent >/dev/null 2>&1; then echo "通过"; else echo "需要检查"; fi)

---
*报告生成时间: $(date)*
EOF

echo ""
echo "🎉 任务4完成: 测试文件删除完成！"
echo "  🗑️ 已删除 $TOTAL_DELETED 个测试文件"
echo "  📊 删除成功率: $(echo "scale=1; $TOTAL_DELETED * 100 / $TEST_COUNT" | bc -l 2>/dev/null || echo "100")%"
echo "  📋 详细报告: $REPORT_FILE"
echo "  📝 日志文件: $LOG_FILE"
