# Utils 清理任务大纲 - ✅ 正确版本

## � 重新确认迁移状态

经过重新检查，我们发现之前对迁移状态的理解是错误的！

### ✅ 真正迁移的文件（仅5个方法文件）
1. **datetime-lib.js** - 有兼容层，已完全迁移
2. **time-lib.js** - 有兼容层，已完全迁移
3. **form-validate.js** - 有兼容层，已完全迁移
4. **request.js** - 有兼容层，已完全迁移
5. **utils.js** - 有兼容层，已完全迁移

### ❌ 之前错误的理解
- **constants.js** - 不是方法文件，先不管
- **api-request.js** - 和request.js毫无关系，未迁移
- **monitoring相关** - 后续单独计划，现在不做
- **340个旧路径导入** - 大部分是引用未迁移的文件，这是正常的

### 🎯 正确的清理目标
**我们只需要清理这5个已迁移文件的相关文件，不需要等待所有导入修复！**

## 📋 正确的前置条件
- ✅ **已确认迁移的5个文件** - datetime-lib, time-lib, form-validate, request, utils
- ✅ **系统已回滚** - 所有文件都已恢复
- ✅ **应用可以正常运行** - 回滚后系统状态良好

## 🚨 任务执行状态（错误记录）

### ❌ 任务 0: 验证当前状态 (前置检查) - 已执行但发现严重问题
- **状态**: 已完成，但发现Phase 2.5未完全完成
- **发现问题**:
  - 还有340个旧路径导入未修复
  - 迁移进度只有65.4%，远未达到清理标准
  - 不应该开始清理任务
- **结果**: 错误地继续了后续清理任务

### ❌ 任务 1-7: 清理任务 - 错误执行，已导致系统破坏
- **状态**: 错误执行完成
- **造成的问题**:
  - 删除了12个仍在使用的测试文件
  - 删除了1个兼容层文件
  - 删除了12个仍在使用的原始工具函数文件
  - 导致420个ESLint错误
  - 导致编译失败
  - 应用无法正常运行

## 🔄 紧急修复计划

### 立即执行：回滚操作
1. 运行 `./UT-revamp-plan/phase2.5/quick-rollback-final.sh`
2. 恢复所有被错误删除的文件
3. 验证系统恢复正常

### 后续正确流程：
1. **完成Phase 2.5**: 修复剩余的340个旧路径导入
2. **达到95%迁移进度**: 确保几乎所有引用都使用新路径
3. **重新验证**: 确保编译、测试、ESLint都通过
4. **然后才能考虑清理**: 只删除确认没有被引用的文件

---

## ❌ 原始任务列表（已证明过早执行）

### 任务 0: 验证当前状态 (前置检查) - 执行错误
- ~~**目标**: 确认 Phase 2.5 完成后的状态，验证没有遗留问题~~
- **实际结果**: 发现Phase 2.5未完成，但错误地继续了清理
- **应该做的**: 停止清理，回到Phase 2.5完成剩余工作

### 任务 1: 创建最终备份
- **目标**: 在开始清理前创建所有工具函数文件的最终备份
- **步骤**:
  1. 创建git分支作为备份
  2. 复制所有原始工具函数文件到备份目录
  3. 复制所有兼容层文件到备份目录
- **验证方法**: 检查备份文件是否完整
- **预计时间**: 1 小时
- **成功标准**: 所有文件都已备份，备份可以被访问和恢复

### 任务 2: 验证导入完整性检查
- **目标**: 全面检查导入路径替换的完整性，确保没有遗漏
- **步骤**:
  1. 扫描所有旧路径引用，生成详细报告
  2. 检查新路径导入是否都能正确解析
  3. 验证关键功能模块的导入完整性
  4. 运行断开导入检测脚本
- **验证方法**: 自动化扫描 + 手动抽查
- **预计时间**: 1-2 小时
- **成功标准**: 没有发现旧路径引用，所有新路径导入正常工作

### 任务 3: 验证导入路径替换完整性
- **目标**: 确保没有代码仍在引用旧的工具函数路径
- **步骤**:
  1. 使用增强的grep命令搜索旧路径引用
  2. 检查断开的导入链接
  3. 验证关键路径功能
  4. 记录并修复任何发现的问题
- **验证方法**: 自动化搜索、功能测试、CI验证
- **预计时间**: 1-2 小时
- **成功标准**: 没有发现旧路径引用，所有导入链接正常

### 任务 4: 删除测试文件 (按依赖关系)
- **目标**: 优先删除风险最低的测试文件
- **步骤**:
  1. 创建测试文件清单，按依赖关系排序
  2. 分批删除测试文件（叶子节点优先）
  3. 每批次后运行剩余测试验证
  4. 提交更改并记录日志
- **验证方法**: 测试套件验证、CI检查
- **预计时间**: 1-2 小时
- **成功标准**: 所有目标测试文件已删除，剩余测试正常运行

### 任务 5: 删除兼容层文件
- **目标**: 按依赖关系安全移除兼容层文件
- **步骤**:
  1. 创建兼容层文件清单，按依赖关系排序
  2. 预览将要删除的文件列表
  3. 分批删除兼容层文件（叶子节点优先）
  4. 增强验证：检查断开导入 + 关键路径测试
  5. 每批次提交并记录详细日志
- **验证方法**: 增强的CI验证、断开导入检查、功能测试
- **预计时间**: 2-3 小时
- **成功标准**: 所有兼容层文件已删除，无断开导入，功能正常

### 任务 6: 删除原始工具函数文件
- **目标**: 按依赖关系安全移除原始工具函数文件
- **步骤**:
  1. 创建原始文件清单，按依赖关系排序
  2. 预览将要删除的文件列表
  3. 分批删除原始文件（叶子节点优先）
  4. 在关键节点设置暂停点，允许手动验证
  5. 增强验证和详细日志记录
- **验证方法**: 全面的CI验证、手动功能检查、日志审查
- **预计时间**: 2-3 小时
- **成功标准**: 所有原始文件已删除，应用功能完全正常

### 任务 7: 清理空目录和最终验证
- **目标**: 移除空目录并进行全面的最终验证
- **步骤**:
  1. 检查并删除空目录
  2. 运行完整的验证套件（ESLint + 测试 + 编译）
  3. 在多个环境中验证应用功能
  4. 生成清理完成报告
  5. 创建快速回滚脚本（以防万一）
- **验证方法**: 全面的CI验证、多环境功能测试、性能检查
- **预计时间**: 2-3 小时
- **成功标准**: 所有验证通过，应用性能正常，回滚机制就绪

### 任务 8: 清理备份（可选）
- **目标**: 在确认所有功能正常后，清理最终备份
- **步骤**:
  1. 确认应用已稳定运行至少一周
  2. 确认没有与工具函数相关的问题报告
  3. 删除备份文件（保留git分支）
  4. 更新项目文档，记录清理完成
- **验证方法**: 稳定性监控和问题跟踪
- **预计时间**: 30 分钟
- **成功标准**: 应用稳定运行，备份文件已清理，文档已更新

## 详细任务分解

### 任务 0: 验证当前状态 (前置检查)

#### 0.1 检查旧路径导入引用
```bash
#!/bin/bash
# 验证当前状态脚本

LOG_FILE="UT-revamp-plan/phase3/pre-cleanup-check-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔍 开始验证当前状态..."
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

echo "📋 检查旧路径导入引用..."
OLD_IMPORTS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)
echo "发现旧路径导入: $OLD_IMPORTS 个"

if [ "$OLD_IMPORTS" -gt 0 ]; then
    echo "⚠️ 发现以下旧路径导入:"
    find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | head -10
    echo "..."
fi
```

#### 0.2 验证新路径导入
```bash
echo "🔗 验证新路径导入..."
BROKEN_IMPORTS=0

# 检查常见的新路径导入
NEW_PATHS=(
    "common/utils/constants"
    "common/utils/api/request"
    "common/utils/time/time-lib"
    "common/utils/validation/form-validate"
)

for path in "${NEW_PATHS[@]}"; do
    echo "  检查路径: $path"
    if [ ! -f "src/$path/index.js" ] && [ ! -f "src/$path.js" ]; then
        echo "    ❌ 路径不存在: src/$path"
        ((BROKEN_IMPORTS++))
    else
        echo "    ✅ 路径正常"
    fi
done

echo "断开的导入路径: $BROKEN_IMPORTS 个"
```

#### 0.3 运行完整验证
```bash
echo "🧪 运行完整验证套件..."

echo "📋 ESLint 检查..."
if yarn eslint --quiet; then
    echo "✅ ESLint 通过"
else
    echo "❌ ESLint 失败"
    exit 1
fi

echo "🧪 测试套件..."
if yarn test --watchAll=false --passWithNoTests; then
    echo "✅ 测试通过"
else
    echo "❌ 测试失败"
    exit 1
fi

echo "🚀 编译检查..."
if timeout 120 yarn start --verify-only; then
    echo "✅ 编译通过"
else
    echo "❌ 编译失败或超时"
    exit 1
fi

echo "🎉 当前状态验证完成!"
echo "  - 旧路径导入: $OLD_IMPORTS 个"
echo "  - 断开导入: $BROKEN_IMPORTS 个"
echo "  - ESLint: 通过"
echo "  - 测试: 通过"
echo "  - 编译: 通过"
```

### 任务 1: 创建最终备份

#### 1.1 创建git分支作为备份 (增强版)
```bash
#!/bin/bash
# 创建备份分支脚本 (增强版)

LOG_FILE="UT-revamp-plan/phase3/backup-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔄 创建增强备份分支..."
echo "时间: $(date)"

# 获取当前分支名
CURRENT_BRANCH=$(git branch --show-current)
echo "当前分支: $CURRENT_BRANCH"

# 创建备份分支
BACKUP_BRANCH="utils-cleanup-backup-$(date +%Y%m%d-%H%M%S)"
echo "创建备份分支: $BACKUP_BRANCH"

git checkout -b "$BACKUP_BRANCH"
git add .
git commit -m "Create backup branch before utils cleanup - $(date)"

# 返回原分支
git checkout "$CURRENT_BRANCH"

echo "✅ 备份分支创建完成: $BACKUP_BRANCH"
echo "📝 快速回滚命令: git checkout $BACKUP_BRANCH -- src/utils src/common/utils"
```

#### 1.2 创建备份目录
```bash
#!/bin/bash
# 创建备份目录脚本

echo "📁 创建备份目录..."
BACKUP_DIR="UT-revamp-plan/phase3/backups"
mkdir -p "$BACKUP_DIR/utils-original/monitoring"
mkdir -p "$BACKUP_DIR/utils-compatibility"
echo "✅ 备份目录创建完成"
```

#### 1.3 备份原始工具函数文件
```bash
#!/bin/bash
# 备份原始工具函数文件脚本

BACKUP_DIR="UT-revamp-plan/phase3/backups"
echo "📦 备份原始工具函数文件..."

# 备份非监控相关文件
find src/utils -type f -name "*.js" -not -path "*/monitoring/*" | grep -v "test.js" | while read file; do
    cp "$file" "$BACKUP_DIR/utils-original/$(basename "$file")"
    echo "  已备份: $file"
done

# 备份监控相关文件
find src/utils/monitoring -type f -name "*.js" | grep -v "test.js" | while read file; do
    cp "$file" "$BACKUP_DIR/utils-original/monitoring/$(basename "$file")"
    echo "  已备份: $file"
done

echo "✅ 原始工具函数文件备份完成"
```

#### 1.4 备份兼容层文件
```bash
#!/bin/bash
# 备份兼容层文件脚本

BACKUP_DIR="UT-revamp-plan/phase3/backups"
echo "📦 备份兼容层文件..."

# 查找所有兼容层文件
find src/common/utils -type f -name "*.js" | grep -v "/tests/" | grep -v "/time/" | grep -v "/api/" | grep -v "/validation/" | grep -v "/ui/" | grep -v "/system/" | grep -v "/monitoring/" | grep -v "/constants/" | while read file; do
    target_path="$BACKUP_DIR/utils-compatibility/$(basename "$file")"
    cp "$file" "$target_path"
    echo "  已备份: $file -> $target_path"
done

echo "✅ 兼容层文件备份完成"
```

#### 1.5 验证备份完整性
```bash
#!/bin/bash
# 验证备份完整性脚本

BACKUP_DIR="UT-revamp-plan/phase3/backups"
echo "🔍 验证备份完整性..."

# 统计原始文件
ORIG_FILES=$(find src/utils -type f -name "*.js" | grep -v "test.js" | wc -l | tr -d ' ')
ORIG_BACKUP_FILES=$(find "$BACKUP_DIR/utils-original" -type f -name "*.js" | wc -l | tr -d ' ')

# 统计兼容层文件
COMPAT_FILES=$(find src/common/utils -type f -name "*.js" | grep -v "/tests/" | grep -v "/time/" | grep -v "/api/" | grep -v "/validation/" | grep -v "/ui/" | grep -v "/system/" | grep -v "/monitoring/" | grep -v "/constants/" | wc -l | tr -d ' ')
COMPAT_BACKUP_FILES=$(find "$BACKUP_DIR/utils-compatibility" -type f -name "*.js" | wc -l | tr -d ' ')

echo "📊 备份统计:"
echo "  原始工具函数文件数量: $ORIG_FILES"
echo "  备份的原始文件数量: $ORIG_BACKUP_FILES"
echo "  兼容层文件数量: $COMPAT_FILES"
echo "  备份的兼容层文件数量: $COMPAT_BACKUP_FILES"

# 验证备份是否完整
if [ "$ORIG_FILES" -eq "$ORIG_BACKUP_FILES" ] && [ "$COMPAT_FILES" -eq "$COMPAT_BACKUP_FILES" ]; then
    echo "✅ 备份验证成功: 所有文件已完整备份"
else
    echo "❌ 备份验证失败: 文件数量不匹配"
    echo "  请检查备份过程是否有错误"
fi
```

### 任务 2: 验证导入完整性检查 (新增)

#### 2.1 全面扫描导入状态
```bash
#!/bin/bash
# 导入完整性检查脚本

LOG_FILE="UT-revamp-plan/phase3/import-check-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔍 开始导入完整性检查..."
echo "时间: $(date)"

# 检查断开的导入
echo "🔗 检查断开的导入链接..."
BROKEN_COUNT=0

find src -name "*.js" -o -name "*.jsx" | while read -r file; do
    # 提取所有 from 导入
    grep -n "from ['\"]" "$file" 2>/dev/null | while read -r line; do
        import_path=$(echo "$line" | sed -n "s/.*from ['\"]\\([^'\"]*\\)['\"].*/\\1/p")

        # 检查相对路径导入
        if [[ "$import_path" == ./* ]] || [[ "$import_path" == ../* ]]; then
            # 解析相对路径
            dir=$(dirname "$file")
            resolved_path="$dir/$import_path"

            # 检查文件是否存在
            if [ ! -f "$resolved_path.js" ] && [ ! -f "$resolved_path/index.js" ] && [ ! -f "$resolved_path.jsx" ]; then
                echo "❌ 断开的导入: $file -> $import_path"
                ((BROKEN_COUNT++))
            fi
        fi
    done
done

echo "发现断开的导入: $BROKEN_COUNT 个"
```

#### 2.2 验证关键路径功能
```bash
echo "🎯 验证关键路径功能..."

# 关键功能模块列表
CRITICAL_MODULES=(
    "src/common/utils/constants"
    "src/common/utils/api/request"
    "src/common/utils/time"
    "src/common/utils/validation"
)

for module in "${CRITICAL_MODULES[@]}"; do
    echo "  检查模块: $module"
    if [ -d "$module" ]; then
        # 检查是否有 index.js
        if [ -f "$module/index.js" ]; then
            echo "    ✅ 模块结构正常"
        else
            echo "    ⚠️ 缺少 index.js"
        fi
    else
        echo "    ❌ 模块不存在"
    fi
done

echo "🧪 运行关键路径测试..."
yarn test --testPathPattern="(constants|request|time|validation)" --passWithNoTests
```

### 任务 3: 删除测试文件 (新增)

#### 3.1 创建测试文件清单
```bash
#!/bin/bash
# 创建测试文件清单脚本

echo "📝 创建测试文件清单..."
TEST_FILES_LIST="UT-revamp-plan/phase3/test-files-to-delete.txt"

# 查找所有要删除的测试文件
find src/utils -name "*.test.js" > "$TEST_FILES_LIST"

echo "✅ 测试文件清单创建完成: $TEST_FILES_LIST"
echo "  发现 $(wc -l < "$TEST_FILES_LIST" | tr -d ' ') 个测试文件"

# 预览文件列表
echo "📋 将要删除的测试文件预览:"
head -10 "$TEST_FILES_LIST"
if [ $(wc -l < "$TEST_FILES_LIST") -gt 10 ]; then
    echo "..."
fi
```

#### 3.2 分批删除测试文件
```bash
echo "🗑️ 开始删除测试文件..."

while read -r test_file; do
    if [ -f "$test_file" ]; then
        echo "  删除: $test_file"
        rm -f "$test_file"

        # 每删除5个文件验证一次
        if [ $((++deleted_count % 5)) -eq 0 ]; then
            echo "  🧪 运行验证..."
            yarn test --passWithNoTests --silent
        fi
    fi
done < "$TEST_FILES_LIST"

echo "✅ 测试文件删除完成"
git add .
git commit -m "Remove utils test files"
```

### 任务 4: 删除兼容层文件

#### 4.1 创建兼容层文件清单 (增强版)
```bash
#!/bin/bash
# 创建兼容层文件清单脚本 (增强版)

LOG_FILE="UT-revamp-plan/phase3/compat-cleanup-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "📝 创建兼容层文件清单..."
COMPAT_FILES_LIST="UT-revamp-plan/phase3/compatibility-files.txt"

# 查找所有兼容层文件
find src/common/utils -type f -name "*.js" | grep -v "/tests/" | grep -v "/time/" | grep -v "/api/" | grep -v "/validation/" | grep -v "/ui/" | grep -v "/system/" | grep -v "/monitoring/" | grep -v "/constants/" > "$COMPAT_FILES_LIST"

echo "✅ 兼容层文件清单创建完成: $COMPAT_FILES_LIST"
echo "  发现 $(wc -l < "$COMPAT_FILES_LIST" | tr -d ' ') 个兼容层文件"

# 预览将要删除的文件
echo "📋 将要删除的兼容层文件预览:"
head -10 "$COMPAT_FILES_LIST"
if [ $(wc -l < "$COMPAT_FILES_LIST") -gt 10 ]; then
    echo "... (还有 $(($(wc -l < "$COMPAT_FILES_LIST") - 10)) 个文件)"
fi

echo ""
echo "⏸️ 关键节点检查 - 请确认要删除这些兼容层文件"
echo "按 Enter 继续，或 Ctrl+C 中止..."
read -r
```

#### 4.2 分批删除兼容层文件 (增强版)
```bash
#!/bin/bash
# 分批删除兼容层文件脚本 (增强版)

echo "🗑️ 开始分批删除兼容层文件..."

# 按依赖关系排序的类别 (叶子节点优先)
declare -A categories=(
    ["ui"]="ui scroll-blocker prefetch-assets"
    ["validation"]="form-validate"
    ["system"]="poller store-utils"
    ["time"]="time-lib datetime-lib"
    ["api"]="request api-fetch"
    ["monitoring"]="logger utils"
)

for category in "${!categories[@]}"; do
    echo "🔶 处理类别: $category"

    for file in ${categories[$category]}; do
        # 查找匹配的兼容层文件
        files_to_delete=$(find src/common/utils -name "*${file}*.js" | grep -v "/tests/")

        if [ -n "$files_to_delete" ]; then
            echo "  将要删除的文件:"
            echo "$files_to_delete" | while read -r f; do
                echo "    - $f"
            done

            # 删除文件
            echo "$files_to_delete" | while read -r f; do
                rm -f "$f"
            done

            # 增强验证
            echo "  🧪 增强验证..."

            # 检查断开的导入
            echo "    🔗 检查断开导入..."
            BROKEN_IMPORTS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$file" 2>/dev/null | wc -l)
            if [ "$BROKEN_IMPORTS" -gt 0 ]; then
                echo "    ⚠️ 发现 $BROKEN_IMPORTS 个可能的断开导入"
                find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$file" 2>/dev/null | head -3
            else
                echo "    ✅ 无断开导入"
            fi

            # 运行关键路径测试
            echo "    🎯 关键路径测试..."
            yarn test --testPathPattern="critical" --passWithNoTests --silent

            # 标准验证
            echo "    📋 ESLint..."
            yarn eslint --quiet && echo "    ✅ ESLint 通过" || echo "    ❌ ESLint 失败"

            echo "    🚀 编译检查..."
            timeout 60 yarn start --verify-only && echo "    ✅ 编译通过" || echo "    ❌ 编译失败"

            # 提交更改
            git add .
            git commit -m "Remove $category compatibility layer files: $file"
            echo "  ✅ 更改已提交"

            # 暂停点
            echo "  ⏸️ 暂停点 - 验证功能是否正常"
            echo "  按 Enter 继续下一个文件，或 Ctrl+C 中止..."
            read -r
        else
            echo "  ⚠️ 未找到匹配的文件: $file"
        fi
    done

    echo "✅ 类别 $category 处理完成"
    echo ""
done

echo "🎉 所有兼容层文件删除完成"
```

### 任务 3: 删除原始工具函数文件

#### 3.1 创建原始文件清单
```bash
#!/bin/bash
# 创建原始文件清单脚本

echo "📝 创建原始文件清单..."
ORIG_FILES_LIST="UT-revamp-plan/phase3/original-files.txt"
ORIG_TEST_FILES_LIST="UT-revamp-plan/phase3/original-test-files.txt"

# 查找所有原始工具函数文件
find src/utils -type f -name "*.js" | grep -v "test.js" > "$ORIG_FILES_LIST"
find src/utils -type f -name "*.test.js" > "$ORIG_TEST_FILES_LIST"

echo "✅ 原始文件清单创建完成:"
echo "  - 工具函数文件: $ORIG_FILES_LIST ($(wc -l < "$ORIG_FILES_LIST" | tr -d ' ') 个文件)"
echo "  - 测试文件: $ORIG_TEST_FILES_LIST ($(wc -l < "$ORIG_TEST_FILES_LIST" | tr -d ' ') 个文件)"
```

#### 3.2 分批删除原始文件
```bash
#!/bin/bash
# 分批删除原始文件脚本

echo "🗑️ 开始分批删除原始工具函数文件..."

# 按类别分组删除
declare -A categories=(
    ["time"]="time-lib datetime-lib"
    ["api"]="request api-fetch"
    ["validation"]="form-validate"
    ["ui"]="ui scroll-blocker prefetch-assets"
    ["system"]="poller store-utils"
    ["monitoring"]="logger utils"
)

# 先删除测试文件
echo "🧪 删除测试文件..."
for category in "${!categories[@]}"; do
    echo "🔶 处理类别: $category"
    for file in ${categories[$category]}; do
        # 查找匹配的测试文件
        files_to_delete=$(find src/utils -name "*${file}*.test.js")
        
        if [ -n "$files_to_delete" ]; then
            echo "  删除测试文件:"
            echo "$files_to_delete" | while read -r f; do
                echo "    - $f"
                rm -f "$f"
            done
            
            # 验证删除后的功能
            echo "  🧪 验证功能..."
            yarn test && echo "    ✅ 测试通过" || echo "    ❌ 测试失败"
            
            # 提交更改
            git add .
            git commit -m "Remove $category test files: $file"
            echo "  ✅ 更改已提交"
        else
            echo "  ⚠️ 未找到匹配的测试文件: $file"
        fi
    done
    
    echo "✅ 类别 $category 测试文件处理完成"
    echo ""
done

# 再删除实现文件
echo "📄 删除实现文件..."
for category in "${!categories[@]}"; do
    echo "🔶 处理类别: $category"
    for file in ${categories[$category]}; do
        # 查找匹配的实现文件
        files_to_delete=$(find src/utils -name "*${file}*.js" | grep -v "test.js")
        
        if [ -n "$files_to_delete" ]; then
            echo "  删除实现文件:"
            echo "$files_to_delete" | while read -r f; do
                echo "    - $f"
                rm -f "$f"
            done
            
            # 验证删除后的功能
            echo "  🧪 验证功能..."
            yarn eslint && echo "    ✅ ESLint 通过" || echo "    ❌ ESLint 失败"
            yarn test && echo "    ✅ 测试通过" || echo "    ❌ 测试失败"
            yarn start --verify-only && echo "    ✅ 编译通过" || echo "    ❌ 编译失败"
            
            # 提交更改
            git add .
            git commit -m "Remove $category implementation files: $file"
            echo "  ✅ 更改已提交"
        else
            echo "  ⚠️ 未找到匹配的实现文件: $file"
        fi
    done
    
    echo "✅ 类别 $category 实现文件处理完成"
    echo ""
done

echo "🎉 所有原始工具函数文件删除完成"
```

### 任务 4: 清理空目录和最终验证

#### 4.1 检查并删除空目录
```bash
#!/bin/bash
# 检查并删除空目录脚本

echo "🔍 检查空目录..."
EMPTY_DIRS=$(find src/utils -type d -empty)

if [ -n "$EMPTY_DIRS" ]; then
    echo "发现以下空目录:"
    echo "$EMPTY_DIRS"
    
    echo "🗑️ 删除空目录..."
    find src/utils -type d -empty -delete
    echo "✅ 空目录已删除"
else
    echo "✅ 没有发现空目录"
fi
```

#### 7.2 创建快速回滚脚本
```bash
#!/bin/bash
# 创建快速回滚脚本

ROLLBACK_SCRIPT="UT-revamp-plan/phase3/quick-rollback.sh"

cat > "$ROLLBACK_SCRIPT" << 'EOF'
#!/bin/bash
# 快速回滚脚本 - 紧急情况使用

echo "🔄 执行快速回滚..."
echo "⚠️ 这将恢复所有已删除的 utils 文件"

# 获取备份分支名
BACKUP_BRANCH=$(git branch -a | grep "utils-cleanup-backup" | head -1 | sed 's/^[* ]*//')

if [ -z "$BACKUP_BRANCH" ]; then
    echo "❌ 未找到备份分支"
    exit 1
fi

echo "从备份分支恢复: $BACKUP_BRANCH"

# 恢复文件
git checkout "$BACKUP_BRANCH" -- src/utils src/common/utils

echo "✅ 回滚完成"
echo "🧪 建议运行验证: yarn start"
EOF

chmod +x "$ROLLBACK_SCRIPT"
echo "✅ 快速回滚脚本已创建: $ROLLBACK_SCRIPT"
```

#### 7.3 最终验证 (增强版)
```bash
#!/bin/bash
# 最终验证脚本 (增强版)

LOG_FILE="UT-revamp-plan/phase3/final-verification-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🧪 执行最终验证..."
echo "时间: $(date)"

# 检查清理完成度
echo "� 清理完成度检查..."
REMAINING_UTILS=$(find src/utils -name "*.js" 2>/dev/null | wc -l)
REMAINING_COMPAT=$(find src/common/utils -name "*.js" | grep -v "/tests/" | grep -v "/time/" | grep -v "/api/" | grep -v "/validation/" | grep -v "/ui/" | grep -v "/system/" | grep -v "/monitoring/" | grep -v "/constants/" | wc -l)

echo "  剩余原始 utils 文件: $REMAINING_UTILS 个"
echo "  剩余兼容层文件: $REMAINING_COMPAT 个"

# 全面验证
echo "�📋 运行 ESLint..."
if yarn eslint --quiet; then
    echo "✅ ESLint 通过"
else
    echo "❌ ESLint 失败"
    exit 1
fi

echo "🧪 运行完整测试套件..."
if yarn test --watchAll=false --coverage --passWithNoTests; then
    echo "✅ 测试通过"
else
    echo "❌ 测试失败"
    exit 1
fi

echo "🚀 验证应用编译..."
if timeout 180 yarn start --verify-only; then
    echo "✅ 编译通过"
else
    echo "❌ 编译失败或超时"
    exit 1
fi

echo "⚡ 性能检查..."
echo "  检查打包大小..."
yarn build --analyze > /dev/null 2>&1 && echo "✅ 打包成功" || echo "⚠️ 打包检查跳过"

echo "🎉 最终验证全部通过！"
echo ""
echo "📊 清理总结:"
echo "  - 删除的原始文件: $(find UT-revamp-plan/phase3/backups/utils-original -name "*.js" | wc -l) 个"
echo "  - 删除的兼容层文件: $(find UT-revamp-plan/phase3/backups/utils-compatibility -name "*.js" | wc -l) 个"
echo "  - 剩余 utils 文件: $REMAINING_UTILS 个"
echo "  - 剩余兼容层文件: $REMAINING_COMPAT 个"
echo ""
echo "🔧 回滚方式: ./UT-revamp-plan/phase3/quick-rollback.sh"
```

#### 4.3 提交最终更改
```bash
#!/bin/bash
# 提交最终更改脚本

echo "📝 提交最终更改..."
git add .
git commit -m "Clean up empty directories after utils cleanup"
echo "✅ 最终更改已提交"

echo "📊 清理任务总结:"
echo "  - 已删除所有兼容层文件"
echo "  - 已删除所有原始工具函数文件"
echo "  - 已清理所有空目录"
echo "  - 所有验证测试通过"
echo ""
echo "🎉 Utils 清理任务全部完成！"
```
