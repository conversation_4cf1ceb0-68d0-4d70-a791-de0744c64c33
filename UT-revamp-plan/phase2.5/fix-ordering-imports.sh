#!/bin/bash
# 修复 ordering 目录导入脚本

LOG_FILE="UT-revamp-plan/phase3/fix-ordering-imports-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔧 开始修复 ordering 目录的导入..."
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 获取所有需要修复的 ordering 文件
ORDERING_FILES=$(find src/ordering -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring")

echo "📋 需要修复的文件数量: $(echo "$ORDERING_FILES" | wc -l)"

echo ""
echo "🔄 开始批量替换..."

# 执行替换
for file in $ORDERING_FILES; do
    if [ -f "$file" ]; then
        echo "  🔧 处理文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 执行替换 - constants 相关
        sed -i.tmp "s|from '../../utils/constants'|from '../../common/utils/constants'|g" "$file"
        sed -i.tmp "s|from '../utils/constants'|from '../../common/utils/constants'|g" "$file"
        sed -i.tmp "s|from '../../../utils/constants'|from '../../common/utils/constants'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/constants'|from '../../common/utils/constants'|g" "$file"
        sed -i.tmp "s|from '../../../../../utils/constants'|from '../../common/utils/constants'|g" "$file"
        
        # history 相关
        sed -i.tmp "s|from '../utils/history'|from '../../common/utils/system/history'|g" "$file"
        sed -i.tmp "s|from '../../utils/history'|from '../../common/utils/system/history'|g" "$file"
        sed -i.tmp "s|from '../../../utils/history'|from '../../common/utils/system/history'|g" "$file"
        
        # utils 相关
        sed -i.tmp "s|from '../../utils/utils'|from '../../common/utils/system/utils'|g" "$file"
        sed -i.tmp "s|from '../../../utils/utils'|from '../../common/utils/system/utils'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/utils'|from '../../common/utils/system/utils'|g" "$file"
        
        # API 请求相关
        sed -i.tmp "s|from '../../utils/api-request'|from '../../common/utils/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../utils/api-request'|from '../../common/utils/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/api-request'|from '../../common/utils/api/request'|g" "$file"
        
        # 时间相关
        sed -i.tmp "s|from '../../utils/datetime-lib'|from '../../common/utils/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from '../../../utils/datetime-lib'|from '../../common/utils/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/datetime-lib'|from '../../common/utils/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from '../../../../../utils/datetime-lib'|from '../../common/utils/time/datetime-lib'|g" "$file"
        
        # 表单验证相关
        sed -i.tmp "s|from '../../utils/form-validate'|from '../../common/utils/validation/form-validate'|g" "$file"
        sed -i.tmp "s|from '../../../utils/form-validate'|from '../../common/utils/validation/form-validate'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/form-validate'|from '../../common/utils/validation/form-validate'|g" "$file"
        
        # 其他常见工具函数
        sed -i.tmp "s|from '../../../utils/request'|from '../../common/utils/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/request'|from '../../common/utils/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../utils/time-lib'|from '../../common/utils/time/time-lib'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/time-lib'|from '../../common/utils/time/time-lib'|g" "$file"
        
        # 清理临时文件
        rm -f "$file.tmp"
        
        # 检查是否有变化
        if ! diff -q "$file" "$file.backup" > /dev/null 2>&1; then
            echo "    ✅ 文件已更新"
        else
            echo "    ⚠️ 文件无变化"
        fi
        
        # 删除备份文件
        rm -f "$file.backup"
    fi
done

echo ""
echo "🧪 验证修复结果..."

# 检查是否还有旧路径导入
REMAINING_OLD_IMPORTS=$(find src/ordering -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)

echo "剩余的旧路径导入: $REMAINING_OLD_IMPORTS 个"

if [ "$REMAINING_OLD_IMPORTS" -gt 0 ]; then
    echo "⚠️ 仍有未处理的导入:"
    find src/ordering -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | head -5
fi

echo ""
echo "📋 运行 ESLint 检查 ordering 目录..."
if yarn eslint src/ordering --quiet; then
    echo "✅ ordering 目录 ESLint 通过"
else
    echo "⚠️ ordering 目录仍有 ESLint 错误"
fi

echo ""
echo "✅ ordering 目录导入修复完成！"
