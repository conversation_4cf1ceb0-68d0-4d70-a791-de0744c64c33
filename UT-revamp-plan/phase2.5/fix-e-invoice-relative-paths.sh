#!/bin/bash
# 修复 e-invoice 目录相对路径脚本

LOG_FILE="UT-revamp-plan/phase3/fix-e-invoice-relative-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔧 修复 e-invoice 目录的相对路径..."
echo "时间: $(date)"

# 获取所有包含 'common/utils/constants' 的文件
FILES_WITH_ABSOLUTE_IMPORTS=$(find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep -l "from 'common/utils" 2>/dev/null)

echo "📋 需要修复相对路径的文件:"
echo "$FILES_WITH_ABSOLUTE_IMPORTS" | sed 's/^/  - /'

echo ""
echo "🔄 开始修复相对路径..."

for file in $FILES_WITH_ABSOLUTE_IMPORTS; do
    if [ -f "$file" ]; then
        echo "  🔧 处理文件: $file"
        
        # 计算相对路径深度
        depth=$(echo "$file" | sed 's|src/e-invoice/||' | tr '/' '\n' | wc -l)
        depth=$((depth - 1))
        
        # 构建相对路径前缀
        relative_prefix=""
        for ((i=0; i<depth; i++)); do
            relative_prefix="../$relative_prefix"
        done
        
        echo "    深度: $depth, 相对路径前缀: $relative_prefix"
        
        # 备份文件
        cp "$file" "$file.backup"
        
        # 替换绝对路径为相对路径
        sed -i.tmp "s|from 'common/utils/constants'|from '${relative_prefix}../common/utils/constants'|g" "$file"
        sed -i.tmp "s|from 'common/utils/system/history'|from '${relative_prefix}../common/utils/system/history'|g" "$file"
        sed -i.tmp "s|from 'common/utils/system/utils'|from '${relative_prefix}../common/utils/system/utils'|g" "$file"
        sed -i.tmp "s|from 'common/utils/api/request'|from '${relative_prefix}../common/utils/api/request'|g" "$file"
        sed -i.tmp "s|from 'common/utils/time/datetime-lib'|from '${relative_prefix}../common/utils/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from 'common/utils/validation/form-validate'|from '${relative_prefix}../common/utils/validation/form-validate'|g" "$file"
        
        # 清理临时文件
        rm -f "$file.tmp"
        
        # 检查变化
        if ! diff -q "$file" "$file.backup" > /dev/null 2>&1; then
            echo "    ✅ 文件已更新"
        else
            echo "    ⚠️ 文件无变化"
        fi
        
        # 删除备份
        rm -f "$file.backup"
    fi
done

echo ""
echo "🧪 验证修复结果..."

# 检查剩余的绝对路径导入
REMAINING_ABSOLUTE=$(find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep -n "from 'common/utils" 2>/dev/null | wc -l)
echo "剩余的绝对路径导入: $REMAINING_ABSOLUTE 个"

if [ "$REMAINING_ABSOLUTE" -gt 0 ]; then
    echo "⚠️ 仍有绝对路径导入:"
    find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep -n "from 'common/utils" 2>/dev/null | head -3
fi

echo ""
echo "📋 运行 ESLint 检查 e-invoice 目录..."
if yarn eslint src/e-invoice --quiet; then
    echo "✅ e-invoice 目录 ESLint 通过"
else
    echo "⚠️ e-invoice 目录仍有 ESLint 错误，但应该减少了"
fi

echo ""
echo "✅ e-invoice 相对路径修复完成！"
