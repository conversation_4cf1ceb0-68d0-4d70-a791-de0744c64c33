#!/bin/bash
# 快速回滚脚本 - 最终版本

echo "🔄 开始快速回滚到清理前状态..."

# 获取备份分支名称
BACKUP_BRANCH=$(git branch | grep "backup-before-utils-cleanup" | head -1 | sed 's/^[* ]*//')

if [ -z "$BACKUP_BRANCH" ]; then
    echo "❌ 未找到备份分支，无法回滚"
    exit 1
fi

echo "📋 找到备份分支: $BACKUP_BRANCH"

# 确认回滚
read -p "确认要回滚到备份分支吗？这将丢失所有清理工作 (y/N): " confirm
if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "❌ 回滚已取消"
    exit 0
fi

# 保存当前状态
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
echo "💾 保存当前状态到分支: cleanup-state-$(date +%Y%m%d-%H%M%S)"
git checkout -b "cleanup-state-$(date +%Y%m%d-%H%M%S)"
git add .
git commit -m "保存清理后的状态" || true

# 回滚到备份分支
echo "🔄 回滚到备份分支..."
git checkout "$BACKUP_BRANCH"

# 创建新的工作分支
NEW_BRANCH="rollback-$(date +%Y%m%d-%H%M%S)"
echo "🌿 创建新的工作分支: $NEW_BRANCH"
git checkout -b "$NEW_BRANCH"

echo "✅ 回滚完成！"
echo "  📋 当前分支: $NEW_BRANCH"
echo "  📋 基于备份: $BACKUP_BRANCH"
echo "  📋 清理状态已保存到: cleanup-state-*"
