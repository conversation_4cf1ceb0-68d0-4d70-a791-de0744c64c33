#!/bin/bash
# 建议的修复命令

echo "🔧 执行建议的导入路径修复..."

# 1. 修复constants导入
echo "1. 修复constants导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/constants'|from 'common/utils/constants'|g" {} \;

# 2. 修复api-request导入
echo "2. 修复api-request导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/api-request'|from 'common/utils/api/request'|g" {} \;
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/request'|from 'common/utils/api/request'|g" {} \;

# 3. 修复time相关导入
echo "3. 修复time相关导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/datetime-lib'|from 'common/utils/time/datetime-lib'|g" {} \;
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/time-lib'|from 'common/utils/time/time-lib'|g" {} \;

# 4. 修复history导入
echo "4. 修复history导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/history'|from 'common/utils/system/history'|g" {} \;

# 5. 修复form相关导入
echo "5. 修复form相关导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/form-validate'|from 'common/utils/validation/form-validate'|g" {} \;

# 清理备份文件
echo "清理备份文件..."
find src -name "*.bak" -delete

echo "✅ 修复完成！请运行测试验证。"
