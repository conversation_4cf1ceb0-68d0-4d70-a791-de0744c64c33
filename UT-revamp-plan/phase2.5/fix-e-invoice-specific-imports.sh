#!/bin/bash
# 修复 e-invoice 特有常量导入脚本

LOG_FILE="UT-revamp-plan/phase3/fix-e-invoice-specific-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔧 修复 e-invoice 特有常量导入..."
echo "时间: $(date)"

# e-invoice 特有的常量列表
E_INVOICE_CONSTANTS=(
    "E_INVOICE_STATUS_TIMEOUT"
    "E_INVOICE_APP_CONTAINER_ID"
    "E_INVOICE_TYPES"
    "E_INVOICE_STATUS"
    "E_INVOICE_BLOCK_ERROR_CODES"
    "GET_E_INVOICE_STATUS_ERROR_CODES"
    "POST_E_INVOICE_ERROR_CODES"
    "SUBMITTED_TIMEOUT_ERROR_MESSAGE"
    "MALAYSIA_STATES"
    "COUNTRIES"
    "CLASSIFICATIONS"
    "SPECIAL_FIELD_NAMES"
    "SEARCH_RADIO_LIST_INPUT_DEFAULT_FOCUS_DELAY"
    "PAGE_ROUTES"
    "PATHS"
    "GET_E_INVOICE_ERROR_CODES"
    "STATUS_TAG_COLORS"
    "STATUS_I18N_KEYS"
    "E_INVOICE_DOCUMENT_TYPES"
)

# 获取所有有ESLint错误的e-invoice文件
FILES_WITH_ERRORS=$(find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*common/utils/constants" 2>/dev/null)

echo "📋 需要修复的文件:"
echo "$FILES_WITH_ERRORS" | sed 's/^/  - /'

echo ""
echo "🔄 开始修复特有常量导入..."

for file in $FILES_WITH_ERRORS; do
    if [ -f "$file" ]; then
        echo "  🔧 处理文件: $file"
        
        # 计算到 e-invoice/utils/constants.js 的相对路径
        depth=$(echo "$file" | sed 's|src/e-invoice/||' | tr '/' '\n' | wc -l)
        depth=$((depth - 1))
        
        # 构建相对路径
        relative_prefix=""
        for ((i=0; i<depth; i++)); do
            relative_prefix="../$relative_prefix"
        done
        
        e_invoice_constants_path="${relative_prefix}utils/constants"
        
        echo "    E-invoice constants 路径: $e_invoice_constants_path"
        
        # 备份文件
        cp "$file" "$file.backup"
        
        # 读取文件内容
        file_content=$(cat "$file")
        
        # 检查文件中使用了哪些e-invoice特有的常量
        used_e_invoice_constants=()
        for const in "${E_INVOICE_CONSTANTS[@]}"; do
            if grep -q "$const" "$file"; then
                used_e_invoice_constants+=("$const")
            fi
        done
        
        if [ ${#used_e_invoice_constants[@]} -gt 0 ]; then
            echo "    使用的e-invoice常量: ${used_e_invoice_constants[*]}"
            
            # 构建新的导入语句
            e_invoice_import_list=$(IFS=', '; echo "${used_e_invoice_constants[*]}")
            
            # 移除这些常量从通用constants的导入
            for const in "${used_e_invoice_constants[@]}"; do
                # 从现有导入中移除这个常量
                sed -i.tmp "s/, $const//g" "$file"
                sed -i.tmp "s/$const, //g" "$file"
                sed -i.tmp "s/{ $const }/{ }/g" "$file"
                sed -i.tmp "s/import { $const } from/import { } from/g" "$file"
            done
            
            # 清理空的导入语句
            sed -i.tmp "/import { } from.*common\/utils\/constants/d" "$file"
            sed -i.tmp "/import {  } from.*common\/utils\/constants/d" "$file"
            
            # 添加新的e-invoice constants导入
            # 在文件开头添加导入
            temp_file=$(mktemp)
            echo "import { $e_invoice_import_list } from '$e_invoice_constants_path';" > "$temp_file"
            cat "$file" >> "$temp_file"
            mv "$temp_file" "$file"
            
            # 清理临时文件
            rm -f "$file.tmp"
            
            echo "    ✅ 文件已更新"
        else
            echo "    ⚠️ 文件未使用e-invoice特有常量"
        fi
        
        # 删除备份
        rm -f "$file.backup"
    fi
done

echo ""
echo "🧪 验证修复结果..."

# 运行ESLint检查e-invoice目录
echo "📋 运行 ESLint 检查 e-invoice 目录..."
if yarn eslint src/e-invoice --quiet; then
    echo "✅ e-invoice 目录 ESLint 通过"
else
    echo "⚠️ e-invoice 目录仍有一些 ESLint 错误，但应该大幅减少"
fi

echo ""
echo "✅ e-invoice 特有常量导入修复完成！"
