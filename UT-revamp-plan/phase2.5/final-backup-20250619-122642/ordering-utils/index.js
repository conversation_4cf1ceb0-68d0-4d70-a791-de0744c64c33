import { SSE_SUBSCRIPTION_GROUP_PATHNAMES, SSE_SUBSCRIPTION_GROUP_TOPICS, SSE_SUBSCRIPTION_GROUPS } from './constants';

export const getIsEnableSSE = (isPayLaterEnabled, currentLocationPathname) => {
  const isOrderedPathname = SSE_SUBSCRIPTION_GROUP_PATHNAMES[SSE_SUBSCRIPTION_GROUPS.ORDERED].some(pathname =>
    pathname.includes(currentLocationPathname)
  );

  if (isOrderedPathname) {
    return true;
  }

  if (isPayLaterEnabled) {
    return true;
  }

  return false;
};

export const registerAddListeners = (pathname, groupCallbacks) => {
  const groupName = Object.keys(SSE_SUBSCRIPTION_GROUP_PATHNAMES).find(pathnameKey =>
    SSE_SUBSCRIPTION_GROUP_PATHNAMES[pathnameKey].includes(pathname)
  );

  if (!groupName || !SSE_SUBSCRIPTION_GROUP_TOPICS[groupName]) {
    return;
  }

  const groupCallback = groupCallbacks[groupName];
  groupCallback();
  window.addEventListener('sh-location-change', groupCallback);
};

export const getSSESubscriptionQueryParams = (pathname, { merchantName, storeId, tableId, receiptNumber } = {}) => {
  const topicKey = Object.keys(SSE_SUBSCRIPTION_GROUP_PATHNAMES).find(pathnameKey =>
    SSE_SUBSCRIPTION_GROUP_PATHNAMES[pathnameKey].includes(pathname)
  );

  if (!topicKey || !SSE_SUBSCRIPTION_GROUP_TOPICS[topicKey]) {
    return null;
  }

  let subscriberGroups = '';

  if (topicKey === SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING) {
    subscriberGroups = `${merchantName}:${storeId}:${tableId}`;
  } else if (topicKey === SSE_SUBSCRIPTION_GROUPS.ORDERED) {
    subscriberGroups = `${merchantName}:${receiptNumber}`;
  }

  return { topics: SSE_SUBSCRIPTION_GROUP_TOPICS[topicKey], subscriberGroups };
};
