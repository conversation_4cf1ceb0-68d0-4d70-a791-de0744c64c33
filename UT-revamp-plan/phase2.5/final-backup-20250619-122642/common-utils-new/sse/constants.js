export const SSE_DEFAULT_QUERY_PARAMS = {
  app: 'BeepWeb',
  subscriber: '',
};

export const SSE_EVENT_NAMES = {
  SHOPPING_CART_UPDATED: 'PayLaterShoppingCartUpdated',
  ORDER_UPDATED: 'OrderUpdated',
};

export const SSE_TOPICS = {
  PAY_LATER_SHOPPING_CART_UPDATED: '1',
  ORDER_UPDATED: '2',
};

export const SSE_ERROR_TYPES = {
  SERVICE_CLOSED: 'Service_Closed',
  TOPICS_REQUIRED: 'Topics_Required',
  TOPICS_INVALID: 'Topics_Invalid',
  STORE_ID_REQUIRED: 'Store_Id_Required',
  TABLE_ID_REQUIRED: 'Table_Id_Required',
  MAX_RECONNECTION_REACHED: 'Max_Reconnection_Reached',
};

export const SSE_CONNECT_SOURCES = {
  DEFAULT: 'default',
  RECONNECT: 'reconnect',
  READY_STATE_RECONNECT: 'ready_state_reconnect',
  VISIBLE: 'visible',
};

export const SSE_CLOSE_SOURCES = {
  DEFAULT: 'default',
  RECONNECT: 'reconnect',
  HIDDEN: 'hidden',
  READY_STATE_CLOSED: 'ready_state_closed',
  ERROR: 'error',
};
