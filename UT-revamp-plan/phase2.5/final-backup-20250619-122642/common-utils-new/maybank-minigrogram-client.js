import logger from '../../utils/monitoring/logger';
import { isMaybankMiniProgram } from './index';

const TIME_OUT_DURATION = 2000;

function triggerDeepLink(deepLinkUrl) {
  return new Promise((resolve, reject) => {
    // set timeout duration
    const timeout = setTimeout(() => {
      reject(new Error('Deeplink failed or was not handled.'));
    }, TIME_OUT_DURATION);

    // listen page to change visibility
    document.addEventListener('visibilitychange', function onVisibilityChange() {
      if (document.visibilityState === 'hidden') {
        clearTimeout(timeout);
        resolve('Success');
        document.removeEventListener('visibilitychange', onVisibilityChange);
      }
    });

    // request Deeplink
    window.location.href = deepLinkUrl;
  });
}

export const callMaybankDeepLinkPay = async paymentUrl => {
  try {
    if (!isMaybankMiniProgram()) {
      throw new Error('Not in Maybank Mini Program.');
    }

    const result = await triggerDeepLink(paymentUrl);

    return result;
  } catch (error) {
    logger.error('Common_MaybankMiniProgram_callMaybankDeepLinkPay', {
      message: error.message,
    });

    throw error;
  }
};
