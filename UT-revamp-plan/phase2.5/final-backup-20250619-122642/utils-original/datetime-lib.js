// Compatibility layer: Re-export from new location
// This file maintains backward compatibility while the actual implementation
// has been moved to src/common/utils/time/datetime-lib.js

export {
  standardizeLocale,
  formatTimeToDateString,
  getDateTimeFormatter,
  padZero,
  isValidDate,
  toLocaleString,
  toLocaleDateString,
  toLocaleTimeString,
  toNumericTime,
  toNumericTimeRange,
  toDayDateMonth,
  toISODateString,
  formatToDeliveryTime,
  formatPickupTime,
  addTime,
  isSameTime,
  getDifferenceInMilliseconds,
  getDifferenceTodayInDays,
  getIsAfterDateTime,
  getIsMidnight,
  getReduceOneSecondForDate,
  getFormatLocaleDateTime,
  getSwitchFormatDate,
  getDateISOString,
} from '../common/utils/time/datetime-lib';
