// Compatibility layer: Re-export from new location
// This file maintains backward compatibility while the actual implementation
// has been moved to src/common/utils/time/time-lib.js

export {
  padZero,
  isValidTime,
  parse,
  stringify,
  toMinutes,
  minutesToTime,
  getAmountOfMinutes,
  add,
  minus,
  isBefore,
  isAfter,
  isSame,
  isSameOrBefore,
  isSameOrAfter,
  ceilToHour,
  floorToHour,
  ceilToQuarter,
  isBetween,
  isToday,
  setDateTime,
  getTimeFromDayjs,
  formatTo12hour,
  formatTime,
  getLocaleTimeTo24hour,
} from '../common/utils/time/time-lib';
