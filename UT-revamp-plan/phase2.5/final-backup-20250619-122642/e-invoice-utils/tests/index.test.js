import { isEInvoicePathname } from '../index';

// Generated by CodiumAI

describe('isEInvoicePathname', () => {
  // Returns true for pathname starting with '/e-invoice'
  it('should return true when pathname starts with "/e-invoice"', () => {
    const pathname = '/e-invoice/some-path';
    const result = isEInvoicePathname(pathname);
    expect(result).toBe(true);
  });

  // Handles empty string as pathname
  it('should return false when pathname is an empty string', () => {
    const pathname = '';
    const result = isEInvoicePathname(pathname);
    expect(result).toBe(false);
  });
});
