#!/bin/bash

# 修复 constants 导入路径错误
# 问题：脚本计算相对路径深度有误，需要手动修复

echo "🔧 开始修复 constants 导入路径错误"
echo "时间: $(date)"

# 修复函数
fix_path() {
    local file="$1"
    local wrong_path="$2"
    local correct_path="$3"
    
    echo "🔧 修复文件: $file"
    echo "   错误路径: $wrong_path"
    echo "   正确路径: $correct_path"
    
    sed -i '' "s|from '$wrong_path'|from '$correct_path'|g" "$file"
    sed -i '' "s|from \"$wrong_path\"|from \"$correct_path\"|g" "$file"
}

# E-invoice 路径修复
echo "📁 修复 src/e-invoice/ 路径..."

# components 层级 (需要 ../../../)
fix_path "src/e-invoice/components/BillingAddress/index.jsx" "../../common/utils/constants" "../../../common/utils/constants"
fix_path "src/e-invoice/components/SubmissionProcess/index.jsx" "../../common/utils/constants" "../../../common/utils/constants"
fix_path "src/e-invoice/components/TransactionDetails/index.jsx" "../../common/utils/constants" "../../../common/utils/constants"

# containers 层级 (需要 ../../../)
fix_path "src/e-invoice/containers/App/index.jsx" "../../common/utils/constants" "../../../common/utils/constants"
fix_path "src/e-invoice/containers/Routes.jsx" "../common/utils/constants" "../../common/utils/constants"
fix_path "src/e-invoice/containers/EInvoiceCategory/index.jsx" "../../common/utils/constants" "../../../common/utils/constants"
fix_path "src/e-invoice/containers/Invalid/index.jsx" "../../common/utils/constants" "../../../common/utils/constants"
fix_path "src/e-invoice/containers/EInvoice/index.jsx" "../../common/utils/constants" "../../../../common/utils/constants"

# EInvoice 子目录
fix_path "src/e-invoice/containers/EInvoice/utils/constants.js" "../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/EInvoice/components/Content/index.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/EInvoice/components/ErrorResult/index.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/EInvoice/redux/selectors.js" "../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/EInvoice/redux/thunks.js" "../../../common/utils/constants" "../../../../../common/utils/constants"

# Business 子目录
fix_path "src/e-invoice/containers/Business/containers/Form/index.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Business/containers/Preview/index.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Business/containers/Preview/redux/selectors.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Business/containers/Preview/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Business/redux/submission/selector.js" "../../../../common/utils/constants" "../../../../../common/utils/constants"

# Consumer 子目录
fix_path "src/e-invoice/containers/Consumer/containers/Form/index.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Form/MalaysianForm.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Form/NonMalaysianForm.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Form/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Preview/MalaysianPreview.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Preview/NonMalaysianPreview.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Preview/redux/selectors.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/containers/Preview/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/redux/common/selector.js" "../../../../common/utils/constants" "../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/redux/submission/malaysian/selector.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/e-invoice/containers/Consumer/redux/submission/nonMalaysian/selector.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"

# redux 层级
fix_path "src/e-invoice/redux/modules/common/selectors.js" "../../../common/utils/constants" "../../../../common/utils/constants"
fix_path "src/e-invoice/redux/modules/common/thunks.js" "../../../common/utils/constants" "../../../../common/utils/constants"

echo "📁 修复 src/rewards/ 路径..."

# Rewards 路径修复
fix_path "src/rewards/containers/Routes.jsx" "../common/utils/constants" "../../common/utils/constants"

# Business 子目录 - 深层路径修复
fix_path "src/rewards/containers/Business/containers/SeamlessLoyalty/index.jsx" "../../../../common/utils/constants" "../../../../../common/utils/constants"

# Redux thunks 路径修复 (都需要 ../../../../../../)
fix_path "src/rewards/containers/Business/containers/CashbackCreditsHistory/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/ClaimUniquePromo/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/MembershipDetailV2/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/MembershipForm/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/PointsHistory/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/PointsRewardDetail/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/PointsRewardsPage/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/SeamlessLoyalty/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/UniquePromoDetail/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"
fix_path "src/rewards/containers/Business/containers/UniquePromoListPage/redux/thunks.js" "../../../../../common/utils/constants" "../../../../../../common/utils/constants"

# Redux modules
fix_path "src/rewards/redux/modules/common/selectors.js" "../../../common/utils/constants" "../../../../common/utils/constants"

echo "🎉 路径修复完成!"
echo "时间: $(date)"

# 验证结果
echo "🔍 验证修复结果..."
error_count=$(find src/e-invoice src/rewards -name "*.js" -o -name "*.jsx" | xargs grep "common/utils/constants" 2>/dev/null | grep -E "(\.\./)*(common/utils/constants)" | wc -l)
echo "包含 common/utils/constants 的导入: $error_count 个"

echo "✅ 修复脚本执行完成!"
