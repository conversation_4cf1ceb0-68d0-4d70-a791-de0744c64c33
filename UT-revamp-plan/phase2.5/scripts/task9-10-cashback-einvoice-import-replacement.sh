#!/bin/bash

# Task 9 & 10: src/cashback/ + src/e-invoice/ 导入路径替换脚本
# 目标: 同时替换两个文件夹中的导入路径
# Task 9: src/cashback/ (40个文件，25个包含utils导入)
# Task 10: src/e-invoice/ (63个文件，44个包含utils导入)

set -e

echo "🚀 开始执行 Task 9 & 10: src/cashback/ + src/e-invoice/ 导入路径替换"
echo "时间: $(date)"
echo "=========================================="

# 1. 创建备份目录
BACKUP_DIR_9="UT-revamp-plan/phase2.5/backups/task9-cashback"
BACKUP_DIR_10="UT-revamp-plan/phase2.5/backups/task10-e-invoice"
echo "📦 创建备份目录: $BACKUP_DIR_9 和 $BACKUP_DIR_10"
mkdir -p "$BACKUP_DIR_9"
mkdir -p "$BACKUP_DIR_10"

# 2. 备份所有相关文件
echo "📋 备份 src/cashback/ 中的所有文件..."
find src/cashback -name "*.js" -o -name "*.jsx" | while read file; do
    backup_path="$BACKUP_DIR_9/${file#src/cashback/}"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
    echo "  备份: $file → $backup_path"
done

echo "📋 备份 src/e-invoice/ 中的所有文件..."
find src/e-invoice -name "*.js" -o -name "*.jsx" | while read file; do
    backup_path="$BACKUP_DIR_10/${file#src/e-invoice/}"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
    echo "  备份: $file → $backup_path"
done

echo ""
echo "🔍 分析需要替换的导入路径..."

# 3. 查找需要替换的导入路径
echo "当前的 utils 导入 (src/cashback/):"
find src/cashback -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null | head -10 || true
echo "... (显示前10行)"

echo ""
echo "当前的 utils 导入 (src/e-invoice/):"
find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null | head -10 || true
echo "... (显示前10行)"

echo ""
echo "🔧 开始替换导入路径..."

# 4. 定义替换函数
replace_imports_in_dir() {
    local dir=$1
    local dir_name=$2
    
    echo "  处理 $dir_name 目录..."
    
    # 替换 constants 导入路径
    echo "    替换 constants 导入路径..."
    find "$dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*common/utils/constants" "$file"; then
            echo "      文件 $file 已经是新路径，跳过"
        elif grep -q "from.*utils/constants" "$file"; then
            echo "      处理文件: $file"
            # 根据文件深度调整路径
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                sed -i.bak "s|from '../utils/constants'|from '../common/utils/constants'|g" "$file"
            elif [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/constants'|from '../../common/utils/constants'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/constants'|from '../../../common/utils/constants'|g" "$file"
            elif [ $depth -eq 5 ]; then
                sed -i.bak "s|from '../../../../utils/constants'|from '../../../../common/utils/constants'|g" "$file"
            fi
        fi
    done
    
    # 替换 datetime-lib 导入路径
    echo "    替换 datetime-lib 导入路径..."
    find "$dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/datetime-lib" "$file"; then
            echo "      处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/datetime-lib'|from '../../common/utils/time/datetime-lib'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/datetime-lib'|from '../../../common/utils/time/datetime-lib'|g" "$file"
            fi
        fi
    done
    
    # 替换 time-lib 导入路径
    echo "    替换 time-lib 导入路径..."
    find "$dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/time-lib" "$file"; then
            echo "      处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/time-lib'|from '../../common/utils/time/time-lib'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/time-lib'|from '../../../common/utils/time/time-lib'|g" "$file"
            fi
        fi
    done
    
    # 替换 form-validate 导入路径
    echo "    替换 form-validate 导入路径..."
    find "$dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/form-validate" "$file"; then
            echo "      处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/form-validate'|from '../../common/utils/validation/form-validate'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/form-validate'|from '../../../common/utils/validation/form-validate'|g" "$file"
            fi
        fi
    done
    
    # 替换 request 导入路径
    echo "    替换 request 导入路径..."
    find "$dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/request" "$file"; then
            echo "      处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/request'|from '../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/request'|from '../../../common/utils/api/request'|g" "$file"
            fi
        fi
    done
}

# 5. 执行替换
replace_imports_in_dir "src/cashback" "src/cashback"
replace_imports_in_dir "src/e-invoice" "src/e-invoice"

# 6. 清理备份文件
echo "  清理临时备份文件..."
find src/cashback -name "*.bak" -delete
find src/e-invoice -name "*.bak" -delete

echo ""
echo "✅ Task 9 & 10 导入路径替换完成"
echo "时间: $(date)"
echo "=========================================="

# 7. 验证结果
echo "🔍 验证结果..."
echo "剩余的 utils 导入 (src/cashback/):"
find src/cashback -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null | head -5 || echo "  无剩余需要替换的 utils 导入"

echo ""
echo "剩余的 utils 导入 (src/e-invoice/):"
find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null | head -5 || echo "  无剩余需要替换的 utils 导入"

echo ""
echo "📊 Task 9 & 10 执行完成！"
echo "请运行以下命令进行验证:"
echo "  yarn test --testPathPattern=\"src/(cashback|e-invoice)\" --passWithNoTests"
echo "  yarn eslint"
echo "  yarn start  # 验证应用编译和启动正常"
