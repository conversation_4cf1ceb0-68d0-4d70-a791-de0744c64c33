#!/bin/bash

# Task 6 验证脚本
# 用于验证 src/stores/ 导入路径替换后的状态

set -e

echo "🔍 Task 6 验证开始"
echo "时间: $(date)"
echo "=========================================="

# 1. ESLint 检查
echo "📋 运行 ESLint 检查整个项目..."
yarn eslint

echo ""

# 2. 运行测试
echo "🧪 运行相关测试..."
yarn test --testPathPattern="src/stores" --passWithNoTests

echo ""
echo "✅ Task 6 验证完成"
echo "时间: $(date)"
echo "=========================================="
