#!/bin/bash

# 修复重复导入问题的脚本

set -e

echo "🚀 开始修复重复导入问题"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
echo ""

# 创建备份目录
BACKUP_DIR="UT-revamp-plan/phase2.5/backups/duplicate-imports-fix"
echo "💾 创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 获取所有有重复导入错误的文件
echo "🔍 查找有重复导入错误的文件..."
DUPLICATE_IMPORT_FILES=(
  "src/ordering/containers/Customer/containers/AddressDetail/index.jsx"
  "src/ordering/containers/Menu/constants.js"
  "src/ordering/containers/PageLogin/redux/selectors.js"
  "src/ordering/containers/Routes.jsx"
  "src/ordering/containers/order-status/containers/StoreReview/constants.js"
  "src/ordering/containers/order-status/containers/StoreReview/redux/thunks.js"
  "src/ordering/containers/order-status/containers/TableSummary/index.jsx"
  "src/ordering/containers/order-status/containers/ThankYou/redux/thunks.js"
  "src/ordering/containers/order-status/redux/selector.js"
  "src/ordering/containers/payments/components/PaymentCardBrands/index.jsx"
  "src/ordering/containers/payments/containers/Stripe/CheckoutForm.jsx"
  "src/ordering/containers/payments/redux/common/thunks/create-order.js"
  "src/ordering/containers/rewards/containers/RewardDetail/redux/thunks.js"
  "src/ordering/containers/rewards/containers/RewardList/components/TicketList/index.jsx"
  "src/ordering/containers/shopping-cart/containers/Cart/PayFirst.jsx"
  "src/ordering/redux/modules/app.js"
  "src/ordering/redux/modules/cart/constants.js"
)

echo "找到 ${#DUPLICATE_IMPORT_FILES[@]} 个需要修复的文件"
echo ""

# 备份所有需要修复的文件
echo "📦 备份需要修复的文件..."
for file in "${DUPLICATE_IMPORT_FILES[@]}"; do
  if [ -f "$file" ]; then
    # 创建目录结构
    backup_file="$BACKUP_DIR/$file"
    backup_dir=$(dirname "$backup_file")
    mkdir -p "$backup_dir"
    
    # 备份文件
    cp "$file" "$backup_file"
    echo "  备份: $file -> $backup_file"
  else
    echo "  ⚠️  文件不存在: $file"
  fi
done

echo "✅ 备份完成"
echo ""

# 修复函数
fix_duplicate_imports() {
  local file="$1"
  echo "🔧 修复文件: $file"
  
  if [ ! -f "$file" ]; then
    echo "  ❌ 文件不存在，跳过"
    return
  fi
  
  # 检查是否有重复导入
  if ! grep -q "import.*common/utils/constants" "$file"; then
    echo "  ℹ️  文件没有 constants 导入，跳过"
    return
  fi
  
  # 创建临时文件
  temp_file=$(mktemp)
  
  # 处理文件，合并重复的导入
  python3 -c "
import re
import sys

def fix_duplicate_imports(content):
    lines = content.split('\n')
    
    # 找到所有 constants 导入
    constants_imports = []
    other_lines = []
    
    for line in lines:
        if re.search(r'from [\'\"]\.\./.*common/utils/constants[\'\"]\s*;?\s*$', line):
            # 提取导入的内容
            if 'import {' in line:
                # 命名导入
                match = re.search(r'import\s*\{\s*([^}]+)\s*\}', line)
                if match:
                    imports = [imp.strip() for imp in match.group(1).split(',')]
                    constants_imports.extend(imports)
            elif 'import ' in line and ' from ' in line:
                # 默认导入
                match = re.search(r'import\s+(\w+)\s+from', line)
                if match:
                    constants_imports.append(f'default as {match.group(1)}')
        else:
            other_lines.append(line)
    
    if not constants_imports:
        return content
    
    # 去重并排序
    unique_imports = list(set(constants_imports))
    unique_imports.sort()
    
    # 分离默认导入和命名导入
    default_imports = [imp for imp in unique_imports if imp.startswith('default as ')]
    named_imports = [imp for imp in unique_imports if not imp.startswith('default as ')]
    
    # 构建新的导入语句
    import_lines = []
    
    if named_imports:
        if len(named_imports) <= 3:
            import_line = f'import {{ {', '.join(named_imports)} }} from \'../../../common/utils/constants\';'
        else:
            import_line = 'import {\\n  ' + ',\\n  '.join(named_imports) + '\\n} from \'../../../common/utils/constants\';'
        import_lines.append(import_line)
    
    if default_imports:
        for default_import in default_imports:
            alias = default_import.replace('default as ', '')
            import_lines.append(f'import {alias} from \'../../../common/utils/constants\';')
    
    # 找到第一个非导入行的位置
    insert_pos = 0
    for i, line in enumerate(other_lines):
        if line.strip() and not line.strip().startswith('import ') and not line.strip().startswith('//') and not line.strip().startswith('/*') and not line.strip().startswith('*'):
            insert_pos = i
            break
    
    # 插入新的导入语句
    result_lines = other_lines[:insert_pos] + import_lines + other_lines[insert_pos:]
    
    return '\\n'.join(result_lines)

# 读取文件内容
with open('$file', 'r', encoding='utf-8') as f:
    content = f.read()

# 修复重复导入
fixed_content = fix_duplicate_imports(content)

# 写入临时文件
with open('$temp_file', 'w', encoding='utf-8') as f:
    f.write(fixed_content)
"
  
  # 如果 Python 脚本成功执行，替换原文件
  if [ $? -eq 0 ]; then
    mv "$temp_file" "$file"
    echo "  ✅ 修复完成"
  else
    echo "  ❌ 修复失败"
    rm -f "$temp_file"
  fi
}

# 修复所有文件
echo "🔄 开始修复重复导入..."
for file in "${DUPLICATE_IMPORT_FILES[@]}"; do
  fix_duplicate_imports "$file"
done

echo ""
echo "📊 修复完成！"
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
echo ""
echo "请运行以下命令进行验证:"
echo "  yarn eslint"
echo "  yarn start"
