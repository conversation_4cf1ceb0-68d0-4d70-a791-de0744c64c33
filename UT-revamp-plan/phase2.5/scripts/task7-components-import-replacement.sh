#!/bin/bash

# Task 7: src/components/ 导入路径替换脚本
# 目标: 替换 src/components/ 文件夹中的导入路径 (26个文件，8个包含utils导入)

set -e

echo "🚀 开始执行 Task 7: src/components/ 导入路径替换"
echo "时间: $(date)"
echo "=========================================="

# 1. 创建备份目录
BACKUP_DIR="UT-revamp-plan/phase2.5/backups/task7-components"
echo "📦 创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 2. 备份所有相关文件
echo "📋 备份 src/components/ 中的所有文件..."
find src/components -name "*.js" -o -name "*.jsx" | while read file; do
    backup_path="$BACKUP_DIR/${file#src/components/}"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
    echo "  备份: $file → $backup_path"
done

echo ""
echo "🔍 分析需要替换的导入路径..."

# 3. 查找需要替换的导入路径
echo "当前的 utils 导入:"
find src/components -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null || true

echo ""
echo "🔧 开始替换导入路径..."

# 4. 替换已迁移文件的导入路径
echo "  替换 constants 导入路径..."
find src/components -name "*.js" -o -name "*.jsx" | while read file; do
    if grep -q "from.*utils/constants" "$file"; then
        echo "    处理文件: $file"
        sed -i.bak "s|from '../utils/constants'|from '../common/utils/constants'|g" "$file"
        sed -i.bak "s|from '../../utils/constants'|from '../../common/utils/constants'|g" "$file"
        sed -i.bak "s|from '../../../utils/constants'|from '../../../common/utils/constants'|g" "$file"
    fi
done

echo "  替换 monitoring/logger 导入路径..."
find src/components -name "*.js" -o -name "*.jsx" | while read file; do
    if grep -q "from.*utils/monitoring/logger" "$file"; then
        echo "    处理文件: $file"
        sed -i.bak "s|from '../utils/monitoring/logger'|from '../common/utils/monitoring/logger'|g" "$file"
        sed -i.bak "s|from '../../utils/monitoring/logger'|from '../../common/utils/monitoring/logger'|g" "$file"
        sed -i.bak "s|from '../../../utils/monitoring/logger'|from '../../../common/utils/monitoring/logger'|g" "$file"
    fi
done

# 5. 清理备份文件
echo "  清理临时备份文件..."
find src/components -name "*.bak" -delete

echo ""
echo "✅ Task 7 导入路径替换完成"
echo "时间: $(date)"
echo "=========================================="

# 6. 验证结果
echo "🔍 验证结果..."
echo "剩余的 utils 导入:"
find src/components -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null || echo "  无剩余需要替换的 utils 导入"

echo ""
echo "📊 Task 7 执行完成！"
echo "请运行以下命令进行验证:"
echo "  yarn test --testPathPattern=\"src/components\" --passWithNoTests"
echo "  yarn eslint"
echo "  yarn start  # 验证应用编译和启动正常"
