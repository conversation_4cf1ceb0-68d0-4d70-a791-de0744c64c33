#!/bin/bash

# 修复导入路径的脚本
# 用于修复Task 1中文件移动后的导入路径问题

echo "开始修复导入路径..."

# 1. 修复 poller 的导入路径
echo "修复 poller 导入路径..."
find src -name "*.js" -o -name "*.jsx" | xargs grep -l "common/utils/poller" | while read file; do
    echo "修复文件: $file"
    sed -i.bak "s|common/utils/poller|common/utils/system/poller|g" "$file"
done

# 2. 修复 prefetch-assets 的导入路径
echo "修复 prefetch-assets 导入路径..."
find src -name "*.js" -o -name "*.jsx" | xargs grep -l "common/utils/prefetch-assets" | while read file; do
    echo "修复文件: $file"
    sed -i.bak "s|common/utils/prefetch-assets|common/utils/ui/prefetch-assets|g" "$file"
done

# 3. 清理备份文件
echo "清理备份文件..."
find src -name "*.bak" -delete

echo "导入路径修复完成！"
