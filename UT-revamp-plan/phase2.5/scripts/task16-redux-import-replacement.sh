#!/bin/bash

# Task 16: src/redux/ 导入路径替换脚本

set -e

echo "🚀 开始执行 Task 16: src/redux/ 导入路径替换"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
echo ""

# 检查目标目录
TARGET_DIR="src/redux"
if [ ! -d "$TARGET_DIR" ]; then
    echo "❌ 错误: 目录 $TARGET_DIR 不存在"
    exit 1
fi

# 统计信息
TOTAL_FILES=$(find "$TARGET_DIR" -name "*.js" -o -name "*.jsx" | wc -l | tr -d ' ')
UTILS_FILES=$(find "$TARGET_DIR" -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*utils/" 2>/dev/null | wc -l | tr -d ' ')

echo "📊 任务统计:"
echo "  - 总文件数: $TOTAL_FILES"
echo "  - 包含utils导入的文件: $UTILS_FILES"
echo ""

# 创建备份目录
BACKUP_DIR="UT-revamp-plan/phase2.5/backups/task16-redux"
echo "💾 创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 备份整个目录
echo "📦 备份 $TARGET_DIR 到 $BACKUP_DIR..."
cp -r "$TARGET_DIR"/* "$BACKUP_DIR/"
echo "✅ 备份完成"
echo ""

# 分析当前的导入情况
echo "🔍 分析当前的导入情况..."
echo "当前的 utils 导入 (前20行):"
find "$TARGET_DIR" -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null | head -20 || true
echo ""

# 导入路径替换函数
replace_imports() {
    local target_dir="$1"
    echo "🔄 开始替换导入路径..."
    
    # 1. constants 导入路径替换
    echo "  📝 替换 constants 导入路径..."
    find "$target_dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*common/utils/constants" "$file"; then
            echo "    文件 $file 已经是新路径，跳过"
        elif grep -q "from.*utils/constants" "$file"; then
            echo "    处理文件: $file"
            # 根据文件深度调整路径
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                # src/redux/file.js
                sed -i.bak "s|from '../utils/constants'|from '../common/utils/constants'|g" "$file"
            elif [ $depth -eq 3 ]; then
                # src/redux/modules/file.js
                sed -i.bak "s|from '../../utils/constants'|from '../../common/utils/constants'|g" "$file"
            elif [ $depth -eq 4 ]; then
                # src/redux/modules/subfolder/file.js
                sed -i.bak "s|from '../../../utils/constants'|from '../../../common/utils/constants'|g" "$file"
            elif [ $depth -eq 5 ]; then
                # src/redux/modules/subfolder/subfolder/file.js
                sed -i.bak "s|from '../../../../utils/constants'|from '../../../../common/utils/constants'|g" "$file"
            elif [ $depth -eq 6 ]; then
                # src/redux/modules/subfolder/subfolder/subfolder/file.js
                sed -i.bak "s|from '../../../../../utils/constants'|from '../../../../../common/utils/constants'|g" "$file"
            fi
        fi
    done
    
    # 2. time-lib 导入路径替换
    echo "  ⏰ 替换 time-lib 导入路径..."
    find "$target_dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/time-lib" "$file"; then
            echo "    处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                sed -i.bak "s|from '../utils/time-lib'|from '../common/utils/time/time-lib'|g" "$file"
            elif [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/time-lib'|from '../../common/utils/time/time-lib'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/time-lib'|from '../../../common/utils/time/time-lib'|g" "$file"
            elif [ $depth -eq 5 ]; then
                sed -i.bak "s|from '../../../../utils/time-lib'|from '../../../../common/utils/time/time-lib'|g" "$file"
            elif [ $depth -eq 6 ]; then
                sed -i.bak "s|from '../../../../../utils/time-lib'|from '../../../../../common/utils/time/time-lib'|g" "$file"
            fi
        fi
    done
    
    # 3. datetime-lib 导入路径替换
    echo "  📅 替换 datetime-lib 导入路径..."
    find "$target_dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/datetime-lib" "$file"; then
            echo "    处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                sed -i.bak "s|from '../utils/datetime-lib'|from '../common/utils/time/datetime-lib'|g" "$file"
            elif [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/datetime-lib'|from '../../common/utils/time/datetime-lib'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/datetime-lib'|from '../../../common/utils/time/datetime-lib'|g" "$file"
            elif [ $depth -eq 5 ]; then
                sed -i.bak "s|from '../../../../utils/datetime-lib'|from '../../../../common/utils/time/datetime-lib'|g" "$file"
            elif [ $depth -eq 6 ]; then
                sed -i.bak "s|from '../../../../../utils/datetime-lib'|from '../../../../../common/utils/time/datetime-lib'|g" "$file"
            fi
        fi
    done
    
    # 4. form-validate 导入路径替换
    echo "  ✅ 替换 form-validate 导入路径..."
    find "$target_dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/form-validate" "$file"; then
            echo "    处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                sed -i.bak "s|from '../utils/form-validate'|from '../common/utils/validation/form-validate'|g" "$file"
            elif [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/form-validate'|from '../../common/utils/validation/form-validate'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/form-validate'|from '../../../common/utils/validation/form-validate'|g" "$file"
            elif [ $depth -eq 5 ]; then
                sed -i.bak "s|from '../../../../utils/form-validate'|from '../../../../common/utils/validation/form-validate'|g" "$file"
            elif [ $depth -eq 6 ]; then
                sed -i.bak "s|from '../../../../../utils/form-validate'|from '../../../../../common/utils/validation/form-validate'|g" "$file"
            fi
        fi
    done
    
    # 5. request 导入路径替换
    echo "  🌐 替换 request 导入路径..."
    find "$target_dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/request" "$file"; then
            echo "    处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                sed -i.bak "s|from '../utils/request'|from '../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/request'|from '../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/request'|from '../../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 5 ]; then
                sed -i.bak "s|from '../../../../utils/request'|from '../../../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 6 ]; then
                sed -i.bak "s|from '../../../../../utils/request'|from '../../../../../common/utils/api/request'|g" "$file"
            fi
        fi
    done
    
    # 6. api/api-fetch 导入路径替换 (替换为 request)
    echo "  🔗 替换 api/api-fetch 导入路径..."
    find "$target_dir" -name "*.js" -o -name "*.jsx" | while read file; do
        if grep -q "from.*utils/api/api-fetch" "$file"; then
            echo "    处理文件: $file"
            depth=$(echo "$file" | tr -cd '/' | wc -c)
            if [ $depth -eq 2 ]; then
                sed -i.bak "s|from '../utils/api/api-fetch'|from '../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 3 ]; then
                sed -i.bak "s|from '../../utils/api/api-fetch'|from '../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 4 ]; then
                sed -i.bak "s|from '../../../utils/api/api-fetch'|from '../../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 5 ]; then
                sed -i.bak "s|from '../../../../utils/api/api-fetch'|from '../../../../common/utils/api/request'|g" "$file"
            elif [ $depth -eq 6 ]; then
                sed -i.bak "s|from '../../../../../utils/api/api-fetch'|from '../../../../../common/utils/api/request'|g" "$file"
            fi
        fi
    done
    
    echo "✅ 导入路径替换完成"
}

# 执行替换
replace_imports "$TARGET_DIR"

echo ""

# 清理临时文件
echo "🧹 清理临时文件..."
find "$TARGET_DIR" -name "*.bak" -delete 2>/dev/null || true
echo "✅ 清理完成"

echo ""
echo "📊 Task 16 执行完成！"
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
echo ""
echo "请运行以下命令进行验证:"
echo "  yarn test --testPathPattern=\"src/redux\" --passWithNoTests"
echo "  yarn eslint"
echo "  yarn start  # 验证应用编译和启动正常"
echo ""
echo "如需检查剩余的 utils 导入:"
echo "  find src/redux -name \"*.js\" -o -name \"*.jsx\" | xargs grep \"from.*utils/\" 2>/dev/null"
