#!/bin/bash

# Task 13 修复脚本: 修复剩余的路径问题
# 处理那些被错误替换的路径

set -e

echo "🔧 开始修复 Task 13 剩余的路径问题"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
echo ""

TARGET_DIR="src/ordering"

# 修复错误的路径替换
echo "🔄 修复错误的路径替换..."

# 1. 修复 ../../../common/utils/constants -> ../../../common/utils/constants (这个路径是正确的，但需要检查)
# 2. 修复 ../../common/utils/constants -> ../../common/utils/constants (这个路径是正确的，但需要检查)
# 3. 修复 ../common/utils/constants -> ../common/utils/constants (这个路径是错误的)

# 让我们手动修复这些特定的文件
echo "  📝 修复具体的路径问题..."

# 修复 src/ordering/containers/App/index.jsx
if [ -f "src/ordering/containers/App/index.jsx" ]; then
    echo "    修复 src/ordering/containers/App/index.jsx"
    # 这个文件的路径实际上是正确的，问题可能在于文件结构
    # 从 src/ordering/containers/App/ 到 src/common/utils/constants 应该是 ../../../common/utils/constants
    # 但是让我们检查实际的文件结构
fi

# 修复 src/ordering/containers/PageLogin/redux/selectors.js
if [ -f "src/ordering/containers/PageLogin/redux/selectors.js" ]; then
    echo "    修复 src/ordering/containers/PageLogin/redux/selectors.js"
    # 从 src/ordering/containers/PageLogin/redux/ 到 src/common/utils/constants 应该是 ../../../../common/utils/constants
    sed -i.bak "s|from '../common/utils/constants'|from '../../../../common/utils/constants'|g" "src/ordering/containers/PageLogin/redux/selectors.js"
fi

# 修复 src/ordering/containers/Profile/ProfileFields/index.jsx
if [ -f "src/ordering/containers/Profile/ProfileFields/index.jsx" ]; then
    echo "    修复 src/ordering/containers/Profile/ProfileFields/index.jsx"
    # 从 src/ordering/containers/Profile/ProfileFields/ 到 src/common/utils/constants 应该是 ../../../../common/utils/constants
    sed -i.bak "s|from '../common/utils/constants'|from '../../../../common/utils/constants'|g" "src/ordering/containers/Profile/ProfileFields/index.jsx"
fi

# 修复 src/ordering/containers/Profile/redux/thunk.js
if [ -f "src/ordering/containers/Profile/redux/thunk.js" ]; then
    echo "    修复 src/ordering/containers/Profile/redux/thunk.js"
    # 从 src/ordering/containers/Profile/redux/ 到 src/common/utils/constants 应该是 ../../../../common/utils/constants
    sed -i.bak "s|from '../common/utils/constants'|from '../../../../common/utils/constants'|g" "src/ordering/containers/Profile/redux/thunk.js"
fi

# 修复 src/ordering/containers/Routes.jsx
if [ -f "src/ordering/containers/Routes.jsx" ]; then
    echo "    修复 src/ordering/containers/Routes.jsx"
    # 从 src/ordering/containers/ 到 src/common/utils/constants 应该是 ../../common/utils/constants
    sed -i.bak "s|from '../common/utils/constants'|from '../../common/utils/constants'|g" "src/ordering/containers/Routes.jsx"
fi

# 修复 src/ordering/containers/order-status/containers/TableSummary/index.jsx
if [ -f "src/ordering/containers/order-status/containers/TableSummary/index.jsx" ]; then
    echo "    修复 src/ordering/containers/order-status/containers/TableSummary/index.jsx"
    # 从 src/ordering/containers/order-status/containers/TableSummary/ 到 src/common/utils/constants 应该是 ../../../../../common/utils/constants
    sed -i.bak "s|from '../../../../common/utils/constants'|from '../../../../../common/utils/constants'|g" "src/ordering/containers/order-status/containers/TableSummary/index.jsx"
fi

# 修复 src/ordering/containers/payments/components/PaymentCardBrands/index.jsx
if [ -f "src/ordering/containers/payments/components/PaymentCardBrands/index.jsx" ]; then
    echo "    修复 src/ordering/containers/payments/components/PaymentCardBrands/index.jsx"
    # 从 src/ordering/containers/payments/components/PaymentCardBrands/ 到 src/common/utils/constants 应该是 ../../../../../common/utils/constants
    sed -i.bak "s|from '../../common/utils/constants'|from '../../../../../common/utils/constants'|g" "src/ordering/containers/payments/components/PaymentCardBrands/index.jsx"
fi

# 修复 src/ordering/containers/payments/containers/Stripe/CheckoutForm.jsx
if [ -f "src/ordering/containers/payments/containers/Stripe/CheckoutForm.jsx" ]; then
    echo "    修复 src/ordering/containers/payments/containers/Stripe/CheckoutForm.jsx"
    # 从 src/ordering/containers/payments/containers/Stripe/ 到 src/common/utils/constants 应该是 ../../../../../common/utils/constants
    sed -i.bak "s|from '../../common/utils/constants'|from '../../../../../common/utils/constants'|g" "src/ordering/containers/payments/containers/Stripe/CheckoutForm.jsx"
fi

# 修复 src/ordering/containers/payments/redux/common/thunks/create-order.js
if [ -f "src/ordering/containers/payments/redux/common/thunks/create-order.js" ]; then
    echo "    修复 src/ordering/containers/payments/redux/common/thunks/create-order.js"
    # 从 src/ordering/containers/payments/redux/common/thunks/ 到 src/common/utils/constants 应该是 ../../../../../../common/utils/constants
    sed -i.bak "s|from '../../../common/utils/constants'|from '../../../../../../common/utils/constants'|g" "src/ordering/containers/payments/redux/common/thunks/create-order.js"
fi

# 修复 src/ordering/containers/rewards/containers/RewardDetail/redux/thunks.js
if [ -f "src/ordering/containers/rewards/containers/RewardDetail/redux/thunks.js" ]; then
    echo "    修复 src/ordering/containers/rewards/containers/RewardDetail/redux/thunks.js"
    # 从 src/ordering/containers/rewards/containers/RewardDetail/redux/ 到 src/common/utils/constants 应该是 ../../../../../../common/utils/constants
    sed -i.bak "s|from '../../../common/utils/constants'|from '../../../../../../common/utils/constants'|g" "src/ordering/containers/rewards/containers/RewardDetail/redux/thunks.js"
fi

# 修复 src/ordering/containers/rewards/containers/RewardList/components/TicketList/index.jsx
if [ -f "src/ordering/containers/rewards/containers/RewardList/components/TicketList/index.jsx" ]; then
    echo "    修复 src/ordering/containers/rewards/containers/RewardList/components/TicketList/index.jsx"
    # 这个文件有两个导入需要修复
    # 从 src/ordering/containers/rewards/containers/RewardList/components/TicketList/ 到 src/common/utils/constants 应该是 ../../../../../../../common/utils/constants
    sed -i.bak "s|from '../../../../../../common/utils/constants'|from '../../../../../../../common/utils/constants'|g" "src/ordering/containers/rewards/containers/RewardList/components/TicketList/index.jsx"
    sed -i.bak "s|from '../../../../common/utils/constants'|from '../../../../../../../common/utils/constants'|g" "src/ordering/containers/rewards/containers/RewardList/components/TicketList/index.jsx"
fi

# 修复 src/ordering/containers/shopping-cart/containers/Cart/PayFirst.jsx
if [ -f "src/ordering/containers/shopping-cart/containers/Cart/PayFirst.jsx" ]; then
    echo "    修复 src/ordering/containers/shopping-cart/containers/Cart/PayFirst.jsx"
    # 从 src/ordering/containers/shopping-cart/containers/Cart/ 到 src/common/utils/constants 应该是 ../../../../../common/utils/constants
    sed -i.bak "s|from '../../../../common/utils/constants'|from '../../../../../common/utils/constants'|g" "src/ordering/containers/shopping-cart/containers/Cart/PayFirst.jsx"
fi

# 修复 src/ordering/redux/modules/app.js
if [ -f "src/ordering/redux/modules/app.js" ]; then
    echo "    修复 src/ordering/redux/modules/app.js"
    # 从 src/ordering/redux/modules/ 到 src/common/utils/constants 应该是 ../../../common/utils/constants
    sed -i.bak "s|from '../../common/utils/constants'|from '../../../common/utils/constants'|g" "src/ordering/redux/modules/app.js"
fi

# 修复 src/ordering/redux/modules/cart/constants.js
if [ -f "src/ordering/redux/modules/cart/constants.js" ]; then
    echo "    修复 src/ordering/redux/modules/cart/constants.js"
    # 从 src/ordering/redux/modules/cart/ 到 src/common/utils/constants 应该是 ../../../../common/utils/constants
    sed -i.bak "s|from '../../../common/utils/constants'|from '../../../../common/utils/constants'|g" "src/ordering/redux/modules/cart/constants.js"
fi

echo "✅ 路径修复完成"

echo ""

# 清理备份文件
echo "🧹 清理备份文件..."
find "$TARGET_DIR" -name "*.bak" -delete 2>/dev/null || true
echo "✅ 清理完成"

echo ""
echo "📊 Task 13 路径修复完成！"
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S %Z')"
echo ""
echo "请运行以下命令进行验证:"
echo "  yarn start  # 验证应用编译和启动正常"
