#!/bin/bash

# Task 8: src/user/ 导入路径替换脚本
# 目标: 替换 src/user/ 文件夹中的导入路径 (40个文件，19个包含utils导入)

set -e

echo "🚀 开始执行 Task 8: src/user/ 导入路径替换"
echo "时间: $(date)"
echo "=========================================="

# 1. 创建备份目录
BACKUP_DIR="UT-revamp-plan/phase2.5/backups/task8-user"
echo "📦 创建备份目录: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 2. 备份所有相关文件
echo "📋 备份 src/user/ 中的所有文件..."
find src/user -name "*.js" -o -name "*.jsx" | while read file; do
    backup_path="$BACKUP_DIR/${file#src/user/}"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
    echo "  备份: $file → $backup_path"
done

echo ""
echo "🔍 分析需要替换的导入路径..."

# 3. 查找需要替换的导入路径
echo "当前的 utils 导入:"
find src/user -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null || true

echo ""
echo "🔧 开始替换导入路径..."

# 4. 替换已迁移文件的导入路径

echo "  替换 constants 导入路径..."
find src/user -name "*.js" -o -name "*.jsx" | while read file; do
    if grep -q "from.*common/utils/constants" "$file"; then
        echo "    文件 $file 已经是新路径，跳过"
    elif grep -q "from.*utils/constants" "$file"; then
        echo "    处理文件: $file"
        # 根据文件深度调整路径
        depth=$(echo "$file" | tr -cd '/' | wc -c)
        if [ $depth -eq 2 ]; then
            # src/user/file.js
            sed -i.bak "s|from '../utils/constants'|from '../common/utils/constants'|g" "$file"
        elif [ $depth -eq 3 ]; then
            # src/user/containers/file.js
            sed -i.bak "s|from '../../utils/constants'|from '../../common/utils/constants'|g" "$file"
        elif [ $depth -eq 4 ]; then
            # src/user/redux/modules/file.js
            sed -i.bak "s|from '../../../utils/constants'|from '../../../common/utils/constants'|g" "$file"
        fi
    fi
done

echo "  替换 datetime-lib 导入路径..."
find src/user -name "*.js" -o -name "*.jsx" | while read file; do
    if grep -q "from.*utils/datetime-lib" "$file"; then
        echo "    处理文件: $file"
        depth=$(echo "$file" | tr -cd '/' | wc -c)
        if [ $depth -eq 4 ]; then
            sed -i.bak "s|from '../../../utils/datetime-lib'|from '../../../common/utils/time/datetime-lib'|g" "$file"
        fi
    fi
done

echo "  替换 api/api-fetch 导入路径..."
find src/user -name "*.js" -o -name "*.jsx" | while read file; do
    if grep -q "from.*utils/api/api-fetch" "$file"; then
        echo "    处理文件: $file"
        depth=$(echo "$file" | tr -cd '/' | wc -c)
        if [ $depth -eq 4 ]; then
            sed -i.bak "s|from '../../../utils/api/api-fetch'|from '../../../common/utils/api/request'|g" "$file"
        fi
    fi
done

# 5. 清理备份文件
echo "  清理临时备份文件..."
find src/user -name "*.bak" -delete

echo ""
echo "✅ Task 8 导入路径替换完成"
echo "时间: $(date)"
echo "=========================================="

# 6. 验证结果
echo "🔍 验证结果..."
echo "剩余的 utils 导入:"
find src/user -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null || echo "  无剩余需要替换的 utils 导入"

echo ""
echo "📊 Task 8 执行完成！"
echo "请运行以下命令进行验证:"
echo "  yarn test --testPathPattern=\"src/user\" --passWithNoTests"
echo "  yarn eslint"
echo "  yarn start  # 验证应用编译和启动正常"
