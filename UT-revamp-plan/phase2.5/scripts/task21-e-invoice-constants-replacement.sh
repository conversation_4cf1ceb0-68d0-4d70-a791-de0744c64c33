#!/bin/bash

# Task 21: src/e-invoice/ constants 导入路径替换
# 专门替换 constants 导入路径

echo "🚀 开始执行 Task 21: src/e-invoice/ constants 导入路径替换"
echo "时间: $(date)"

# 创建备份
echo "📦 创建备份..."
mkdir -p UT-revamp-plan/phase2.5/backups/task21-e-invoice
cp -r src/e-invoice UT-revamp-plan/phase2.5/backups/task21-e-invoice/

# 查找需要替换的文件
echo "🔍 查找需要替换的文件..."
files_to_process=$(find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*utils/constants" 2>/dev/null | grep -v "common/utils/constants" || true)

if [ -z "$files_to_process" ]; then
    echo "✅ 没有找到需要替换的文件"
    exit 0
fi

echo "📋 找到以下文件需要处理:"
echo "$files_to_process"

# 替换函数
replace_constants_imports() {
    local file="$1"
    echo "🔧 处理文件: $file"
    
    # 计算相对路径深度
    local depth=$(echo "$file" | sed 's|src/e-invoice/||' | tr -cd '/' | wc -c)
    local relative_path=""
    for ((i=0; i<depth; i++)); do
        relative_path="../$relative_path"
    done
    
    # 替换 constants 导入路径
    sed -i '' "s|from '${relative_path}utils/constants'|from '${relative_path}common/utils/constants'|g" "$file"
    sed -i '' "s|from \"${relative_path}utils/constants\"|from \"${relative_path}common/utils/constants\"|g" "$file"
    
    echo "✅ 完成处理: $file"
}

# 处理每个文件
echo "$files_to_process" | while read -r file; do
    if [ -n "$file" ]; then
        replace_constants_imports "$file"
    fi
done

echo "🎉 Task 21 完成!"
echo "时间: $(date)"

# 验证结果
echo "🔍 验证结果..."
remaining=$(find src/e-invoice -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/constants" 2>/dev/null | grep -v "common/utils/constants" | wc -l)
echo "剩余需要替换的导入: $remaining 个"

if [ "$remaining" -eq 0 ]; then
    echo "✅ 所有 constants 导入已成功替换!"
else
    echo "⚠️ 还有 $remaining 个导入需要手动检查"
fi
