#!/bin/bash

# 通用验证脚本
# 用于验证任何目录的 ESLint 和测试状态
# 使用方法: bash verify-general.sh [目录路径] [任务名称]

set -e

# 参数
TARGET_DIR=${1:-"src/"}
TASK_NAME=${2:-"General"}

echo "🔍 $TASK_NAME 验证开始"
echo "目标目录: $TARGET_DIR"
echo "时间: $(date)"
echo "=========================================="

# 1. ESLint 检查
echo "📋 运行 ESLint 检查整个项目..."
yarn eslint

echo ""

# 2. 运行测试
echo "🧪 运行相关测试..."
if [ -d "$TARGET_DIR" ]; then
    yarn test --testPathPattern="$TARGET_DIR" --passWithNoTests
else
    echo "⚠️  目录 $TARGET_DIR 不存在，跳过测试"
fi

echo ""

# 3. 验证应用编译和启动
echo "🚀 验证应用编译和启动..."
echo "启动开发服务器进行编译验证..."
timeout 30s yarn start > /dev/null 2>&1 && echo "✅ 应用编译成功" || echo "❌ 应用编译失败，请检查错误"

echo ""

# 4. 检查导入路径状态
echo "🔍 检查剩余的 utils 导入..."
if [ -d "$TARGET_DIR" ]; then
    echo "当前 utils 导入:"
    find "$TARGET_DIR" -name "*.js" -o -name "*.jsx" | xargs grep "from.*utils/" 2>/dev/null || echo "  无 utils 导入"
else
    echo "⚠️  目录 $TARGET_DIR 不存在，跳过导入检查"
fi

echo ""
echo "✅ $TASK_NAME 验证完成"
echo "时间: $(date)"
echo "=========================================="
