#!/bin/bash
# 任务1: 创建最终备份脚本

LOG_FILE="UT-revamp-plan/phase3/task1-backup-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔄 任务1: 创建最终备份"
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 检查当前git状态
echo ""
echo "📋 检查当前git状态..."
git status --porcelain

# 如果有未提交的更改，先提交
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️ 发现未提交的更改，正在提交..."
    git add .
    git commit -m "Phase 2.5: 导入路径修复进展 - 减少107个旧路径导入 (24%改善)"
    echo "✅ 更改已提交"
else
    echo "✅ 工作目录干净，无需提交"
fi

# 创建git分支备份
echo ""
echo "🌿 创建git分支备份..."
BACKUP_BRANCH="backup-before-utils-cleanup-$(date +%Y%m%d-%H%M%S)"
git checkout -b "$BACKUP_BRANCH"
echo "✅ 创建备份分支: $BACKUP_BRANCH"

# 切换回原分支
ORIGINAL_BRANCH=$(git rev-parse --abbrev-ref HEAD~1)
if [ "$ORIGINAL_BRANCH" = "HEAD" ]; then
    ORIGINAL_BRANCH="WB-10827"  # 默认分支
fi
git checkout "$ORIGINAL_BRANCH"
echo "✅ 切换回原分支: $ORIGINAL_BRANCH"

# 创建文件备份目录
echo ""
echo "📁 创建文件备份..."
BACKUP_DIR="UT-revamp-plan/phase3/final-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 备份所有工具函数相关文件
echo "  📋 备份工具函数文件..."

# 备份原始utils目录
if [ -d "src/utils" ]; then
    cp -r "src/utils" "$BACKUP_DIR/utils-original"
    echo "    ✅ 备份 src/utils -> $BACKUP_DIR/utils-original"
fi

# 备份新的common/utils目录
if [ -d "src/common/utils" ]; then
    cp -r "src/common/utils" "$BACKUP_DIR/common-utils-new"
    echo "    ✅ 备份 src/common/utils -> $BACKUP_DIR/common-utils-new"
fi

# 备份各模块的utils目录
for module_dir in src/*/utils; do
    if [ -d "$module_dir" ]; then
        module_name=$(basename $(dirname "$module_dir"))
        cp -r "$module_dir" "$BACKUP_DIR/${module_name}-utils"
        echo "    ✅ 备份 $module_dir -> $BACKUP_DIR/${module_name}-utils"
    fi
done

# 备份测试文件
echo "  📋 备份测试文件..."
find src -name "*.test.js" -path "*/utils/*" -exec cp --parents {} "$BACKUP_DIR/" \;
echo "    ✅ 备份工具函数测试文件"

# 创建备份清单
echo ""
echo "📝 创建备份清单..."
MANIFEST_FILE="$BACKUP_DIR/backup-manifest.txt"
cat > "$MANIFEST_FILE" << EOF
# 最终备份清单
# 创建时间: $(date)
# Git分支备份: $BACKUP_BRANCH
# 文件备份目录: $BACKUP_DIR

## Git信息
当前分支: $ORIGINAL_BRANCH
备份分支: $BACKUP_BRANCH
最新提交: $(git rev-parse HEAD)
提交信息: $(git log -1 --pretty=format:"%s")

## 备份文件统计
EOF

echo "原始utils目录文件数: $(find src/utils -type f 2>/dev/null | wc -l)" >> "$MANIFEST_FILE"
echo "新common/utils目录文件数: $(find src/common/utils -type f 2>/dev/null | wc -l)" >> "$MANIFEST_FILE"
echo "模块utils目录数: $(find src/*/utils -type d 2>/dev/null | wc -l)" >> "$MANIFEST_FILE"
echo "工具函数测试文件数: $(find src -name "*.test.js" -path "*/utils/*" 2>/dev/null | wc -l)" >> "$MANIFEST_FILE"

echo "" >> "$MANIFEST_FILE"
echo "## 详细文件列表" >> "$MANIFEST_FILE"
find "$BACKUP_DIR" -type f | sort >> "$MANIFEST_FILE"

echo "✅ 备份清单已创建: $MANIFEST_FILE"

# 创建快速回滚脚本
echo ""
echo "🔄 创建快速回滚脚本..."
ROLLBACK_SCRIPT="$BACKUP_DIR/quick-rollback.sh"
cat > "$ROLLBACK_SCRIPT" << 'EOF'
#!/bin/bash
# 快速回滚脚本
# 使用方法: ./quick-rollback.sh

echo "🔄 开始快速回滚..."

# 回滚到备份分支
BACKUP_BRANCH="BACKUP_BRANCH_PLACEHOLDER"
ORIGINAL_BRANCH="ORIGINAL_BRANCH_PLACEHOLDER"

echo "切换到备份分支: $BACKUP_BRANCH"
git checkout "$BACKUP_BRANCH"

echo "创建新的工作分支..."
git checkout -b "rollback-$(date +%Y%m%d-%H%M%S)"

echo "✅ 回滚完成！"
echo "当前在回滚分支，可以继续工作或合并到主分支"
EOF

# 替换占位符
sed -i.tmp "s/BACKUP_BRANCH_PLACEHOLDER/$BACKUP_BRANCH/g" "$ROLLBACK_SCRIPT"
sed -i.tmp "s/ORIGINAL_BRANCH_PLACEHOLDER/$ORIGINAL_BRANCH/g" "$ROLLBACK_SCRIPT"
rm -f "$ROLLBACK_SCRIPT.tmp"

chmod +x "$ROLLBACK_SCRIPT"
echo "✅ 快速回滚脚本已创建: $ROLLBACK_SCRIPT"

# 验证备份完整性
echo ""
echo "🔍 验证备份完整性..."
BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
BACKUP_FILES=$(find "$BACKUP_DIR" -type f | wc -l)

echo "备份目录大小: $BACKUP_SIZE"
echo "备份文件数量: $BACKUP_FILES"

if [ "$BACKUP_FILES" -gt 0 ]; then
    echo "✅ 备份验证通过"
else
    echo "❌ 备份验证失败 - 没有文件被备份"
    exit 1
fi

echo ""
echo "🎉 任务1完成: 最终备份创建成功！"
echo "  📁 备份目录: $BACKUP_DIR"
echo "  🌿 Git备份分支: $BACKUP_BRANCH"
echo "  📝 备份清单: $MANIFEST_FILE"
echo "  🔄 回滚脚本: $ROLLBACK_SCRIPT"
echo "  📊 备份大小: $BACKUP_SIZE"
echo "  📋 备份文件数: $BACKUP_FILES"
