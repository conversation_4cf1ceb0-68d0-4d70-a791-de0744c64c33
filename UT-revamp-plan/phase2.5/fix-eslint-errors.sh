#!/bin/bash
# 修复 ESLint 错误脚本

LOG_FILE="UT-revamp-plan/phase3/fix-eslint-errors-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔧 开始修复 ESLint 错误..."
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

echo ""
echo "🔄 第一步：修复文件扩展名问题..."

# 获取所有有路径解析错误的文件
FILES_WITH_PATH_ERRORS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*common/utils" 2>/dev/null)

for file in $FILES_WITH_PATH_ERRORS; do
    if [ -f "$file" ]; then
        echo "  🔧 处理文件: $file"
        
        # 备份文件
        cp "$file" "$file.backup"
        
        # 添加文件扩展名
        sed -i.tmp "s|from '\([^']*common/utils/[^']*\)'|from '\1/index'|g" "$file"
        
        # 特殊处理一些已知的文件路径
        sed -i.tmp "s|from '\([^']*common/utils/constants\)/index'|from '\1'|g" "$file"
        sed -i.tmp "s|from '\([^']*common/utils/system/utils\)/index'|from '\1'|g" "$file"
        sed -i.tmp "s|from '\([^']*common/utils/system/history\)/index'|from '\1'|g" "$file"
        sed -i.tmp "s|from '\([^']*common/utils/api/request\)/index'|from '\1'|g" "$file"
        sed -i.tmp "s|from '\([^']*common/utils/time/datetime-lib\)/index'|from '\1'|g" "$file"
        sed -i.tmp "s|from '\([^']*common/utils/time/time-lib\)/index'|from '\1'|g" "$file"
        sed -i.tmp "s|from '\([^']*common/utils/validation/form-validate\)/index'|from '\1'|g" "$file"
        
        # 清理临时文件
        rm -f "$file.tmp"
        
        # 检查变化
        if ! diff -q "$file" "$file.backup" > /dev/null 2>&1; then
            echo "    ✅ 文件已更新"
        else
            echo "    ⚠️ 文件无变化"
        fi
        
        # 删除备份
        rm -f "$file.backup"
    fi
done

echo ""
echo "🔄 第二步：使用 ESLint --fix 自动修复..."

# 运行 ESLint --fix 来自动修复可修复的问题
echo "运行 ESLint --fix..."
if yarn eslint --fix --quiet; then
    echo "✅ ESLint --fix 完成"
else
    echo "⚠️ ESLint --fix 完成，但仍有一些错误"
fi

echo ""
echo "🧪 验证修复结果..."

# 检查剩余的ESLint错误
echo "📋 检查剩余的 ESLint 错误..."
ESLINT_OUTPUT=$(yarn eslint --quiet 2>&1)
ESLINT_EXIT_CODE=$?

if [ $ESLINT_EXIT_CODE -eq 0 ]; then
    echo "✅ 所有 ESLint 错误已修复！"
else
    echo "⚠️ 仍有一些 ESLint 错误需要手动修复"
    echo "错误数量: $(echo "$ESLINT_OUTPUT" | grep -c "error")"
    echo ""
    echo "主要错误类型:"
    echo "$ESLINT_OUTPUT" | grep "error" | cut -d' ' -f4- | sort | uniq -c | sort -nr | head -5
fi

echo ""
echo "📊 总体进展:"
TOTAL_REMAINING=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)
echo "剩余旧路径导入: $TOTAL_REMAINING 个"

echo ""
echo "✅ ESLint 错误修复完成！"
