import { E_INVOICE_STATUS, E_INVOICE_BLOCK_ERROR_CODES } from '../../../utils/constants';

export const PAGE_HTML_ID = {
  key: 'id',
  value: 'e-invoice-html',
};

export const E_INVOICE_DOCUMENT_TYPES = {
  INVOICE: 'INVOICE',
  REFUND: 'REFUND',
};

export const GET_E_INVOICE_ERROR_CODES = {
  ORDER_NOT_FOUND: '41028',
  ORDER_TRANSACTION_TYPE_NOT_SUPPORT: '41029',
  CONSOLIDATION: '41037',
  ...E_INVOICE_BLOCK_ERROR_CODES,
};

export const STATUS_TAG_COLORS = {
  [E_INVOICE_STATUS.CANCEL]: null,
  [E_INVOICE_STATUS.SUBMITTED]: null,
  [E_INVOICE_STATUS.VALID]: 'green',
  [E_INVOICE_STATUS.REJECT]: 'red',
};

export const STATUS_I18N_KEYS = {
  [E_INVOICE_STATUS.CANCEL]: 'Canceled',
  [E_INVOICE_STATUS.SUBMITTED]: 'Submitted',
  [E_INVOICE_STATUS.VALID]: 'Validated',
  [E_INVOICE_STATUS.REJECT]: 'Rejected',
};
