import React, { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMount } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import { UserCircle } from 'phosphor-react';
import BeepLogoSimpleImage from '../../../images/beep-logo-simple.svg';
import UserBackgroundImage from '../../../images/user-download-banner-background.png';
import { DOWNLOAD_BEEP_APP_URL } from '../../../common/utils/constants';
import { getDownloadBeepAppUrl, removeLocalStorageVariable } from '../../../common/utils';
import { getIsLogin } from '../../../redux/modules/user/selectors';
import { getShouldShowUserNotificationBadge } from '../../redux/common/selectors';
import { updateMembershipList } from '../../redux/memberships/thunks';
import { getLoginDrawerShow, getCompleteDrawerShow, getIsDownloadBannerShown } from './redux/selectors';
import { mounted, hideLoginDrawer, hideCompleteDrawer, showCompleteDrawer, showLoginDrawer } from './redux/thunks';
import Frame from '../../../common/components/Frame';
import PageHeader from '../../../common/components/PageHeader';
import { ObjectFitImage } from '../../../common/components/Image';
import Button from '../../../common/components/Button';
import Badge from '../../../common/components/Badge';
import DownloadBanner from '../../../common/components/DownloadBanner';
import Login from '../../../common/containers/Login';
import CompleteProfile from '../../../common/containers/CompleteProfile';
import MembershipList from '../../components/MembershipList';
import styles from './MembershipsPage.module.scss';

const MembershipsPage = () => {
  const { t } = useTranslation('User');
  const dispatch = useDispatch();
  const isLogin = useSelector(getIsLogin);
  const shouldShowUserNotificationBadge = useSelector(getShouldShowUserNotificationBadge);
  const isLoginDrawerShown = useSelector(getLoginDrawerShow);
  const isCompleteProfileDrawerShown = useSelector(getCompleteDrawerShow);
  const isDownloadBannerShown = useSelector(getIsDownloadBannerShown);
  const handleCloseLoginDrawer = useCallback(() => {
    dispatch(hideLoginDrawer());
  }, [dispatch]);
  const handleCloseCompleteProfileDrawer = useCallback(() => {
    dispatch(hideCompleteDrawer());
  }, [dispatch]);
  const handleUserButtonClick = useCallback(() => {
    if (isLogin) {
      dispatch(showCompleteDrawer());
    } else {
      dispatch(showLoginDrawer());
    }
  }, [dispatch, isLogin]);

  const handleLogout = useCallback(() => {
    // Clear user login data from localStorage
    removeLocalStorageVariable('user.p');
    removeLocalStorageVariable('user.country');

    // Reload the page to reset all application state
    window.location.reload();
  }, []);

  useMount(() => {
    dispatch(mounted());
  });

  useEffect(() => {
    if (isLogin) {
      dispatch(updateMembershipList());
    }
  }, [isLogin, dispatch]);

  return (
    <Frame>
      <section className={styles.MembershipsPageHeaderContainer}>
        {isDownloadBannerShown && (
          <DownloadBanner
            className={styles.MembershipsPageDownloadBanner}
            closable
            link={getDownloadBeepAppUrl({
              source: DOWNLOAD_BEEP_APP_URL.UTM_SOURCE.WEB,
              medium: DOWNLOAD_BEEP_APP_URL.UTM_MEDIUM.BANNER,
              campaign: DOWNLOAD_BEEP_APP_URL.UTM_CAMPAIGN.MY_MEMBERSHIPS,
            })}
            text={t('DownloadBannerText')}
            backgroundImage={UserBackgroundImage}
          />
        )}
        <PageHeader
          isWebBackButtonShow={false}
          className={styles.MembershipsPageHeader}
          titleClassName={styles.MembershipsPageLogoContainer}
          title={
            <ObjectFitImage
              noCompression
              className={styles.MembershipsPageLogo}
              src={BeepLogoSimpleImage}
              alt="Beep Logo"
            />
          }
          rightContent={
            <Button
              data-test-id="user.memberships-page.header.user-button"
              type="text"
              className={styles.MembershipsPageUserButton}
              contentClassName={styles.MembershipsPageUserButtonContent}
              onClick={handleUserButtonClick}
            >
              {shouldShowUserNotificationBadge && <Badge className={styles.MembershipsPageNotificationBadge} />}
              <UserCircle className={styles.MembershipsPageUserIcon} size={24} weight="light" />
            </Button>
          }
        />
      </section>

      <section className={styles.MembershipsPageSection}>
        <h2 className={styles.MembershipsPageTitle}>{t('MyMembershipsTitle')}</h2>
        <MembershipList />
      </section>
      <CompleteProfile
        show={isCompleteProfileDrawerShown}
        showHeader
        showSkipButton={false}
        showLogoutButton
        onClose={handleCloseCompleteProfileDrawer}
        onLogout={handleLogout}
      />
      <Login show={isLoginDrawerShown} showCloseButton={false} onClose={handleCloseLoginDrawer} />
    </Frame>
  );
};

MembershipsPage.displayName = 'MembershipsPage';

export default MembershipsPage;
