import { createAsyncThunk } from '@reduxjs/toolkit';
import <PERSON>leverTap from '../../../../utils/clevertap';
import { getIsLogin } from '../../../../redux/modules/user/selectors';
import { initUserInfo, loginUserByBeepApp } from '../../../../redux/modules/user/thunks';
import { fetchRewardList } from '../../../../redux/modules/rewards/thunks';
import { updateMembershipList } from '../../../redux/memberships/thunks';
import { getIsWebview } from '../../../redux/common/selectors';

export const changeActiveTab = createAsyncThunk('user/nativeRewards/changeActiveTab', async tabKey => tabKey);

export const mounted = createAsyncThunk('user/nativeRewards/mounted', async (_, { dispatch, getState }) => {
  const state = getState();
  const isWebview = getIsWebview(state);

  CleverTap.pushEvent('Native Rewards Page - View Page');

  await dispatch(initUserInfo());

  // 2. Only handle Beep App login (not web and alipay)
  if (isWebview) {
    await dispatch(loginUserByBeepApp());
  }

  const isLogin = getIsLogin(getState());

  if (isLogin) {
    // Fetch data for both tabs
    dispatch(updateMembershipList()); // For membership tab
    dispatch(fetchRewardList()); // For promo voucher tab
  }
});
