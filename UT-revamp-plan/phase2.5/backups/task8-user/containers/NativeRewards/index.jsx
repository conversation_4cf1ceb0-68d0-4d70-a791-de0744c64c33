import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useMount } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import { TAB_TYPES } from './utils/constants';
import { getClassName } from '../../../common/utils/ui';
import { getActiveTab } from './redux/selectors';
import { changeActiveTab, mounted } from './redux/thunks';
import Frame from '../../../common/components/Frame';
import SwitchButton from '../../../common/components/SwitchButton';
import MembershipList from '../../components/MembershipList';
import PromoVoucherList from '../../components/PromoVoucherList';
import styles from './NativeRewards.module.scss';

const HEADER_MIN_HEIGHT = 108;

const NativeRewards = () => {
  const { t } = useTranslation(['User']);
  const dispatch = useDispatch();
  const activeTab = useSelector(getActiveTab);
  const tabOptions = [
    {
      key: TAB_TYPES.MEMBERSHIP,
      label: t('MembershipTabLabel'),
      disabled: false,
    },
    {
      key: TAB_TYPES.PROMO_VOUCHER,
      label: t('PromoVoucherTabLabel'),
      disabled: false,
    },
  ];
  const handleTabChange = useCallback(
    tabKey => {
      dispatch(changeActiveTab(tabKey));
    },
    [dispatch]
  );

  useMount(() => {
    dispatch(mounted());
  });

  return (
    <Frame>
      <div
        style={{ minHeight: `${HEADER_MIN_HEIGHT}px` }}
        className={getClassName([
          styles.NativeRewardsHeader,
          activeTab === TAB_TYPES.MEMBERSHIP && styles.NativeRewardsMembershipHeader,
        ])}
      >
        <h1 className={styles.NativeRewardsTitle}>{t('NativeRewardsTitle')}</h1>

        <section className={styles.NativeRewardsTabSection}>
          <SwitchButton
            className={styles.NativeRewardsSwitchButton}
            data-test-id="user.native-rewards.tab-switch"
            options={tabOptions}
            activeKey={activeTab}
            onChange={handleTabChange}
          />
        </section>
      </div>

      {activeTab === TAB_TYPES.MEMBERSHIP && (
        <section className={styles.NativeRewardsMembershipContentSection}>
          <MembershipList />
        </section>
      )}
      {activeTab === TAB_TYPES.PROMO_VOUCHER && (
        <PromoVoucherList searchRewardStyle={{ top: `${HEADER_MIN_HEIGHT}px` }} />
      )}
    </Frame>
  );
};

NativeRewards.displayName = 'NativeRewards';

export default NativeRewards;
