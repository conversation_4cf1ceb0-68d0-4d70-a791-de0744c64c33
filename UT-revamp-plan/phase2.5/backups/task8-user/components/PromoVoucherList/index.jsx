import React from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles, useSetState } from 'react-use';
import { useTranslation } from 'react-i18next';
import { actions as rewardsActions } from '../../../redux/modules/rewards';
import {
  getIsConsumerEmptyReward,
  getIsSearchEmptyReward,
  getIsRewardListSearching,
} from '../../redux/promoVoucherList/selectors';
import { mounted } from '../../redux/promoVoucherList/thunks';
import Loader from '../../../common/components/Loader';
import SearchReward from './components/SearchReward';
import SkeletonLoader from './components/SkeletonLoader';
import TicketList from './components/TicketList';
import RewardDetailDrawer from './components/RewardDetailDrawer';
import EmptyVoucher from './components/EmptyVoucher';
import EmptySearchResult from './components/EmptySearchResult';
import styles from './PromoVoucherList.module.scss';

const PromoVoucherList = ({ searchRewardStyle }) => {
  const { t } = useTranslation(['User']);
  const dispatch = useDispatch();
  const [mountedStatus, setMountedStatus] = useSetState(false);
  const isSearchEmptyReward = useSelector(getIsSearchEmptyReward);
  const isConsumerEmptyReward = useSelector(getIsConsumerEmptyReward);
  const isRewardListSearching = useSelector(getIsRewardListSearching);

  useLifecycles(
    () => {
      dispatch(mounted()).then(() => {
        setMountedStatus(true);
      });
    },
    () => {
      dispatch(rewardsActions.resetRewardsState());
    }
  );

  return (
    <>
      <SearchReward style={searchRewardStyle} />
      {isSearchEmptyReward ? (
        <EmptySearchResult />
      ) : isConsumerEmptyReward ? (
        <EmptyVoucher />
      ) : isRewardListSearching ? (
        <section className={styles.PromoVoucherListSearchLoaderSection}>
          <div className={styles.PromoVoucherListSearchLoaderContainer}>
            <Loader className={styles.PromoVoucherListSearchLoader} size={30} />
            <span className={styles.PromoVoucherListSearchLoaderText}>{t('PromoVoucherListSearching')}</span>
          </div>
        </section>
      ) : mountedStatus ? (
        <>
          <TicketList />
          <RewardDetailDrawer />
        </>
      ) : (
        <SkeletonLoader />
      )}
    </>
  );
};

PromoVoucherList.displayName = 'PromoVoucherList';

PromoVoucherList.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  searchRewardStyle: PropTypes.object,
};

PromoVoucherList.defaultProps = {
  searchRewardStyle: {},
};

export default PromoVoucherList;
