import React from 'react';
import { useTranslation } from 'react-i18next';
import UserEmptyMembershipImage from '../../../../../images/user-empty-membership.svg';
import UserPointsImage from '../../../../../images/user-points-icon.svg';
import UserCashbackImage from '../../../../../images/user-cashback-icon.svg';
import UserDiscountsImage from '../../../../../images/user-discounts-icon.svg';
import UserRewardsImage from '../../../../../images/user-rewards-icon.svg';
import { ObjectFitImage } from '../../../../../common/components/Image';
import styles from './EmptyMembershipListResult.module.scss';

const EmptyMembershipListResult = () => {
  const { t } = useTranslation('User');
  const rewards = [
    {
      icon: UserPointsImage,
      nameKey: 'Points',
    },
    {
      icon: UserCashbackImage,
      nameKey: 'Cashback',
    },
    {
      icon: UserDiscountsImage,
      nameKey: 'Discounts',
    },
    {
      icon: UserRewardsImage,
      nameKey: 'Vouchers',
    },
  ];

  return (
    <section className={styles.EmptyMembershipListResult}>
      <div className={styles.EmptyMembershipListResultImageContainer}>
        <ObjectFitImage
          noCompression
          className={styles.EmptyMembershipListResultImage}
          src={UserEmptyMembershipImage}
          alt="Empty membership illustration"
        />
      </div>
      <h2 className={styles.EmptyMembershipListResultTitle}>{t('EmptyMembershipListResultTitle')}</h2>
      <p className={styles.EmptyMembershipListResultDescription}>{t('EmptyMembershipListResultDescription')}</p>
      <div className={styles.EmptyMembershipListResultRewards}>
        {rewards.map(rewardItem => (
          <div key={rewardItem.nameKey} className={styles.EmptyMembershipListResultReward}>
            <div className={styles.EmptyMembershipListResultRewardIconContainer}>
              <ObjectFitImage
                noCompression
                src={rewardItem.icon}
                alt={t(rewardItem.nameKey)}
                className={styles.EmptyMembershipListResultRewardIcon}
              />
            </div>
            <span className={styles.EmptyMembershipListResultRewardName}>{t(rewardItem.nameKey)}</span>
          </div>
        ))}
      </div>
    </section>
  );
};

EmptyMembershipListResult.displayName = 'EmptyMembershipListResult';

export default EmptyMembershipListResult;
