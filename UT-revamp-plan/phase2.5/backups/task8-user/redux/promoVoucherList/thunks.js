import { createAsyncThunk } from '@reduxjs/toolkit';
import CleverTap from '../../../utils/clevertap';
import { getIsLogin } from '../../../redux/modules/user/selectors';
import { initUserInfo, loginUserByBeepApp } from '../../../redux/modules/user/thunks';
import { fetchRewardList, fetchRewardDetail } from '../../../redux/modules/rewards/thunks';
import { getIsWebview } from '../common/selectors';
import { getSelectedRewardId, getSelectedRewardType, getSelectedRewardUniquePromotionCodeId } from './selectors';

export const mounted = createAsyncThunk('user/promoVoucherList/mounted', async (_, { dispatch, getState }) => {
  const isWebview = getIsWebview(getState());

  CleverTap.pushEvent('My Rewards Page - View Page');

  await dispatch(initUserInfo());

  // only display in Beep App right now
  if (isWebview) {
    await dispatch(loginUserByBeepApp());
  }

  const isLogin = getIsLogin(getState());

  if (isLogin) {
    dispatch(fetchRewardList());
  }
});

export const searchPromos = createAsyncThunk(
  'user/promoVoucherList/searchPromos',
  async (searchKeyword, { dispatch }) => dispatch(fetchRewardList({ search: searchKeyword }))
);

export const selectedRewardItem = createAsyncThunk(
  'user/promoVoucherList/selectedRewardItem',
  async selectedReward => selectedReward
);

export const showRewardDetailDrawer = createAsyncThunk('user/promoVoucherList/showRewardDetailDrawer', async () => {});

export const hideRewardDetailDrawer = createAsyncThunk('user/promoVoucherList/hideRewardDetailDrawer', async () => {});

export const fetchConsumerRewardDetail = createAsyncThunk(
  'user/promoVoucherList/fetchConsumerRewardDetail',
  async (_, { dispatch, getState }) => {
    CleverTap.pushEvent('My Reward Voucher & Promo Details - View Page');

    const state = getState();
    const id = getSelectedRewardId(state);
    const uniquePromotionCodeId = getSelectedRewardUniquePromotionCodeId(state);
    const type = getSelectedRewardType(state);

    dispatch(fetchRewardDetail({ id, uniquePromotionCodeId, type }));
  }
);

export const clickRewardItem = createAsyncThunk(
  'user/promoVoucherList/clickRewardItem',
  async (selectedReward, { dispatch }) => {
    await dispatch(selectedRewardItem(selectedReward));
    dispatch(showRewardDetailDrawer());
    dispatch(fetchConsumerRewardDetail());
  }
);
