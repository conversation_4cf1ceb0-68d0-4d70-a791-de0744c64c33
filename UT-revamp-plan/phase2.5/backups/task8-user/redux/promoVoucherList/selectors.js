import { createSelector } from 'reselect';
import i18next from 'i18next';
import { PROMO_VOUCHER_STATUS } from '../../../common/utils/constants';
import {
  REWARD_APPLIED_CODE_ERRORS_I18N_KEYS,
  REWARD_APPLY_TO_LIMITS_CONDITIONS,
  REWARDS_APPLIED_SOURCES,
} from '../../../common/utils/rewards/constants';
import { getPrice } from '../../../common/utils';
import { formatTimeToDateString } from '../../../utils/datetime-lib';
import {
  getRemainingRewardExpiredDays,
  getFormatDiscountValue,
  getExpiringDaysI18n,
} from '../../../common/utils/rewards';
import { getMerchantLocale, getMerchantCurrency, getMerchantCountry } from '../../../redux/modules/merchant/selectors';
import {
  getLoadRewardListRequestData,
  getIsLoadRewardListRequestCompleted,
  getIsLoadRewardListRequestPending,
  getRewardDetailDiscountValue,
  getRewardDetailDiscountType,
  getRewardDetailValidTo,
  getRewardDetailMinSpendAmount,
  getRewardDetailApplicableBusiness,
  getRewardDetailMerchantDisplayName,
  getRewardDetailProductLimits,
  getRewardDetailLimitsConditions,
  getIsAllStoresApplied,
  getRewardDetailLimitsAppliedSources,
  getIsRewardDetailUnAvailable,
} from '../../../redux/modules/rewards/selectors';

export const getIsSearchBoxEmpty = state => state.promoVoucherList.searchBox.isEmpty;

export const getSearchBoxError = state => state.promoVoucherList.searchBox.error;

export const getSelectedRewardId = state => state.promoVoucherList.selectedReward.id;

export const getSelectedRewardUniquePromotionCodeId = state =>
  state.promoVoucherList.selectedReward.uniquePromotionCodeId;

export const getSelectedRewardCode = state => state.promoVoucherList.selectedReward.code;

export const getSelectedRewardType = state => state.promoVoucherList.selectedReward.type;

export const getIsRewardDetailDrawerShow = state => state.promoVoucherList.rewardDetailDrawerShow;

/*
 * Selectors derived from state
 */
export const getRewardList = createSelector(
  getLoadRewardListRequestData,
  getMerchantCountry,
  getMerchantCurrency,
  getMerchantLocale,
  (loadRewardListRequestData, merchantCountry, merchantCurrency, merchantLocale) =>
    loadRewardListRequestData.map(rewardItem => {
      const {
        id,
        uniquePromotionCodeId,
        code,
        type,
        merchantDisplayName,
        discountType,
        discountValue,
        name,
        validTo,
        status,
        minSpendAmount,
      } = rewardItem;
      const applicableMerchants = merchantDisplayName || i18next.t('User:RewardSelectedMerchantsOnlyText');
      const value = getFormatDiscountValue(discountType, discountValue, {
        locale: merchantLocale,
        currency: merchantCurrency,
        country: merchantCountry,
      });
      const remainingExpiredDays = getRemainingRewardExpiredDays(validTo);
      const isUnavailable = [PROMO_VOUCHER_STATUS.EXPIRED, PROMO_VOUCHER_STATUS.REDEEMED].includes(status);
      const expiringDateI18n = validTo
        ? {
            i18nKey: 'ValidUntil',
            params: { date: formatTimeToDateString(merchantCountry, validTo) },
          }
        : null;
      const expiringDaysI18n = getExpiringDaysI18n(remainingExpiredDays);
      const minSpendI18n = minSpendAmount
        ? {
            i18nKey: 'MinConsumption',
            params: {
              amount: getPrice(minSpendAmount, {
                locale: merchantLocale,
                currency: merchantCurrency,
                country: merchantCountry,
              }),
            },
          }
        : null;

      return {
        id,
        uniquePromotionCodeId,
        code,
        type,
        key: `${id}-${uniquePromotionCodeId}-${type}`,
        merchantDisplayName,
        applicableMerchants,
        value,
        name,
        expiringDateI18n,
        expiringDaysI18n,
        status,
        isUnavailable,
        minSpendI18n,
      };
    })
);

export const getSearchRewardError = createSelector(
  getSearchBoxError,
  searchBoxError => searchBoxError && i18next.t(`User:${REWARD_APPLIED_CODE_ERRORS_I18N_KEYS[searchBoxError]}`)
);

export const getIsConsumerEmptyReward = createSelector(
  getIsSearchBoxEmpty,
  getRewardList,
  getIsLoadRewardListRequestCompleted,
  (isSearchBoxEmpty, rewardList, isLoadRewardListRequestCompleted) =>
    isSearchBoxEmpty && rewardList.length === 0 && isLoadRewardListRequestCompleted
);

export const getIsSearchEmptyReward = createSelector(
  getIsSearchBoxEmpty,
  getRewardList,
  getIsLoadRewardListRequestCompleted,
  (isSearchBoxEmpty, rewardList, isLoadRewardListRequestCompleted) =>
    !isSearchBoxEmpty && rewardList.length === 0 && isLoadRewardListRequestCompleted
);

export const getIsRewardListSearching = createSelector(
  getIsSearchBoxEmpty,
  getIsLoadRewardListRequestPending,
  (isSearchBoxEmpty, isLoadRewardListRequestPending) => !isSearchBoxEmpty && isLoadRewardListRequestPending
);

export const getRewardFormatDiscountValue = createSelector(
  getRewardDetailDiscountValue,
  getRewardDetailDiscountType,
  getMerchantCountry,
  getMerchantCurrency,
  getMerchantLocale,
  (rewardDetailDiscountValue, rewardDetailDiscountType, merchantCountry, merchantCurrency, merchantLocale) =>
    getFormatDiscountValue(rewardDetailDiscountType, rewardDetailDiscountValue, {
      country: merchantCountry,
      currency: merchantCurrency,
      locale: merchantLocale,
    })
);

export const getRewardLimitations = createSelector(
  getRewardDetailValidTo,
  getRewardDetailMinSpendAmount,
  getMerchantCountry,
  getMerchantCurrency,
  getMerchantLocale,
  (rewardDetailValidTo, rewardDetailMinSpendAmount, merchantCountry, merchantCurrency, merchantLocale) => {
    const limitations = [];

    if (rewardDetailMinSpendAmount) {
      limitations.push({
        key: 'myRewardDetail-minConsumption',
        i18nKey: 'MinConsumption',
        params: {
          amount: getPrice(rewardDetailMinSpendAmount, {
            country: merchantCountry,
            currency: merchantCurrency,
            locale: merchantLocale,
          }),
        },
      });
    }

    if (rewardDetailValidTo) {
      limitations.push({
        key: 'myRewardDetail-expiringDate',
        i18nKey: 'ValidUntil',
        params: { date: formatTimeToDateString(merchantCountry, rewardDetailValidTo) },
      });
    }

    return limitations;
  }
);

export const getRewardDetailExpiringDaysI18n = createSelector(getRewardDetailValidTo, rewardDetailValidTo => {
  const remainingRewardExpiredDays = getRemainingRewardExpiredDays(rewardDetailValidTo);

  return getExpiringDaysI18n(remainingRewardExpiredDays);
});

export const getRewardDetailFormatApplicableMerchantsText = createSelector(
  getRewardDetailMerchantDisplayName,
  merchantDisplayName => merchantDisplayName || i18next.t('User:RewardSelectedMerchantsOnlyText')
);

export const getRewardDetailFormatAppliedProductsText = createSelector(
  getRewardDetailProductLimits,
  getRewardDetailLimitsConditions,
  (rewardDetailProductLimits, rewardDetailLimitsConditions) => {
    const applyLimitedProducts = rewardDetailLimitsConditions.filter(
      ({ entity, propertyName }) =>
        entity === REWARD_APPLY_TO_LIMITS_CONDITIONS.ENTITY.PRODUCT &&
        [
          REWARD_APPLY_TO_LIMITS_CONDITIONS.PROPERTY_NAME.CATEGORY,
          REWARD_APPLY_TO_LIMITS_CONDITIONS.PROPERTY_NAME.TAGS,
          REWARD_APPLY_TO_LIMITS_CONDITIONS.PROPERTY_NAME.ID,
        ].includes(propertyName)
    );

    if (!rewardDetailProductLimits) {
      return null;
    }

    if (rewardDetailProductLimits.length === 0 && applyLimitedProducts.length === 0) {
      return i18next.t('User:RewardDetailAllProductsText');
    }

    return i18next.t('User:RewardDetailSelectedProductsText');
  }
);

export const getRewardDetailFormatAppliedStoresText = createSelector(getIsAllStoresApplied, isAllStoresApplied => {
  if (isAllStoresApplied) {
    return i18next.t('User:RewardDetailAllStoresText');
  }

  return i18next.t('User:RewardDetailSelectedStoresText');
});

export const getRewardDetailRedeemOnlineList = createSelector(
  getRewardDetailLimitsAppliedSources,
  rewardDetailLimitsAppliedSources =>
    rewardDetailLimitsAppliedSources.filter(source => source !== REWARDS_APPLIED_SOURCES.POS)
);

export const getIsRewardDetailRedeemOnlineShow = createSelector(
  getRewardDetailRedeemOnlineList,
  rewardDetailRedeemOnlineList => rewardDetailRedeemOnlineList.length > 0
);

export const getIsRewardDetailRedeemInStoreShow = createSelector(
  getRewardDetailLimitsAppliedSources,
  rewardDetailLimitsAppliedSources => rewardDetailLimitsAppliedSources.includes(REWARDS_APPLIED_SOURCES.POS)
);

export const getRewardDetailContentList = createSelector(
  getRewardDetailFormatApplicableMerchantsText,
  getRewardDetailFormatAppliedProductsText,
  getRewardDetailFormatAppliedStoresText,
  getRewardDetailRedeemOnlineList,
  getIsRewardDetailRedeemOnlineShow,
  getIsRewardDetailRedeemInStoreShow,
  (
    rewardDetailFormatApplicableMerchantsText,
    rewardDetailFormatAppliedProductsText,
    rewardDetailFormatAppliedStoresText,
    rewardDetailRedeemOnlineList,
    isRewardDetailRedeemOnlineShow,
    isRewardDetailRedeemInStoreShow
  ) => {
    const contentList = [
      {
        title: i18next.t('User:RewardDetailApplicableMerchantsTitle'),
        titleDescription: rewardDetailFormatApplicableMerchantsText,
      },
    ];

    if (rewardDetailFormatAppliedProductsText) {
      contentList.push({
        title: i18next.t('User:RewardDetailApplicableProductsTitle'),
        titleDescription: rewardDetailFormatAppliedProductsText,
      });
    }

    if (rewardDetailFormatAppliedStoresText) {
      contentList.push({
        title: i18next.t('User:RewardDetailApplicableStoresTitle'),
        titleDescription: rewardDetailFormatAppliedStoresText,
      });
    }

    const articleContentList = [];

    if (isRewardDetailRedeemOnlineShow) {
      articleContentList.push({
        subtitle: i18next.t('User:RewardDetailRedeemOnlineTitle'),
        description: i18next.t('User:RewardDetailRedeemOnlineDescription'),
        rewardDetailRedeemOnlineList,
      });
    }

    if (isRewardDetailRedeemInStoreShow) {
      articleContentList.push({
        subtitle: i18next.t('User:RewardDetailRedeemInStoreTitle'),
        description: i18next.t('User:RewardDetailRedeemInStoreDescription'),
      });
    }

    contentList.push({
      title: i18next.t('User:HowToUse'),
      articleContentList,
    });

    return contentList;
  }
);

export const getIsApplyNowButtonShow = createSelector(
  getRewardDetailLimitsAppliedSources,
  getIsRewardDetailUnAvailable,
  (rewardDetailLimitsAppliedSources, isRewardDetailUnAvailable) =>
    (rewardDetailLimitsAppliedSources.includes(REWARDS_APPLIED_SOURCES.Beep_Pickup) ||
      rewardDetailLimitsAppliedSources.includes(REWARDS_APPLIED_SOURCES.Beep_Delivery)) &&
    !isRewardDetailUnAvailable
);

export const getRewardSpecificMerchant = createSelector(
  getRewardDetailApplicableBusiness,
  rewardDetailApplicableBusiness => {
    if (!rewardDetailApplicableBusiness) {
      return null;
    }

    return rewardDetailApplicableBusiness.length === 1 ? rewardDetailApplicableBusiness[0] : null;
  }
);
