import { createSlice } from '@reduxjs/toolkit';
import {
  fetchMembershipList,
  updateMembershipListData,
  updateMembershipListInitialLoaded,
  updateMembershipListPaginationEnd,
  updateMembershipListPaginationOffset,
} from './thunks';
import { API_REQUEST_STATUS } from '../../../common/utils/constants';

const initialState = {
  memberships: {
    data: [],
    initialLoaded: false,
    pagination: {
      offset: 0,
      limit: 10,
      end: false,
    },
  },
  loadMembershipListRequest: {
    data: null,
    error: null,
    status: null,
  },
};

export const { reducer, actions } = createSlice({
  name: 'user/membershipList',
  initialState,
  reducers: {
    membershipsReset: state => {
      state.memberships.data = [];
      state.memberships.pagination.offset = 0;
      state.memberships.pagination.end = false;
      state.memberships.initialLoaded = false;
    },
    loadMembershipListRequestReset: state => {
      state.loadMembershipListRequest.status = null;
      state.loadMembershipListRequest.error = null;
      state.loadMembershipListRequest.data = null;
    },
  },
  extraReducers: {
    [updateMembershipListData.fulfilled.type]: (state, { payload }) => {
      state.memberships.data = payload;
      state.memberships.initialLoaded = true;
    },
    [updateMembershipListInitialLoaded.fulfilled.type]: (state, { payload }) => {
      state.memberships.initialLoaded = payload;
    },
    [updateMembershipListPaginationOffset.fulfilled.type]: (state, { payload }) => {
      state.memberships.pagination.offset = payload;
    },
    [updateMembershipListPaginationEnd.fulfilled.type]: (state, { payload }) => {
      state.memberships.pagination.end = payload;
    },
    [fetchMembershipList.pending.type]: state => {
      state.loadMembershipListRequest.status = API_REQUEST_STATUS.LOADING;
      state.loadMembershipListRequest.error = null;
    },
    [fetchMembershipList.fulfilled.type]: (state, { payload }) => {
      state.loadMembershipListRequest.status = API_REQUEST_STATUS.SUCCESS;
      state.loadMembershipListRequest.data = payload;
      state.loadMembershipListRequest.error = null;
    },
    [fetchMembershipList.rejected.type]: (state, { error }) => {
      state.loadMembershipListRequest.status = API_REQUEST_STATUS.ERROR;
      state.loadMembershipListRequest.error = error;
      state.loadMembershipListRequest.data = null;
    },
  },
});

export default reducer;
