import _uniqWith from 'lodash/uniqWith';
import _isArray from 'lodash/isArray';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { getConsumerId } from '../../../redux/modules/user/selectors';
import { getMembershipList } from './api-request';
import {
  getMembershipListData,
  getMembershipListPaginationEnd,
  getMembershipListPaginationOffset,
  getMembershipListPaginationLimit,
  getLoadMembershipListRequestData,
  getIsLoadMembershipListRequestRejected,
} from './selectors';

export const updateMembershipListData = createAsyncThunk(
  'user/membershipList/updateMembershipListData',
  async (newMembershipListData, { getState }) => {
    const state = getState();
    const membershipListData = getMembershipListData(state);

    return _uniqWith(
      [...membershipListData, ...newMembershipListData],
      (currentMembership, newMembership) =>
        currentMembership.merchantInfo?.name === newMembership.merchantInfo?.name &&
        currentMembership.customerInfo?.customerId === newMembership.customerInfo?.customerId
    );
  }
);

export const updateMembershipListInitialLoaded = createAsyncThunk(
  'user/membershipList/updateMembershipListInitialLoaded',
  async (isInitialLoaded = false) => isInitialLoaded
);

export const updateMembershipListPaginationOffset = createAsyncThunk(
  'user/membershipList/updateMembershipListPaginationOffset',
  async newOffset => newOffset
);

export const updateMembershipListPaginationEnd = createAsyncThunk(
  'user/membershipList/updateMembershipListPaginationEnd',
  async (newMembershipListLength, { getState }) => {
    const state = getState();
    const membershipListPaginationLimit = getMembershipListPaginationLimit(state);

    return newMembershipListLength === 0 || newMembershipListLength < membershipListPaginationLimit;
  }
);

export const fetchMembershipList = createAsyncThunk(
  'user/membershipList/fetchMembershipList',
  async (_, { dispatch, getState }) => {
    try {
      const state = getState();
      const consumerId = getConsumerId(state);
      const offset = getMembershipListPaginationOffset(state);
      const limit = getMembershipListPaginationLimit(state);

      const result = await getMembershipList(consumerId, { offset, limit });

      if (offset === 0) {
        dispatch(updateMembershipListInitialLoaded(true));
      }

      if (!_isArray(result)) {
        throw new Error('Result is not an array');
      }

      return result;
    } catch (error) {
      await dispatch(updateMembershipListPaginationEnd(true));

      throw error;
    }
  }
);

export const updateMembershipList = createAsyncThunk(
  'user/membershipList/updateMembershipList',
  async (_, { dispatch, getState }) => {
    const isPaginationEnd = getMembershipListPaginationEnd(getState());

    if (isPaginationEnd) {
      return;
    }

    await dispatch(fetchMembershipList());

    const isLoadMembershipListRequestRejected = getIsLoadMembershipListRequestRejected(getState());

    if (isLoadMembershipListRequestRejected) {
      throw new Error('Load membership list request rejected');
    }

    const loadMembershipListRequestData = getLoadMembershipListRequestData(getState());
    const membershipListPaginationOffset = getMembershipListPaginationOffset(getState());
    const membershipListPaginationLimit = getMembershipListPaginationLimit(getState());
    const newLoadMembershipListLength = loadMembershipListRequestData?.length || 0;

    await dispatch(updateMembershipListPaginationEnd(newLoadMembershipListLength));

    if (newLoadMembershipListLength === 0) {
      return;
    }

    await dispatch(updateMembershipListData(loadMembershipListRequestData));

    if (newLoadMembershipListLength < membershipListPaginationLimit) {
      return;
    }

    await dispatch(updateMembershipListPaginationOffset(membershipListPaginationOffset + 1));

    setTimeout(async () => {
      await dispatch(updateMembershipList());
    }, 3000);
  }
);
