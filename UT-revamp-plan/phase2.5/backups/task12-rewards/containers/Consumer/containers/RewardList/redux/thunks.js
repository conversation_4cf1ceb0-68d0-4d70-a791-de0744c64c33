import { createAsyncThunk } from '@reduxjs/toolkit';
import CleverTap from '../../../../../../utils/clevertap';
import { getIsLogin } from '../../../../../../redux/modules/user/selectors';
import { initUserInfo, loginUserByBeepApp } from '../../../../../../redux/modules/user/thunks';
import { fetchRewardList, fetchRewardDetail } from '../../../../../../redux/modules/rewards/thunks';
import { getIsWebview } from '../../../../../redux/modules/common/selectors';
import { getSelectedRewardId, getSelectedRewardType, getSelectedRewardUniquePromotionCodeId } from './selectors';

export const mounted = createAsyncThunk('rewards/consumer/rewardList/mounted', async (_, { dispatch, getState }) => {
  const isWebview = getIsWebview(getState());

  CleverTap.pushEvent('My Rewards Page - View Page');

  await dispatch(initUserInfo());

  // only display in Beep App right now
  if (isWebview) {
    await dispatch(loginUserByBeepApp());
  }

  const isLogin = getIsLogin(getState());

  if (isLogin) {
    dispatch(fetchRewardList());
  }
});

export const searchPromos = createAsyncThunk(
  'rewards/consumer/rewardList/searchPromos',
  async (searchKeyword, { dispatch }) => dispatch(fetchRewardList({ search: searchKeyword }))
);

export const selectedRewardItem = createAsyncThunk(
  'rewards/consumer/rewardList/selectedRewardItem',
  async selectedReward => selectedReward
);

export const showRewardDetailDrawer = createAsyncThunk(
  'rewards/consumer/rewardList/showRewardDetailDrawer',
  async () => {}
);

export const hideRewardDetailDrawer = createAsyncThunk(
  'rewards/consumer/rewardList/hideRewardDetailDrawer',
  async () => {}
);

export const fetchConsumerRewardDetail = createAsyncThunk(
  'rewards/consumer/rewardList/fetchConsumerRewardDetail',
  async (_, { dispatch, getState }) => {
    CleverTap.pushEvent('My Reward Voucher & Promo Details - View Page');

    const state = getState();
    const id = getSelectedRewardId(state);
    const uniquePromotionCodeId = getSelectedRewardUniquePromotionCodeId(state);
    const type = getSelectedRewardType(state);

    dispatch(fetchRewardDetail({ id, uniquePromotionCodeId, type }));
  }
);

export const clickRewardItem = createAsyncThunk(
  'rewards/consumer/rewardList/clickRewardItem',
  async (selectedReward, { dispatch }) => {
    await dispatch(selectedRewardItem(selectedReward));
    dispatch(showRewardDetailDrawer());
    dispatch(fetchConsumerRewardDetail());
  }
);
