import React, { useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { getMerchantStoreUrl } from '../../../../../../../../../common/utils';
import {
  gotoHomeSection as goToNativeHomePageSection,
  openWebViewURL,
} from '../../../../../../../../../utils/native-methods';
import { getIsWebview, getHomePageUrl } from '../../../../../../../../redux/modules/common/selectors';
import { getIsApplyNowButtonShow, getRewardSpecificMerchant } from '../../../../redux/selectors';
import Button from '../../../../../../../../../common/components/Button';
import styles from './RewardDetailFooter.module.scss';

const RewardDetailFooter = () => {
  const { t } = useTranslation(['Rewards']);
  const [isApplyNowButtonPending, setIsApplyNowButtonPending] = useState(false);
  const isWebview = useSelector(getIsWebview);
  const homePageUrl = useSelector(getHomePageUrl);
  const isApplyNowButtonShow = useSelector(getIsApplyNowButtonShow);
  const rewardSpecificMerchant = useSelector(getRewardSpecificMerchant);
  const handleClickApplyNowButton = useCallback(() => {
    setIsApplyNowButtonPending(true);
    const merchantStoreUrl =
      rewardSpecificMerchant &&
      getMerchantStoreUrl(rewardSpecificMerchant, {
        source: encodeURIComponent(window.location.href),
      });

    if (!isWebview) {
      window.location.href = rewardSpecificMerchant ? merchantStoreUrl : homePageUrl;

      return;
    }

    rewardSpecificMerchant ? openWebViewURL(merchantStoreUrl) : goToNativeHomePageSection();
  }, [rewardSpecificMerchant, isWebview, homePageUrl]);

  if (!isApplyNowButtonShow) {
    return null;
  }

  return (
    <div className={styles.RewardDetailFooter}>
      <Button
        block
        className={styles.RewardDetailFooterButton}
        data-test-id="my-reward.reward-list.apply-button"
        loading={isApplyNowButtonPending}
        disabled={isApplyNowButtonPending}
        onClick={handleClickApplyNowButton}
      >
        {t('ApplyNow')}
      </Button>
    </div>
  );
};

RewardDetailFooter.displayName = 'RewardDetailFooter';

export default RewardDetailFooter;
