import React from 'react';
import Skeleton, { SkeletonTheme } from '../../../../../../../common/components/ReactLoadingSkeleton';
import styles from './SkeletonLoader.module.scss';

const SkeletonLoader = () => (
  <SkeletonTheme duration={2}>
    <Skeleton
      count={1}
      containerClassName={styles.SkeletonLoaderMerchantDisplayNameContainer}
      className={styles.SkeletonLoaderMerchantDisplayName}
    />

    <Skeleton
      count={1}
      containerClassName={styles.SkeletonLoaderMemberCardContainer}
      className={styles.SkeletonLoaderMemberCard}
    />
    <Skeleton
      containerClassName={styles.SkeletonLoaderRewardsButtonsContainer}
      className={styles.SkeletonLoaderRewardsButton}
      count={3}
    />

    <section className={styles.SkeletonLoaderGetRewardsSection}>
      <Skeleton
        containerClassName={styles.SkeletonLoaderGetRewardsTitleContainer}
        className={styles.SkeletonLoaderGetRewardsTitle}
        count={1}
      />
      <Skeleton
        containerClassName={styles.SkeletonLoaderGetRewardsContainer}
        className={styles.SkeletonLoaderGetRewards}
        count={3}
      />
    </section>
  </SkeletonTheme>
);

SkeletonLoader.displayName = 'SkeletonLoader';

export default SkeletonLoader;
