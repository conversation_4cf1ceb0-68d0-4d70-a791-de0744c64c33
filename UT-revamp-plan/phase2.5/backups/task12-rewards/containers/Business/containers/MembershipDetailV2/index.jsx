import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useMount } from 'react-use';
import { getClassName } from '../../../../../common/utils/ui';
import { NATIVE_DARK_MODE } from '../../utils/constants';
import {
  getMerchantDisplayName,
  getIsMerchantEnabledStoreCredits,
} from '../../../../../redux/modules/merchant/selectors';
import { getIsCustomerNotFoundError } from '../../../../redux/modules/customer/selectors';
import {
  mounted,
  backButtonClicked,
  membershipTierTabClickedForCleverTap,
  closeButtonClicked,
  hideWebProfileForm,
} from './redux/thunks';
import {
  getShouldShowBackButton,
  getIsProfileModalShow,
  getIsCashbackStoreCreditCardShow,
  getShouldShowSkeletonLoader,
  getIsDisabledMembershipDetail,
} from './redux/selectors';
import Frame from '../../../../../common/components/Frame';
import PageHeader from '../../../../../common/components/PageHeader';
import MembershipTiersTabs from '../../components/MembershipTiersTabs';
import MemberCard from './components/MemberCard';
import CashbackCard from './components/CashbackCard';
import StoreCreditsCard from './components/StoreCreditsCard';
import RewardsButtons from './components/RewardsButtons';
import PointsRewards from './components/PointsRewards';
import BirthdayCampaign from './components/BirthdayCampaign';
import MyRewards from './components/MyRewards';
import MemberPrompt from './components/MemberPrompt';
import CompleteProfile from '../../../CompleteProfile';
import CustomerNotFoundResult from './components/CustomerNotFoundResult';
import DisableMembershipResult from './components/DisableMembershipResult';
import SkeletonLoader from './components/SkeletonLoader';
import styles from './MembershipDetail.module.scss';

const MembershipDetail = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const isProfileModalShow = useSelector(getIsProfileModalShow);
  const isCustomerNotFoundError = useSelector(getIsCustomerNotFoundError);
  const isDisabledMembershipDetail = useSelector(getIsDisabledMembershipDetail);
  const isMerchantEnabledStoreCredits = useSelector(getIsMerchantEnabledStoreCredits);
  const merchantDisplayName = useSelector(getMerchantDisplayName);
  const shouldShowBackButton = useSelector(getShouldShowBackButton);
  const isCashbackStoreCreditCardShow = useSelector(getIsCashbackStoreCreditCardShow);
  const shouldShowSkeletonLoader = useSelector(getShouldShowSkeletonLoader);
  const handleClickHeaderBackButton = useCallback(() => dispatch(backButtonClicked()), [dispatch]);
  const handleClickHeaderCloseButton = useCallback(() => dispatch(closeButtonClicked()), [dispatch]);
  const handleClickMembershipTierTab = useCallback(
    tierName => dispatch(membershipTierTabClickedForCleverTap(tierName)),
    [dispatch]
  );
  const handleCloseCompleteProfile = useCallback(() => dispatch(hideWebProfileForm()), [dispatch]);

  useMount(() => {
    dispatch(mounted());
  });

  return (
    <Frame>
      {shouldShowSkeletonLoader ? (
        <SkeletonLoader />
      ) : isDisabledMembershipDetail ? (
        <DisableMembershipResult />
      ) : isCustomerNotFoundError ? (
        <CustomerNotFoundResult />
      ) : (
        <>
          <PageHeader
            nativeLeftIcon="whiteBack"
            nativeStyles={{
              color: NATIVE_DARK_MODE.TEXT_COLOR,
              backgroundColor: NATIVE_DARK_MODE.HEADER_BACKGROUND_COLOR,
            }}
            className={styles.MembershipDetailPageHeader}
            leftContentClassName={styles.MembershipDetailPageHeaderLeftContent}
            titleClassName={getClassName([
              styles.MembershipDetailPageHeaderTitle,
              shouldShowBackButton ? null : styles.MembershipDetailPageHeaderTitleNoBack,
            ])}
            isWebBackButtonShow={shouldShowBackButton}
            title={merchantDisplayName}
            onBackArrowClick={shouldShowBackButton ? handleClickHeaderBackButton : handleClickHeaderCloseButton}
          />
          {!isCashbackStoreCreditCardShow ? (
            <MemberCard />
          ) : isMerchantEnabledStoreCredits ? (
            <StoreCreditsCard />
          ) : (
            <CashbackCard />
          )}
          <RewardsButtons />
          <PointsRewards />
          <BirthdayCampaign />
          <MyRewards />
          <section className={styles.MembershipDetailBenefitsSection}>
            <h2 className={styles.MembershipDetailBenefitsTitle}>{t('MembershipBenefits')}</h2>
            <MembershipTiersTabs onClickMembershipTierTab={handleClickMembershipTierTab} />
          </section>
        </>
      )}

      <MemberPrompt />
      <CompleteProfile show={isProfileModalShow} onClose={handleCloseCompleteProfile} />
    </Frame>
  );
};

MembershipDetail.displayName = 'MembershipDetail';

export default MembershipDetail;
