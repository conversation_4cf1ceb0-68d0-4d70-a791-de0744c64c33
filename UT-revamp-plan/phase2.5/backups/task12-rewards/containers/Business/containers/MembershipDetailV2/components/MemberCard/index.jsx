import React, { useState, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation, Trans } from 'react-i18next';
import { Info, User } from 'phosphor-react';
import { useMeasure } from 'react-use';
import { getClassName } from '../../../../../../../common/utils/ui';
import { getMemberCardProgressStyles } from '../../utils';
import { getIsLogin } from '../../../../../../../redux/modules/user/selectors';
import { getCustomerTierLevelName } from '../../../../../../redux/modules/customer/selectors';
import {
  getMemberCardStyles,
  getMerchantMembershipTierList,
  getMembershipTierListLength,
  getCustomerMemberTierProgressInfo,
  getCustomerCurrentStatusPromptI18nInfo,
} from '../../redux/selectors';
import { showProfileForm } from '../../redux/thunks';
import MemberIcon from '../../../../components/MemberIcon';
import Button from '../../../../../../../common/components/Button';
import styles from './MemberCard.module.scss';

const INFO_ICON_WIDTH = 18;
const MemberCard = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const [ref, { width }] = useMeasure();
  const infoIconRef = useRef(null);
  const promptToolTipRef = useRef(null);
  const isLogin = useSelector(getIsLogin);
  const customerTierLevelName = useSelector(getCustomerTierLevelName);
  const memberCardStyles = useSelector(getMemberCardStyles);
  const merchantMembershipTierList = useSelector(getMerchantMembershipTierList);
  const membershipTierListLength = useSelector(getMembershipTierListLength);
  const customerMemberTierProgressInfo = useSelector(getCustomerMemberTierProgressInfo);
  const customerCurrentStatusPromptI18nInfo = useSelector(getCustomerCurrentStatusPromptI18nInfo);
  const { messageI18nKey, messageI18nParams } = customerCurrentStatusPromptI18nInfo || {};
  const [promptToolTipShown, setPromptToolTipShown] = useState(false);
  const [promptToolTipTransform, setPromptToolTipTransform] = useState(null);
  const calculatePromptToolTipTransform = useCallback(() => {
    const infoIconDistanceWithRightEdge = window.innerWidth - infoIconRef.current.getBoundingClientRect().right;
    const promptToolTipWidth = promptToolTipRef.current.offsetWidth;
    const transform =
      infoIconDistanceWithRightEdge > promptToolTipWidth / 2
        ? 'translateX(-75%)'
        : `translateX(-${promptToolTipWidth - INFO_ICON_WIDTH - infoIconDistanceWithRightEdge / 2}px)`;

    setPromptToolTipTransform(transform);
  }, []);
  const handleClickCurrentMemberTierPromptToolTip = useCallback(() => {
    calculatePromptToolTipTransform();
    setPromptToolTipShown(!promptToolTipShown);
  }, [calculatePromptToolTipTransform, promptToolTipShown, setPromptToolTipShown]);
  const handleClickViewProfileButton = useCallback(() => dispatch(showProfileForm()), [dispatch]);

  return (
    <section className={styles.MemberCardSection}>
      <div className={styles.MemberCard} style={memberCardStyles}>
        <div className={styles.MemberCardTop}>
          <h2 className={styles.MemberCardCustomerTierName}>{customerTierLevelName}</h2>
          {isLogin && (
            <Button
              data-test-id="rewards.business.membership-detail.member-card.view-profile-button"
              className={styles.MemberCardViewProfileButton}
              contentClassName={styles.MemberCardViewProfileButtonContent}
              type="text"
              theme="ghost"
              onClick={handleClickViewProfileButton}
            >
              <User size={14} />
              <span className={styles.MemberCardViewProfileText}>{t('ViewProfile')}</span>
            </Button>
          )}
        </div>
        <div className={styles.MemberCardTiersProgress}>
          {customerMemberTierProgressInfo && (
            <div role="progressbar" className={styles.MemberCardProgress} aria-label="Member Progress Bar" ref={ref}>
              <div
                className={styles.MemberCardProgressBar}
                style={getMemberCardProgressStyles(width, membershipTierListLength, customerMemberTierProgressInfo)}
              />
            </div>
          )}

          <ul className={styles.MemberCardTiers}>
            {merchantMembershipTierList.map(merchantMembershipTier => {
              const { level, iconColorPalettes } = merchantMembershipTier;

              return (
                <li key={`member-card-level-${level}`}>
                  <MemberIcon
                    className={styles.MemberCardTierIcon}
                    id={`member-level-icon-${level}`}
                    crownStartColor={iconColorPalettes.crown.startColor}
                    crownEndColor={iconColorPalettes.crown.endColor}
                    backgroundStartColor={iconColorPalettes.background.startColor}
                    backgroundEndColor={iconColorPalettes.background.endColor}
                    strokeColor={iconColorPalettes.strokeColor}
                  />
                </li>
              );
            })}
          </ul>
        </div>
        {customerCurrentStatusPromptI18nInfo && (
          <div className={styles.MemberCardTierProgressPromptContainer}>
            <p className={styles.MemberCardTierProgressPrompt}>
              <Trans t={t} i18nKey={messageI18nKey} values={messageI18nParams} components={<strong />} />
            </p>

            <button
              data-test-id="rewards.business.membership-detail.member-card.progress-info-tooltip-button"
              className={getClassName([
                styles.MemberCardTierProgressPromptToolTipContainer,
                promptToolTipShown ? styles.MemberCardTierProgressPromptToolTipContainer__show : null,
              ])}
              onClick={handleClickCurrentMemberTierPromptToolTip}
            >
              <Info size={INFO_ICON_WIDTH} ref={infoIconRef} />
              <div
                className={styles.MemberCardTierProgressPromptToolTip}
                style={promptToolTipTransform && { transform: promptToolTipTransform }}
                ref={promptToolTipRef}
              >
                <span className={styles.MemberCardTierProgressPromptToolTipText}>{t('LevelUpdateRuleText')}</span>
              </div>
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

MemberCard.displayName = 'MemberCard';

export default MemberCard;
