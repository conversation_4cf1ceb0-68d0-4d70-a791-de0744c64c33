import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { getMerchantDisplayName } from '../../../../../../../redux/modules/merchant/selectors';
import { closeButtonClicked } from '../../redux/thunks';
import PageHeader from '../../../../../../../common/components/PageHeader';
import ErrorResult from '../../../../components/ErrorResult';

const CustomerNotFoundResult = () => {
  const { t } = useTranslation('Rewards');
  const dispatch = useDispatch();
  const merchantDisplayName = useSelector(getMerchantDisplayName);
  const handleClickHeaderCloseButton = useCallback(() => dispatch(closeButtonClicked()), [dispatch]);

  return (
    <ErrorResult
      mountAtRoot
      title={t('MerchantDisabledMembershipTitle')}
      content={t('MerchantDisabledMembershipDescription')}
      isCloseButtonShow={false}
      header={
        <PageHeader
          isWebBackButtonShow={false}
          title={merchantDisplayName}
          onBackArrowClick={handleClickHeaderCloseButton}
        />
      }
    />
  );
};

CustomerNotFoundResult.displayName = 'CustomerNotFoundResult';

export default CustomerNotFoundResult;
