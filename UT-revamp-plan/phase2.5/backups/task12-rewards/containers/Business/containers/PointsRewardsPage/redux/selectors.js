import { createSelector } from 'reselect';
import { getIsLoadMerchantRequestCompleted } from '../../../../../../redux/modules/merchant/selectors';
import { getIsLoadCustomerRequestCompleted } from '../../../../../redux/modules/customer/selectors';
import { getIsLoadPointsRewardListCompleted } from '../../../redux/common/selectors';

export const getShouldSkeletonLoaderShow = createSelector(
  getIsLoadMerchantRequestCompleted,
  getIsLoadCustomerRequestCompleted,
  getIsLoadPointsRewardListCompleted,
  (isLoadMerchantRequestCompleted, isLoadCustomerRequestCompleted, isLoadPointsRewardListCompleted) =>
    !isLoadMerchantRequestCompleted || !isLoadCustomerRequestCompleted || !isLoadPointsRewardListCompleted
);
