import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles } from 'react-use';
import { useTranslation } from 'react-i18next';
import {
  UNIQUE_PROMO_STATUS_I18KEYS,
  REWARDS_APPLIED_SOURCE_I18KEYS,
} from '../../../../../common/utils/rewards/constants';
import { actions as uniquePromoDetailActions } from './redux';
import {
  getUniquePromoFormatDiscountValue,
  getUniquePromoPromotionName,
  getUniquePromoLimitations,
  getIsUniquePromoUnAvailable,
  getUniquePromoStatus,
  getUniquePromoExpiringDaysI18n,
  getShouldSkeletonLoaderShow,
  getUniquePromoDetailContentList,
} from './redux/selectors';
import { backButtonClicked, mounted } from './redux/thunks';
import Frame from '../../../../../common/components/Frame';
import PageHeader from '../../../../../common/components/PageHeader';
import Tag from '../../../../../common/components/Tag';
import Ticket from '../../../../../common/components/Ticket';
import SkeletonLoader from './components/SkeletonLoader';
import Article from '../../../../../common/components/Article';
import styles from './UniquePromoDetail.module.scss';

const UniquePromoDetail = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const shouldSkeletonLoaderShow = useSelector(getShouldSkeletonLoaderShow);
  const formatDiscountValue = useSelector(getUniquePromoFormatDiscountValue);
  const name = useSelector(getUniquePromoPromotionName);
  const limitations = useSelector(getUniquePromoLimitations);
  const isUniquePromoUnAvailable = useSelector(getIsUniquePromoUnAvailable);
  const status = useSelector(getUniquePromoStatus);
  const expiringDaysI18n = useSelector(getUniquePromoExpiringDaysI18n);
  const uniquePromoDetailContentList = useSelector(getUniquePromoDetailContentList);
  const handleClickHeaderBackButton = useCallback(() => dispatch(backButtonClicked()), [dispatch]);

  useLifecycles(
    () => {
      dispatch(mounted());
    },
    () => {
      dispatch(uniquePromoDetailActions.loadUniquePromoDetailRequestReset());
    }
  );

  return (
    <Frame>
      <PageHeader title={t('UniquePromoDetails')} onBackArrowClick={handleClickHeaderBackButton} />

      {shouldSkeletonLoaderShow ? (
        <SkeletonLoader />
      ) : (
        <>
          <Ticket
            orientation="vertical"
            size="large"
            showBorder={false}
            className={styles.UniquePromoDetailTicket}
            mainClassName={styles.UniquePromoDetailTicketMain}
            stubClassName={styles.UniquePromoDetailTicketStub}
            main={
              <div className={styles.UniquePromoDetailTicketMainContent}>
                <data className={styles.UniquePromoDetailTicketDiscountValue} value={formatDiscountValue}>
                  {t('DiscountValueText', { discount: formatDiscountValue })}
                </data>
                <h2 className={styles.UniquePromoDetailTicketName}>{name}</h2>
              </div>
            }
            stub={
              <>
                <ul className={styles.UniquePromoDetailLimitations}>
                  {limitations.map(limitation => (
                    <li className={styles.UniquePromoDetailLimitation} key={limitation.key}>
                      {t(limitation.i18nKey, limitation.params)}
                    </li>
                  ))}
                </ul>

                {isUniquePromoUnAvailable ? (
                  <Tag className={styles.UniquePromoDetailTicketStatusTag}>
                    {t(UNIQUE_PROMO_STATUS_I18KEYS[status])}
                  </Tag>
                ) : (
                  expiringDaysI18n && <Tag color="red">{t(expiringDaysI18n.i18nKey, expiringDaysI18n.params)}</Tag>
                )}
              </>
            }
          />

          {uniquePromoDetailContentList.map((contentItem, index) => {
            const { title, titleDescription, articleContentList } = contentItem;

            return (
              <section
                className={styles.UniquePromoDetailContentArticleSection}
                // eslint-disable-next-line react/no-array-index-key
                key={`rewardsUniquePromoDetail-article-${index}`}
              >
                <Article
                  title={title}
                  titleDescription={titleDescription}
                  articleContentList={
                    articleContentList &&
                    articleContentList.map(item => {
                      const { subtitle, description, uniquePromoRedeemOnlineList } = item;
                      const articleContent = { subtitle, description, content: null };

                      if (uniquePromoRedeemOnlineList) {
                        articleContent.content = (
                          <ul className={styles.UniquePromoDetailHowToUseRedeemOnlineList}>
                            {uniquePromoRedeemOnlineList.map(redeemOnlineChannel => (
                              <li
                                key={`rewardsUniquePromoDetail-redeemOnlineChannel-${redeemOnlineChannel}`}
                                className={styles.UniquePromoDetailHowToUseRedeemOnlineItem}
                              >
                                {t(REWARDS_APPLIED_SOURCE_I18KEYS[redeemOnlineChannel])}
                              </li>
                            ))}
                          </ul>
                        );
                      }

                      return articleContent;
                    })
                  }
                />
              </section>
            );
          })}
        </>
      )}
    </Frame>
  );
};

UniquePromoDetail.displayName = 'UniquePromoDetail';

export default UniquePromoDetail;
