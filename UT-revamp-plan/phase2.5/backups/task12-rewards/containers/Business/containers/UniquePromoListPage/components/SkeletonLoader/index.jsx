import React from 'react';
import Skeleton, { SkeletonTheme } from '../../../../../../../common/components/ReactLoadingSkeleton';
import styles from './SkeletonLoader.module.scss';

const SkeletonLoader = () => (
  <SkeletonTheme duration={2}>
    <section className={styles.SkeletonLoaderUniquePromoListSection}>
      <Skeleton
        containerClassName={styles.SkeletonLoaderUniquePromoListContainer}
        className={styles.SkeletonLoaderUniquePromoList}
        count={6}
      />
    </section>
  </SkeletonTheme>
);

SkeletonLoader.displayName = 'SkeletonLoader';

export default SkeletonLoader;
