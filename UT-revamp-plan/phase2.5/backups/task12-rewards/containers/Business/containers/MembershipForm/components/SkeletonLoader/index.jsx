import React from 'react';
import Skeleton, { SkeletonTheme } from '../../../../../../../common/components/ReactLoadingSkeleton';
import SquareSkeleton from '../../../../../../../common/components/SquareSkeleton';
import styles from './SkeletonLoader.module.scss';

const SkeletonLoader = () => (
  <SkeletonTheme duration={2}>
    <section className="tw-flex tw-flex-col tw-items-center tw-justify-center">
      <SquareSkeleton
        wrapperClassName={styles.SkeletonLoaderRewardsBannerWrapper}
        containerClassName="tw-flex tw-flex-1"
        className={styles.SkeletonLoaderRewardsBanner}
      />
      <div className="tw-flex tw-items-center tw-w-full tw-px-12 sm:tw-px-12px tw-py-24 sm:tw-py-24px">
        <Skeleton
          className="tw-flex tw-items-center tw-py-4 sm:tw-py-4px tw-my-2 sm:tw-my-2px"
          count={6}
          containerClassName="tw-flex-1 tw-flex-col"
        />
      </div>
    </section>
  </SkeletonTheme>
);

SkeletonLoader.displayName = 'SkeletonLoader';

export default SkeletonLoader;
