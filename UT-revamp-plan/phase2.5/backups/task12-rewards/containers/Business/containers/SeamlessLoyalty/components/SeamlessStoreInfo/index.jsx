import React from 'react';
import { useSelector } from 'react-redux';
import { ObjectFitImage } from '../../../../../../../common/components/Image';
import { getMerchantLogo, getMerchantDisplayName } from '../../../../../../../redux/modules/merchant/selectors';
import { getIsCustomerCashbackAvailable } from '../../redux/selectors';
import styles from './SeamlessStoreInfo.module.scss';

const SeamlessStoreInfo = () => {
  const merchantLogo = useSelector(getMerchantLogo);
  const merchantDisplayName = useSelector(getMerchantDisplayName);
  const isCustomerCashbackAvailable = useSelector(getIsCustomerCashbackAvailable);

  return (
    <section
      className={`tw-flex ${
        !isCustomerCashbackAvailable
          ? styles.SeamlessLoyaltyContentExist
          : `${styles.SeamlessLoyaltyContentNoExist} tw-bg-gray-200`
      } tw-flex-col tw-items-center tw-justify-center tw-flex-shrink-0 tw-px-16 sm:tw-px-16px tw-pt-24 sm:tw-pt-24px`}
    >
      {isCustomerCashbackAvailable ? (
        <ObjectFitImage className={styles.SeamlessLoyaltyStoreLogo} src={merchantLogo} />
      ) : null}
      <h1 className={styles.SeamlessLoyaltyStoreName}>{merchantDisplayName}</h1>
    </section>
  );
};

SeamlessStoreInfo.displayName = 'SeamlessStoreInfo';

export default SeamlessStoreInfo;
