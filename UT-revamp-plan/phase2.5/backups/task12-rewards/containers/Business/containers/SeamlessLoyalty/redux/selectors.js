import { createSelector } from '@reduxjs/toolkit';
import { getQueryString } from '../../../../../../common/utils';
import { API_REQUEST_STATUS } from '../../../../../../common/utils/constants';
import {
  getLoadMerchantRequestError,
  getIsLoadMerchantRequestCompleted,
  getIsMalaysianMerchant,
  getIsMerchantMembershipEnabled,
  getIsMerchantEnabledCashback,
} from '../../../../../../redux/modules/merchant/selectors';
import { getIsLogin } from '../../../../../../redux/modules/user/selectors';
import { getIsWeb } from '../../../../../redux/modules/common/selectors';
import { getCustomerCashback } from '../../../../../redux/modules/customer/selectors';
import {
  getConfirmSharingConsumerInfoStatus,
  getConfirmSharingConsumerInfoError,
} from '../../../redux/common/selectors';

export const getSeamlessLoyaltyRequestId = () => getQueryString('shareInfoReqId');

export const getUpdateSharingConsumerInfoError = state =>
  state.business.seamlessLoyalty.updateSharingConsumerInfoRequest.error;

/**
 * Derived selectors
 */
export const getIsConfirmSharingConsumerInfoCompleted = createSelector(
  getConfirmSharingConsumerInfoStatus,
  confirmSharingConsumerInfoStatus =>
    [API_REQUEST_STATUS.FULFILLED, API_REQUEST_STATUS.REJECTED].includes(confirmSharingConsumerInfoStatus)
);

export const getIsSharingConsumerInfoEnabled = createSelector(
  getIsLogin,
  getSeamlessLoyaltyRequestId,
  (isLogin, requestId) => isLogin && !!requestId
);

export const getIsAllInitialRequestsCompleted = createSelector(
  getIsConfirmSharingConsumerInfoCompleted,
  getIsLoadMerchantRequestCompleted,
  (isConfirmSharingConsumerInfoCompleted, isLoadMerchantRequestCompleted) =>
    isConfirmSharingConsumerInfoCompleted && isLoadMerchantRequestCompleted
);

export const getAnyInitialRequestError = createSelector(
  getUpdateSharingConsumerInfoError,
  getConfirmSharingConsumerInfoError,
  getLoadMerchantRequestError,
  (updateSharingConsumerInfoError, confirmSharingConsumerInfoError, loadMerchantRequestError) =>
    updateSharingConsumerInfoError || confirmSharingConsumerInfoError || loadMerchantRequestError
);

export const getIsMalaysianSeamlessLoyaltyWebShow = createSelector(
  getIsWeb,
  getIsMalaysianMerchant,
  (isWeb, isMalaysianMerchant) => isWeb && isMalaysianMerchant
);

export const getIsNotMembershipSeamlessLoyaltyShow = createSelector(
  getIsWeb,
  getIsLoadMerchantRequestCompleted,
  getIsMalaysianMerchant,
  getIsMerchantMembershipEnabled,
  (isWeb, isLoadMerchantRequestCompleted, isMalaysianMerchant, isMerchantMembershipEnabled) =>
    isLoadMerchantRequestCompleted && !isMerchantMembershipEnabled && (isMalaysianMerchant ? !isWeb : true)
);

export const getIsCustomerCashbackAvailable = createSelector(
  getIsMerchantEnabledCashback,
  getCustomerCashback,
  (isMerchantEnabledCashback, customerCashback) => isMerchantEnabledCashback && customerCashback > 0
);
