import React from 'react';
import hoistNonReactStatic from 'hoist-non-react-statics';
/**
 * This is a HOC to help to carry arbitrary data attributes (the attributes that starts with "data-") to inner component.
 */
export default Component => {
  const WithDataAttr = props => {
    const dataAttrs = {};
    Object.keys(props).forEach(propName => {
      if (/^data-/.test(propName)) {
        // eslint-disable-next-line react/destructuring-assignment
        dataAttrs[propName] = props[propName];
      }
    });
    // eslint-disable-next-line react/jsx-props-no-spreading
    return <Component {...props} dataAttributes={dataAttrs} />;
  };
  hoistNonReactStatic(WithDataAttr, Component);
  WithDataAttr.displayName = 'WithDataAttr';
  return WithDataAttr;
};
