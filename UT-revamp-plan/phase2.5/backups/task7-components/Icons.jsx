/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';

export const IconPending = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path d="M13.26 3C8.17 2.86 4 6.95 4 12H2.21c-.45 0-.67.54-.35.85l2.79 2.8c.2.2.51.2.71 0l2.79-2.8c.31-.31.09-.85-.36-.85H6c0-3.9 3.18-7.05 7.1-7 3.72.05 6.85 3.18 6.9 6.9.05 3.91-3.1 7.1-7 7.1-1.61 0-3.1-.55-4.28-1.48-.4-.31-.96-.28-1.32.08-.42.42-.39 1.13.08 1.49C9 20.29 10.91 21 13 21c5.05 0 9.14-4.17 9-9.26-.13-4.69-4.05-8.61-8.74-8.74zm-.51 5c-.41 0-.75.34-.75.75v3.68c0 .35.19.68.49.86l3.12 1.85c.36.21.82.09 1.03-.26.21-.36.09-.82-.26-1.03l-2.88-1.71v-3.4c0-.4-.34-.74-.75-.74z" />
    </svg>
  </i>
);
IconPending.displayName = 'IconPending';

export const IconEarned = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4 11h-3v3c0 .55-.45 1-1 1s-1-.45-1-1v-3H8c-.55 0-1-.45-1-1s.45-1 1-1h3V8c0-.55.45-1 1-1s1 .45 1 1v3h3c.55 0 1 .45 1 1s-.45 1-1 1z" />
    </svg>
  </i>
);
IconEarned.displayName = 'IconEarned';

export const IconChecked = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM9.29 16.29L5.7 12.7c-.39-.39-.39-1.02 0-1.41.39-.39 1.02-.39 1.41 0L10 14.17l6.88-6.88c.39-.39 1.02-.39 1.41 0 .39.39.39 1.02 0 1.41l-7.59 7.59c-.38.39-1.02.39-1.41 0z" />
    </svg>
  </i>
);
IconChecked.displayName = 'IconChecked';

export const IconDone = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
    </svg>
  </i>
);
IconDone.displayName = 'IconDone';

export const IconTicket = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path d="M18 17H6v-2h12v2zm0-4H6v-2h12v2zm0-4H6V7h12v2zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2v20z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconTicket.displayName = 'IconTicket';

export const IconCart = props => (
  <i {...props}>
    <svg width="42" height="32" viewBox="0 0 42 32" xmlns="http://www.w3.org/2000/svg">
      <g fillRule="evenodd">
        <path d="M39.375 9.042H34.02L26.93.902C25.986-.18 24.327-.307 23.23.622S22 3.18 22.944 4.265l4.16 4.777h-12.2l4.16-4.777C20 3.18 19.872 1.55 18.77.622a2.66 2.66 0 0 0-3.702.28l-7.09 8.14H2.625C1.175 9.042 0 10.198 0 11.625s1.175 2.583 2.625 2.583h.03c0 .08-.023.14-.01.212l2.26 13.342C5.22 29.638 6.852 31 8.786 31h24.427c1.934 0 3.567-1.362 3.884-3.238l2.26-13.342c.012-.07-.01-.13-.01-.212h.03c1.45 0 2.625-1.157 2.625-2.583s-1.175-2.583-2.625-2.583zM14.81 24.37c0 .7-.542 1.25-1.212 1.25s-1.212-.56-1.212-1.25v-6.245c0-.7.542-1.25 1.212-1.25s1.212.56 1.212 1.25v6.245zm4.898-.376c0 .7-.542 1.25-1.212 1.25s-1.212-.56-1.212-1.25v-6.245c0-.7.542-1.25 1.212-1.25s1.212.56 1.212 1.25v6.245zm4.828.416c0 .7-.542 1.25-1.212 1.25s-1.212-.56-1.212-1.25v-6.245c0-.7.542-1.25 1.212-1.25s1.212.56 1.212 1.25v6.245zm5.703-.307c0 .7-.542 1.25-1.212 1.25s-1.212-.56-1.212-1.25v-6.245c0-.7.542-1.25 1.212-1.25s1.212.56 1.212 1.25V24.1z" />
      </g>
    </svg>
  </i>
);
IconCart.displayName = 'IconCart';

export const IconLocation = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconLocation.displayName = 'IconLocation';

export const IconClose = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconClose.displayName = 'IconClose';

export const IconLeftArrow = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" />
    </svg>
  </i>
);
IconLeftArrow.displayName = 'IconLeftArrow';

export const IconKeyArrowDown = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
    </svg>
  </i>
);
IconKeyArrowDown.displayName = 'IconKeyArrowDown';

export const IconNext = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconNext.displayName = 'IconNext';

export const IconExpandMore = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
      <path d="M24 24H0V0h24v24z" fill="none" opacity=".87" />
      <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6-1.41-1.41z" />
    </svg>
  </i>
);
IconExpandMore.displayName = 'IconExpandMore';

export const IconDelete = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zm2.46-7.12l1.41-1.41L12 12.59l2.12-2.12 1.41 1.41L13.41 14l2.12 2.12-1.41 1.41L12 15.41l-2.12 2.12-1.41-1.41L10.59 14l-2.13-2.12zM15.5 4l-1-1h-5l-1 1H5v2h14V4z" />
      <path fill="none" d="M0 0h24v24H0z" />
    </svg>
  </i>
);
IconDelete.displayName = 'IconDelete';

export const IconInfo = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
    </svg>
  </i>
);
IconInfo.displayName = 'IconInfo';

export const IconPin = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M18 8c0-3.31-2.69-6-6-6S6 4.69 6 8c0 4.5 6 11 6 11s6-6.5 6-11zm-8 0c0-1.1.9-2 2-2s2 .9 2 2-.89 2-2 2c-1.1 0-2-.9-2-2zM5 20v2h14v-2H5z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconPin.displayName = 'IconPin';

export const IconEdit = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconEdit.displayName = 'IconEdit';

export const IconInfoOutline = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
    </svg>
  </i>
);
IconInfoOutline.displayName = 'IconInfoOutline';

export const IconMotorcycle = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M12.72 11l-2 2h-.77l-.25-.69c-.18-.48-.42-.92-.72-1.31h3.74m2.69-6H11v2h3.59l2 2H5c-2.8 0-5 2.2-5 5s2.2 5 5 5c2.46 0 4.45-1.69 4.9-4h1.65l2.77-2.77c-.21.54-.32 1.14-.32 1.77 0 2.8 2.2 5 5 5s5-2.2 5-5c0-2.65-1.97-4.77-4.56-4.97L15.41 5zM19 17c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zM5 17c-1.63 0-3-1.37-3-3s1.37-3 3-3c1.28 0 2.4.85 2.82 2H5v2h2.82C7.4 16.15 6.28 17 5 17z" />
    </svg>
  </i>
);
IconMotorcycle.displayName = 'IconMotorcycle';

export const IconAccessTime = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
    </svg>
  </i>
);
IconAccessTime.displayName = 'IconAccessTime';

export const IconSearch = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconSearch.displayName = 'IconSearch';

export const IconGpsFixed = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3c-.46-4.17-3.77-7.48-7.94-7.94V1h-2v2.06C6.83 3.52 3.52 6.83 3.06 11H1v2h2.06c.46 4.17 3.77 7.48 7.94 7.94V23h2v-2.06c4.17-.46 7.48-3.77 7.94-7.94H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" />
    </svg>
  </i>
);
IconGpsFixed.displayName = 'IconGpsFixed';

export const IconBookmarks = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M19 18l2 1V3c0-1.1-.9-2-2-2H8.99C7.89 1 7 1.9 7 3h10c1.1 0 2 .9 2 2v13zM15 5H5c-1.1 0-2 .9-2 2v16l7-3 7 3V7c0-1.1-.9-2-2-2z" />
    </svg>
  </i>
);
IconBookmarks.displayName = 'IconBookmarks';

export const IconHome = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconHome.displayName = 'IconHome';

export const IconCropFree = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M3 5v4h2V5h4V3H5c-1.1 0-2 .9-2 2zm2 10H3v4c0 1.1.9 2 2 2h4v-2H5v-4zm14 4h-4v2h4c1.1 0 2-.9 2-2v-4h-2v4zm0-16h-4v2h4v4h2V5c0-1.1-.9-2-2-2z" />
    </svg>
  </i>
);
IconCropFree.displayName = 'IconCropFree';

export const IconAccountCircle = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconAccountCircle.displayName = 'IconAccountCircle';

export const IconLabelOutline = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0V0z" fill="none" />
      <path d="M17.63 5.84C17.27 5.33 16.67 5 16 5L5 5.01C3.9 5.01 3 5.9 3 7v10c0 1.1.9 1.99 2 1.99L16 19c.67 0 1.27-.33 1.63-.84L22 12l-4.37-6.16zM16 17H5V7h11l3.55 5L16 17z" />
    </svg>
  </i>
);
IconLabelOutline.displayName = 'IconLabelOutline';

export const IconNotificationActive = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M7.58 4.08L6.15 2.65C3.75 4.48 2.17 7.3 2.03 10.5h2c.15-2.65 1.51-4.97 3.55-6.42zm12.39 6.42h2c-.15-3.2-1.73-6.02-4.12-7.85l-1.42 1.43c2.02 1.45 3.39 3.77 3.54 6.42zM18 11c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2v-5zm-6 11c.14 0 .27-.01.4-.04.65-.14 1.18-.58 1.44-1.18.1-.24.15-.5.15-.78h-4c.01 1.1.9 2 2.01 2z" />
    </svg>
  </i>
);
IconNotificationActive.displayName = 'IconNotificationActive';

export const IconAttachMoney = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconAttachMoney.displayName = 'IconAttachMoney';

export const IconLocalOffer = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z" />
    </svg>
  </i>
);
IconLocalOffer.displayName = 'IconLocalOffer';

export const IconBookmark = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2z" />
      <path d="M0 0h24v24H0z" fill="none" />
    </svg>
  </i>
);
IconBookmark.displayName = 'IconBookmark';

export const IconWallet = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M21 18v1c0 1.1-.9 2-2 2H5c-1.11 0-2-.9-2-2V5c0-1.1.89-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.11 0-2 .9-2 2v8c0 1.1.89 2 2 2h9zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z" />
    </svg>
  </i>
);
IconWallet.displayName = 'IconWallet';

export const IconScanner = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 16 16" width="24">
      <path d="M.6 9.7c.4 0 .6.3.6.6h0v2.2c0 .1.1.2.2.2h2.2c.4 0 .6.3.6.6s-.2.7-.5.7h0-2.2A1.54 1.54 0 0 1 0 12.5h0v-2.2a.65.65 0 0 1 .6-.6zm12.8 0c.4 0 .6.3.6.6h0v2.2a1.54 1.54 0 0 1-1.5 1.5h0-2.2c-.4 0-.6-.3-.6-.6a.65.65 0 0 1 .6-.6h2.2c.1 0 .2-.1.2-.2h0v-2.2a.68.68 0 0 1 .7-.7zm0-3.2c.3 0 .6.2.6.5s-.3.5-.6.5h0H.6C.3 7.5 0 7.3 0 7s.3-.5.6-.5h12.8zM12.5 0A1.54 1.54 0 0 1 14 1.5h0v2.2c0 .4-.3.6-.6.6s-.7-.3-.7-.6h0V1.5c0-.1-.1-.2-.2-.2h0-2.2c-.3 0-.6-.3-.6-.7s.3-.6.6-.6h2.2zM3.7 0a.65.65 0 0 1 .6.6c0 .3-.3.7-.6.7h0-2.2c-.1 0-.2.1-.2.2h0v2.2c0 .3-.3.6-.7.6S0 4 0 3.7h0V1.5A1.54 1.54 0 0 1 1.5 0h2.2z" />
    </svg>
  </i>
);
IconScanner.displayName = 'IconScanner';

export const IconInsertPhoto = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
    </svg>
  </i>
);
IconInsertPhoto.displayName = 'IconInsertPhoto';

export const IconAddAddress = props => (
  <i {...props}>
    <svg width="32px" height="32px" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <polygon
          id="path-1"
          points="16.82 21.82 16.82 16.82 21.82 16.82 21.82 15.18 16.82 15.18 16.82 10.18 15.18 10.18 15.18 15.18 10.18 15.18 10.18 16.82 15.18 16.82 15.18 21.82"
        />
      </defs>
      <g id="Beep-Delivery-2.0" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="00---Deliver-To---Add-Address" transform="translate(-14.000000, -82.000000)">
          <g id="Location-List" transform="translate(0.000000, 60.000000)">
            <g id="Molecules/Location/Default-Copy-4" transform="translate(0.000000, 6.000000)">
              <g id="Icon" transform="translate(14.000000, 16.000000)">
                <mask id="mask-2" fill="white">
                  <use xlinkHref="#path-1" />
                </mask>
                <use id="" fill="#000000" fillRule="nonzero" xlinkHref="#path-1" />
                <g id="Variables/Palette/Neutrals/05-Manatee" mask="url(#mask-2)" fill="#8D90A1">
                  <rect id="Color" x="0" y="0" width="32" height="32" />
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  </i>
);
IconAddAddress.displayName = 'IconAddAddress';

export const IconVoucherTicket = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" enableBackground="new 0 0 24 24" height="24" viewBox="0 0 24 24" width="24">
      <g>
        <rect fill="none" height="24" width="24" x="0" />
      </g>
      <g>
        <g>
          <g>
            <path d="M22,10V6c0-1.11-0.9-2-2-2H4C2.9,4,2.01,4.89,2.01,6v4C3.11,10,4,10.9,4,12s-0.89,2-2,2v4c0,1.1,0.9,2,2,2h16 c1.1,0,2-0.9,2-2v-4c-1.1,0-2-0.9-2-2S20.9,10,22,10z M13,17.5h-2v-2h2V17.5z M13,13h-2v-2h2V13z M13,8.5h-2v-2h2V8.5z" />
          </g>
        </g>
      </g>
    </svg>
  </i>
);
IconVoucherTicket.displayName = 'IconVoucherTicket';

export const IconWrappedClose = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black" width="24px" height="24px">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M14.59 8L12 10.59 9.41 8 8 9.41 10.59 12 8 14.59 9.41 16 12 13.41 14.59 16 16 14.59 13.41 12 16 9.41 14.59 8zM12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
    </svg>
  </i>
);
IconWrappedClose.displayName = 'IconWrappedClose';

export const IconStar = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24px" height="24px">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
    </svg>
  </i>
);
IconStar.displayName = 'IconStar';

export const IconError = props => (
  <i {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px">
      <path d="M0 0h24v24H0z" fill="none" />
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
    </svg>
  </i>
);
IconError.displayName = 'IconError';
