export const APP_TYPES = {
  CLEAR_API_ERROR: 'ORDERING/APP/CLEAR_API_ERROR',
  PUT_ERROR: 'ORDERING/APP/PUT_ERROR',

  // fetch onlineStoreInfo
  FETCH_ONLINESTOREINFO_REQUEST: 'ORDERING/APP/FETCH_ONLINESTOREINFO_REQUEST',
  <PERSON><PERSON><PERSON>_ONLINESTOREINFO_SUCCESS: 'ORDERING/APP/FETCH_ONLINESTOREINFO_SUCCESS',
  FETCH_ONLINESTOREINFO_FAILURE: 'ORDERING/APP/FETCH_ONLINESTOREINFO_FAILURE',

  // fetch coreBusiness
  RESET_COREBUSINESS_STATUS: 'ORDERING/APP/RESET_COREBUSINESS_STATUS',
  FETCH_COREBUSINESS_REQUEST: 'ORDERING/APP/FETCH_COREBUSINESS_REQUEST',
  FETCH_COREBUSINESS_SUCCESS: 'ORDERING/APP/FETCH_COREBUSINESS_SUCCESS',
  FETCH_COREBUSINESS_FAILURE: 'ORDERING/APP/FETCH_COREBUSINESS_FAILURE',

  // set as guest
  SET_CONSUMER_AS_GUEST_REQUEST: 'ORDERING/APP/SET_CONSUMER_AS_GUEST_REQUEST',
  SET_CONSUMER_AS_GUEST_SUCCESS: 'ORDERING/APP/SET_CONSUMER_AS_GUEST_SUCCESS',
  SET_CONSUMER_AS_GUEST_FAILURE: 'ORDERING/APP/SET_CONSUMER_AS_GUEST_FAILURE',

  // fetch login status
  FETCH_LOGIN_STATUS_REQUEST: 'ORDERING/APP/FETCH_LOGIN_STATUS_REQUEST',
  FETCH_LOGIN_STATUS_SUCCESS: 'ORDERING/APP/FETCH_LOGIN_STATUS_SUCCESS',
  FETCH_LOGIN_STATUS_FAILURE: 'ORDERING/APP/FETCH_LOGIN_STATUS_FAILURE',

  // login
  CREATE_LOGIN_REQUEST: 'ORDERING/APP/CREATE_LOGIN_REQUEST',
  CREATE_LOGIN_SUCCESS: 'ORDERING/APP/CREATE_LOGIN_SUCCESS',
  CREATE_LOGIN_FAILURE: 'ORDERING/APP/CREATE_LOGIN_FAILURE',
  CLEAR_TOKENS: 'ORDERING/APP/CLEAR_TOKENS',

  UPDATE_USER: 'ORDERING/UPDATE_USER',

  // Profile
  LOAD_CONSUMER_PROFILE_PENDING: 'ordering/profile/loadProfileInfo/pending',
  LOAD_CONSUMER_PROFILE_FULFILLED: 'ordering/profile/loadProfileInfo/fulfilled',
  LOAD_CONSUMER_PROFILE_REJECTED: 'ordering/profile/loadProfileInfo/rejected',

  UPDATE_CONSUMER_PROFILE: 'ordering/profile/updateProfile',

  UPDATE_LOGIN_SOURCE: 'ordering/updateLoginSource',

  // get OTP
  RESET_GET_OTP_REQUEST: 'ORDERING/APP/RESET_GET_OTP_REQUEST',
  GET_OTP_REQUEST: 'ORDERING/APP/GET_OTP_REQUEST',
  GET_OTP_SUCCESS: 'ORDERING/APP/GET_OTP_SUCCESS',
  GET_OTP_FAILURE: 'ORDERING/APP/GET_OTP_FAILURE',

  // get phone WhatsApp support
  GET_WHATSAPPSUPPORT_REQUEST: 'ORDERING/APP/GET_WHATSAPPSUPPORT_REQUEST',
  GET_WHATSAPPSUPPORT_SUCCESS: 'ORDERING/APP/GET_WHATSAPPSUPPORT_SUCCESS',
  GET_WHATSAPPSUPPORT_FAILURE: 'ORDERING/APP/GET_WHATSAPPSUPPORT_FAILURE',

  // OTP
  RESET_CREATE_OTP_REQUEST: 'ORDERING/APP/RESET_CREATE_OTP_REQUEST',
  CREATE_OTP_REQUEST: 'ORDERING/APP/CREATE_OTP_REQUEST',
  CREATE_OTP_SUCCESS: 'ORDERING/APP/CREATE_OTP_SUCCESS',
  CREATE_OTP_FAILURE: 'ORDERING/APP/CREATE_OTP_FAILURE',

  // fetch shoppingCart
  FETCH_SHOPPINGCART_REQUEST: 'ORDERING/APP/FETCH_SHOPPINGCART_REQUEST',
  FETCH_SHOPPINGCART_SUCCESS: 'ORDERING/APP/FETCH_SHOPPINGCART_SUCCESS',
  FETCH_SHOPPINGCART_FAILURE: 'ORDERING/APP/FETCH_SHOPPINGCART_FAILURE',

  // mutable removeShoppingCartItem
  REMOVE_SHOPPINGCARTITEM_REQUEST: 'ORDERING/APP/REMOVE_SHOPPINGCARTITEM_REQUEST',
  REMOVE_SHOPPINGCARTITEM_SUCCESS: 'ORDERING/APP/REMOVE_SHOPPINGCARTITEM_SUCCESS',
  REMOVE_SHOPPINGCARTITEM_FAILURE: 'ORDERING/APP/REMOVE_SHOPPINGCARTITEM_FAILURE',

  // mutable addOrUpdateShoppingCartItem
  ADDORUPDATE_SHOPPINGCARTITEM_REQUEST: 'ORDERING/APP/ADDORUPDATE_SHOPPINGCARTITEM_REQUEST',
  ADDORUPDATE_SHOPPINGCARTITEM_SUCCESS: 'ORDERING/APP/ADDORUPDATE_SHOPPINGCARTITEM_SUCCESS',
  ADDORUPDATE_SHOPPINGCARTITEM_FAILURE: 'ORDERING/APP/ADDORUPDATE_SHOPPINGCARTITEM_FAILURE',

  // mutable shoppingCart applyCashback
  UPDATE_SHOPPINGCART_APPLYCASHBACK: 'ORDERING/APP/UPDATE_SHOPPINGCART_APPLYCASHBACK',

  // clear all
  CLEARALL_REQUEST: 'ORDERING/APP/CLEARALL_REQUEST',
  CLEARALL_SUCCESS: 'ORDERING/APP/CLEARALL_SUCCESS',
  CLEARALL_FAILURE: 'ORDERING/APP/CLEARALL_FAILURE',

  // clear all by products
  CLEARALL_BY_PRODUCTS_REQUEST: 'ORDERING/APP/CLEARALL_BY_PRODUCTS_REQUEST',
  CLEARALL_BY_PRODUCTS_SUCCESS: 'ORDERING/APP/CLEARALL_BY_PRODUCTS_REQUEST',
  CLEARALL_BY_PRODUCTS_FAILURE: 'ORDERING/APP/CLEARALL_BY_PRODUCTS_REQUEST',

  // delivery details
  DELIVERY_DETAILS_INIT: 'ORDERING/APP/DELIVERY_DETAILS_INIT',
  DELIVERY_DETAILS_UPDATED: 'ORDERING/APP/DELIVERY_DETAILS_UPDATED',

  // core stores
  FETCH_CORESTORES_REQUEST: 'ORDERING/APP/FETCH_CORESTORES_REQUEST',
  FETCH_CORESTORES_SUCCESS: 'ORDERING/APP/FETCH_CORESTORES_SUCCESS',
  FETCH_CORESTORES_FAILURE: 'ORDERING/APP/FETCH_CORESTORES_FAILURE',

  // store hash code
  FETCH_STORE_HASHCODE_REQUEST: 'ORDERING/APP/FETCH_STORE_HASHCODE_REQUEST',
  FETCH_STORE_HASHCODE_SUCCESS: 'ORDERING/APP/FETCH_STORE_HASHCODE_SUCCESS',
  FETCH_STORE_HASHCODE_FAILURE: 'ORDERING/APP/FETCH_STORE_HASHCODE_FAILURE',

  // fetch onlineCategory
  RESET_ONLINECATEGORY_STATUS: 'ORDERING/APP/RESET_ONLINECATEGORY_STATUS',
  FETCH_ONLINECATEGORY_REQUEST: 'ORDERING/APP/FETCH_ONLINECATEGORY_REQUEST',
  FETCH_ONLINECATEGORY_SUCCESS: 'ORDERING/APP/FETCH_ONLINECATEGORY_SUCCESS',
  FETCH_ONLINECATEGORY_FAILURE: 'ORDERING/APP/FETCH_ONLINECATEGORY_FAILURE',

  // fetch productDetail
  FETCH_PRODUCTDETAIL_REQUEST: 'ORDERING/APP/FETCH_PRODUCTDETAIL_REQUEST',
  FETCH_PRODUCTDETAIL_SUCCESS: 'ORDERING/APP/FETCH_PRODUCTDETAIL_SUCCESS',
  FETCH_PRODUCTDETAIL_FAILURE: 'ORDERING/APP/FETCH_PRODUCTDETAIL_FAILURE',

  // fetch delivery address detail
  FETCH_DELIVERYADDRESSDETAIL_REQUEST: 'ORDERING/APP/FETCH_DELIVERYADDRESSDETAIL_REQUEST',
  FETCH_DELIVERYADDRESSDETAIL_SUCCESS: 'ORDERING/APP/FETCH_DELIVERYADDRESSDETAIL_SUCCESS',
  FETCH_DELIVERYADDRESSDETAIL_FAILURE: 'ORDERING/APP/FETCH_DELIVERYADDRESSDETAIL_FAILURE',

  UPDATE_SHIPPING_TYPE: 'ORDERING/APP/UPDATE_SHIPPING_TYPE',
  UPDATE_STORE_ID: 'ORDERING/APP/UPDATE_STORE_ID',

  CHECK_URL_VALIDATION_REQUEST: 'ORDERING/APP/CHECK_URL_VALIDATION_REQUEST',
  CHECK_URL_VALIDATION_SUCCESS: 'ORDERING/APP/CHECK_URL_VALIDATION_SUCCESS',
  CHECK_URL_VALIDATION_FAILURE: 'ORDERING/APP/CHECK_URL_VALIDATION_FAILURE',

  // Customer Info
  LOAD_CONSUMER_INFO_PENDING: 'ordering/app/loadCustomerInfo/pending',
  LOAD_CONSUMER_INFO_FULFILLED: 'ordering/app/loadCustomerInfo/fulfilled',
  LOAD_CONSUMER_INFO_REJECTED: 'ordering/app/loadProfileInfo/rejected',
};

export const PROMOTION_TYPES = {
  // Apply Promotion code
  APPLY_PROMOTION_CODE_REQUEST: 'ORDERING/PROMOTION/APPLY_PROMOTION_CODE_REQUEST',
  APPLY_PROMOTION_CODE_SUCCESS: 'ORDERING/PROMOTION/APPLY_PROMOTION_CODE_SUCCESS',
  APPLY_PROMOTION_CODE_FAILURE: 'ORDERING/PROMOTION/APPLY_PROMOTION_CODE_FAILURE',

  // Fetch user voucher list
  FETCH_CONSUMER_VOUCHER_LIST_REQUEST: 'ORDERING/PROMOTION/FETCH_CONSUMER_VOUCHER_LIST_REQUEST',
  FETCH_CONSUMER_VOUCHER_LIST_SUCCESS: 'ORDERING/PROMOTION/FETCH_CONSUMER_VOUCHER_LIST_SUCCESS',
  FETCH_CONSUMER_VOUCHER_LIST_FAILURE: 'ORDERING/PROMOTION/FETCH_CONSUMER_VOUCHER_LIST_FAILURE',

  // Fetch promo/voucher info
  FETCH_PROMO_INFO_REQUEST: 'ORDERING/PROMOTION/FETCH_PROMO_INFO_REQUEST',
  FETCH_PROMO_INFO_SUCCESS: 'ORDERING/PROMOTION/FETCH_PROMO_INFO_SUCCESS',
  FETCH_PROMO_INFO_FAILURE: 'ORDERING/PROMOTION/FETCH_PROMO_INFO_FAILURE',

  // Apply Voucher code
  APPLY_VOUCHER_REQUEST: 'ORDERING/PROMOTION/APPLY_VOUCHER_REQUEST',
  APPLY_VOUCHER_SUCCESS: 'ORDERING/PROMOTION/APPLY_VOUCHER_SUCCESS',
  APPLY_VOUCHER_FAILURE: 'ORDERING/PROMOTION/APPLY_VOUCHER_FAILURE',

  // DISMISS Promotion code
  DISMISS_PROMOTION_CODE_REQUEST: 'ORDERING/PROMOTION/DISMISS_PROMOTION_CODE_REQUEST',
  DISMISS_PROMOTION_CODE_SUCCESS: 'ORDERING/PROMOTION/DISMISS_PROMOTION_CODE_SUCCESS',
  DISMISS_PROMOTION_CODE_FAILURE: 'ORDERING/PROMOTION/DISMISS_PROMOTION_CODE_FAILURE',

  UPDATE_PROMOTION_CODE: 'ORDERING/PROMOTION/UPDATE_PROMOTION_CODE',
  INITIAL_PROMOTION_CODE: 'ORDERING/PROMOTION/INITIAL_PROMOTION_CODE',
  UPDATE_SEARCH_MODE: 'ORDERING/PROMOTION/UPDATE_SEARCH_MODE',
  UPDATE_HAS_SEARCHED_PROMO: 'ORDERING/PROMOTION/UPDATE_HAS_SEARCHED_PROMO',
  EMPTY_FOUND_PROMO: 'ORDERING/PROMOTION/EMPTY_FOUND_PROMO',
  SELECT_PROMO: 'ORDERING/PROMOTION/SELECT_PROMO',
  DESELECT_PROMO: 'ORDERING/PROMOTION/DESELECT_PROMO',
};

export const THANK_YOU_TYPES = {
  // fetch order
  FETCH_ORDER_REQUEST: 'ORDERING/THANK_YOU/FETCH_ORDER_REQUEST',
  FETCH_ORDER_SUCCESS: 'ORDERING/THANK_YOU/FETCH_ORDER_SUCCESS',
  FETCH_ORDER_FAILURE: 'ORDERING/THANK_YOU/FETCH_ORDER_FAILURE',

  FETCH_ORDER_STATUS_REQUEST: 'ORDERING/THANK_YOU/FETCH_ORDER_STATUS_REQUEST',
  FETCH_ORDER_STATUS_SUCCESS: 'ORDERING/THANK_YOU/FETCH_ORDER_STATUS_SUCCESS',
  FETCH_ORDER_STATUS_FAILURE: 'ORDERING/THANK_YOU/FETCH_ORDER_STATUS_FAILURE',

  // fetch CashbackInfo
  FETCH_CASHBACKINFO_REQUEST: 'ORDERING/THANK_YOU/FETCH_CASHBACKINFO_REQUEST',
  FETCH_CASHBACKINFO_SUCCESS: 'ORDERING/THANK_YOU/FETCH_CASHBACKINFO_SUCCESS',
  FETCH_CASHBACKINFO_FAILURE: 'ORDERING/THANK_YOU/FETCH_CASHBACKINFO_FAILURE',

  // create CashbackInfo
  CREATE_CASHBACKINFO_REQUEST: 'ORDERING/THANK_YOU/CREATE_CASHBACKINFO_REQUEST',
  CREATE_CASHBACKINFO_SUCCESS: 'ORDERING/THANK_YOU/CREATE_CASHBACKINFO_SUCCESS',
  CREATE_CASHBACKINFO_FAILURE: 'ORDERING/THANK_YOU/CREATE_CASHBACKINFO_FAILURE',

  // fetch store Hash code
  FETCH_STORE_HASHCODE_REQUEST: 'ORDERING/THANK_YOU/FETCH_STORE_HASHCODE_REQUEST',
  FETCH_STORE_HASHCODE_SUCCESS: 'ORDERING/THANK_YOU/FETCH_STORE_HASHCODE_SUCCESS',
  FETCH_STORE_HASHCODE_FAILURE: 'ORDERING/THANK_YOU/FETCH_STORE_HASHCODE_FAILURE',

  FETCH_STORE_HASHCODE_WITH_TABLEID_REQUEST: 'ORDERING/THANK_YOU/FETCH_STORE_HASHCODE_WITH_TABLEID_REQUEST',
  FETCH_STORE_HASHCODE_WITH_TABLEID_SUCCESS: 'ORDERING/THANK_YOU/FETCH_STORE_HASHCODE_WITH_TABLEID_SUCCESS',
  FETCH_STORE_HASHCODE_WITH_TABLEID_FAILURE: 'ORDERING/THANK_YOU/FETCH_STORE_HASHCODE_WITH_TABLEID_FAILURE',
};

export const REPORT_DRIVER_TYPES = {
  // update input notes
  UPDATE_INPUT_NOTES: 'REPORT_DRIVER/UPDATE_INPUT_NOTES',

  // set upload photo file
  SET_UPLOAD_PHOTO_FILE: 'REPORT_DRIVER/SET_UPLOAD_PHOTO_FILE',

  // remove upload photo file
  REMOVE_UPLOAD_PHOTO_FILE: 'REPORT_DRIVER/REMOVE_UPLOAD_PHOTO_FILE',

  // set upload photo uploaded location
  SET_UPLOAD_PHOTO_LOCATION: 'REPORT_DRIVER/SET_UPLOAD_PHOTO_LOCATION',

  // select reason code
  SELECT_REASON_CODE: 'REPORT_DRIVER/SELECT_REASON_CODE',

  // submit report
  SUBMIT_REPORT_REQUEST: 'REPORT_DRIVER/SUBMIT_REPORT_REQUEST',
  SUBMIT_REPORT_SUCCESS: 'REPORT_DRIVER/SUBMIT_REPORT_SUCCESS',
  SUBMIT_REPORT_FAILURE: 'REPORT_DRIVER/SUBMIT_REPORT_FAILURE',

  // fetch report
  FETCH_REPORT_REQUEST: 'REPORT_DRIVER/FETCH_REPORT_REQUEST',
  FETCH_REPORT_SUCCESS: 'REPORT_DRIVER/FETCH_REPORT_SUCCESS',
  FETCH_REPORT_FAILURE: 'REPORT_DRIVER/FETCH_REPORT_FAILURE',

  // update submit status
  UPDATE_SUBMIT_STATUS: 'REPORT_DRIVER/UPDATE_SUBMIT_STATUS',
};

export const LOCATION_AND_DATE = {
  INITIAL: 'LOCATION_AND_DATE/INITIAL',

  RESET: 'LOCATION_AND_DATE/RESET',

  DELIVERY_TYPE_CHANGED: 'LOCATION_AND_DATE/DELIVERY_TYPE_CHANGED',

  STORE_CHANGED: 'LOCATION_AND_DATE/STORE_CHANGED',

  DELIVERY_ADDRESS_CHANGED: 'LOCATION_AND_DATE/DELIVERY_ADDRESS_CHANGED',

  SELECTED_DAY_CHANGE: 'LOCATION_AND_DATE/SELECTED_DAY_CHANGE',

  SELECTED_FROM_TIME_CHANGED: 'LOCATION_AND_DATE/SELECTED_FROM_TIME_CHANGED',

  CURRENT_DATE_UPDATED: 'LOCATION_AND_DATE/CURRENT_DATE_UPDATED',

  TIME_SLOT_SOLD_DATA_LOADED: 'LOCATION_AND_DATE/TIME_SLOT_SOLD_DATA_LOADED',

  SHOW_LOADING: 'LOCATION_AND_DATE/SHOW_LOADING',
};
