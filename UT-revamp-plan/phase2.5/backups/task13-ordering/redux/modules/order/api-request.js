import { get, post } from '../../../../utils/api/api-fetch';

export const getOrder = receiptNumber => post('/api/gql/Order', { orderId: receiptNumber });

export const getOrderStatus = receiptNumber => post(`/api/transactions/${receiptNumber}/status`);

export const getPayLaterOrder = receiptNumber => get(`/api/v3/transactions/${receiptNumber}/calculation`);

export const getPayLaterOrderStatus = receiptNumber => get(`/api/v3/transactions/${receiptNumber}/status`);
