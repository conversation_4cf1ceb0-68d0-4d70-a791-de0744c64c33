import { createSlice } from '@reduxjs/toolkit';
import {
  fetchOrder,
  fetchPayLaterOrder,
  fetchPayLaterOrderStatus,
  syncPayLaterOrderAndStatus,
  updatePayLaterOrderRedirectUrl,
} from './thunks';
import { API_REQUEST_STATUS } from '../../../../common/utils/constants';

const PromotionItemModel = {
  promotionId: null,
  tax: 0,
  taxCode: null,
  code: null,
  promotionCode: null,
  promotionName: null,
  status: null,
  discount: 0,
  discountType: null,
};

const appliedVoucherModel = {
  voucherId: null,
  voucherCode: null,
  value: 0,
  cost: 0,
  purchaseChannel: null,
};

const loyaltyDiscountsModel = {
  displayDiscount: 0,
  spentValue: 0,
};

const initialState = {
  loadOrderRequest: {
    data: null,
    status: null,
    error: null,
  },
  loadPayLaterOrderRequest: {
    data: {
      orderStatus: null,
      receiptNumber: null,
      tableId: null,
      isStorePayByCashOnly: false,
      tax: 0,
      cashback: 0,
      displayPromotions: [],
      loyaltyDiscounts: [],
      appliedVoucher: null,
      total: 0,
      subtotal: 0,
      modifiedTime: null,
      serviceCharge: 0,
      serviceChargeInfo: {},
      shippingFee: 0,
      subOrders: [],
      items: [],
      applyCashback: false,
      redirectUrl: null,
      // Discount added from POS. It's a product items discount, meaning it's calculated only with respect to subtotal. --Jiwang said
      productsManualDiscount: 0,
    },
    status: null,
    error: null,
  },
  loadPayLaterOrderStatusRequest: {
    data: null,
    status: null,
    error: null,
  },
  payLaterOrderStatusInfo: {
    data: {
      tableId: null,
      storeHash: null,
    },
    status: null,
    error: null,
  },
};

const { reducer, actions } = createSlice({
  name: 'ordering/order',
  initialState,
  reducers: {
    updateCashbackApplyStatus(state, action) {
      state.loadPayLaterOrderRequest.data.applyCashback = action.payload;
    },
  },
  extraReducers: {
    [fetchOrder.pending.type]: state => {
      state.loadOrderRequest.status = API_REQUEST_STATUS.PENDING;
      state.loadOrderRequest.error = null;
    },
    [fetchOrder.fulfilled.type]: (state, { payload }) => {
      state.loadOrderRequest.data = payload;
      state.loadOrderRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loadOrderRequest.error = null;
    },
    [fetchOrder.rejected.type]: (state, { error }) => {
      state.loadOrderRequest.status = API_REQUEST_STATUS.REJECTED;
      state.loadOrderRequest.error = error;
    },
    [fetchPayLaterOrder.pending.type]: state => {
      state.loadPayLaterOrderRequest.status = API_REQUEST_STATUS.PENDING;
      state.loadPayLaterOrderRequest.error = null;
    },
    [fetchPayLaterOrder.fulfilled.type]: (state, { payload }) => {
      const { displayPromotions = [], loyaltyDiscounts = [], appliedVoucher, status: orderStatus, ...others } = {
        ...state.loadPayLaterOrderRequest.data,
        ...payload,
      };

      state.loadPayLaterOrderRequest.data = {
        ...others,
        orderStatus,
        status: orderStatus,
        loyaltyDiscounts: (loyaltyDiscounts || []).map(item => ({ ...loyaltyDiscountsModel, ...item })),
        displayPromotions: (displayPromotions || []).map(promotion => ({ ...PromotionItemModel, ...promotion })),
        appliedVoucher: { ...appliedVoucherModel, ...appliedVoucher },
      };
      state.loadPayLaterOrderRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loadPayLaterOrderRequest.error = null;
    },
    [fetchPayLaterOrder.rejected.type]: (state, { error }) => {
      state.loadPayLaterOrderRequest.error = error;
      state.loadPayLaterOrderRequest.status = API_REQUEST_STATUS.REJECTED;
    },
    [fetchPayLaterOrderStatus.pending.type]: state => {
      state.loadPayLaterOrderStatusRequest.status = API_REQUEST_STATUS.PENDING;
      state.loadPayLaterOrderStatusRequest.error = null;
    },
    [fetchPayLaterOrderStatus.fulfilled.type]: (state, { payload }) => {
      state.loadPayLaterOrderStatusRequest.data = payload;
      state.loadPayLaterOrderStatusRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loadPayLaterOrderStatusRequest.error = null;
    },
    [fetchPayLaterOrderStatus.rejected.type]: (state, { error }) => {
      state.loadPayLaterOrderStatusRequest.error = error;
      state.loadPayLaterOrderStatusRequest.status = API_REQUEST_STATUS.REJECTED;
    },
    [syncPayLaterOrderAndStatus.pending.type]: state => {
      state.payLaterOrderStatusInfo.status = API_REQUEST_STATUS.PENDING;
      state.payLaterOrderStatusInfo.error = null;
    },
    [syncPayLaterOrderAndStatus.fulfilled.type]: (state, { payload }) => {
      const { status, tableId, hash } = payload;

      // TODO: Migrate and update this data to payLaterOrderStatusInfo
      state.loadPayLaterOrderRequest.data.orderStatus = status;
      state.payLaterOrderStatusInfo.data.tableId = tableId;

      // WB-4939: BE will only generate new h when the table id is changed for performance sake.
      // Therefore, we should only update the hash when needed.
      if (hash) {
        state.payLaterOrderStatusInfo.data.storeHash = hash;
      }

      // TODO: Migrate and update this data to payLaterOrderStatusInfo
      if (payload.redirectUrl) {
        state.loadPayLaterOrderRequest.data.redirectUrl = payload.redirectUrl;
      }
      state.payLaterOrderStatusInfo.status = API_REQUEST_STATUS.FULFILLED;
    },
    [syncPayLaterOrderAndStatus.rejected.type]: (state, { error }) => {
      state.payLaterOrderStatusInfo.error = error;
      state.payLaterOrderStatusInfo.status = API_REQUEST_STATUS.REJECTED;
    },
    [updatePayLaterOrderRedirectUrl.fulfilled.type]: (state, { payload }) => {
      state.loadPayLaterOrderRequest.data.redirectUrl = payload;
    },
  },
});

export { actions };

export default reducer;
