import { PATH_NAME_MAPPING } from '../../common/utils/constants';
import { SSE_EVENT_NAMES, SSE_TOPICS } from '../../common/utils/sse/constants';

export const PAGE_ROUTES = {
  LOGIN: PATH_NAME_MAPPING.ORDERING_LOGIN,
  MENU: PATH_NAME_MAPPING.ORDERING_HOME,
  CUSTOMER: PATH_NAME_MAPPING.ORDERING_CUSTOMER_INFO,
  LOCATION: PATH_NAME_MAPPING.ORDERING_LOCATION,
  CART: PATH_NAME_MAPPING.ORDERING_CART,
  REWARD_LIST: `${PATH_NAME_MAPPING.REWARD}${PATH_NAME_MAPPING.LIST}`,
  REWARD_DETAIL: `${PATH_NAME_MAPPING.REWARD}${PATH_NAME_MAPPING.DETAIL}`,
  TABLE_SUMMARY: PATH_NAME_MAPPING.ORDERING_TABLE_SUMMARY,
  THANK_YOU: PATH_NAME_MAPPING.THANK_YOU,
  THANK_YOU_ORDER_DETAIL: PATH_NAME_MAPPING.ORDER_DETAILS,
};

export const SSE_SUBSCRIPTION_GROUPS = {
  PAY_LATER_ORDERING: 'PAY_LATER_ORDERING',
  ORDERED: 'ORDERED',
};

export const SSE_EVENT_LISTENER_PATHNAME_KEYS = {
  SHOPPING_CART_UPDATED: 'SHOPPING_CART_UPDATED',
  TABLE_SUMMARY_UPDATED: 'TABLE_SUMMARY_UPDATED',
  ORDER_UPDATED: 'ORDER_UPDATED',
};

export const SSE_SUBSCRIPTION_GROUP_PATHNAMES = {
  [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: [
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.MENU}`,
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.CART}`,
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.TABLE_SUMMARY}`,
  ],
  [SSE_SUBSCRIPTION_GROUPS.ORDERED]: [
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU}`,
    `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU_ORDER_DETAIL}`,
  ],
};

export const SSE_SUBSCRIPTION_GROUP_TOPICS = {
  [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: [SSE_TOPICS.PAY_LATER_SHOPPING_CART_UPDATED, SSE_TOPICS.ORDER_UPDATED],
  [SSE_SUBSCRIPTION_GROUPS.ORDERED]: SSE_TOPICS.ORDER_UPDATED,
};

export const SSE_EVENT_LISTENERS_LIMITATIONS = {
  [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: {
    [SSE_EVENT_LISTENER_PATHNAME_KEYS.SHOPPING_CART_UPDATED]: {
      eventName: SSE_EVENT_NAMES.SHOPPING_CART_UPDATED,
      pathnames: [
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.MENU}`,
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.CART}`,
      ],
    },
    [SSE_EVENT_LISTENER_PATHNAME_KEYS.TABLE_SUMMARY_UPDATED]: {
      eventName: SSE_EVENT_NAMES.ORDER_UPDATED,
      pathnames: [`${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.TABLE_SUMMARY}`],
    },
  },
  [SSE_SUBSCRIPTION_GROUPS.ORDERED]: {
    [SSE_EVENT_LISTENER_PATHNAME_KEYS.ORDER_UPDATED]: {
      eventName: SSE_EVENT_NAMES.ORDER_UPDATED,
      pathnames: [
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU}`,
        `${PATH_NAME_MAPPING.ORDERING_BASE}${PAGE_ROUTES.THANK_YOU_ORDER_DETAIL}`,
      ],
    },
  },
};
