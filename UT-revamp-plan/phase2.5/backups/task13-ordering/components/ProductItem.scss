@use "../../Variables" as *;

.product-item {
  position: relative;
  background-color: $white;
  list-style: none;

  &__image-container {
    position: relative;
    width: 18vw;
    height: 18vw;
    min-width: 80px;
    height: 80px;
    overflow: hidden;
  }

  &__image {
    width: 100%;
    height: 100%;
  }

  &__title {
    max-height: 2.8em;
    line-clamp: 2;
    -webkit-line-clamp: 2;
  }

  &__description {
    color: $theme-color;
    line-clamp: 3;
    -webkit-line-clamp: 3;
  }

  &__takeaway-variant {
    color: $status-primary-basic;
    font-weight: 700;
  }
}

@media (min-width: 770px) {
  .product-item {
    &__image-container {
      width: 72px;
      height: 72px;
    }
  }
}
