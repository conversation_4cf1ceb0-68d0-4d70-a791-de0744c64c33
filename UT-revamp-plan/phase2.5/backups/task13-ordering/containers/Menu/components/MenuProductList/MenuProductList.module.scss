@use "../../../../../common/styles/variables" as *;

.menuProductCategorySearchContainer {
  @apply tw-flex tw-items-center tw-sticky tw-top-0 tw-p-16 sm:tw-p-16px tw-bg-white tw-z-10;
}

.bestSellerCategoryProductList {
  @apply tw-relative tw-grid tw-grid-cols-2 sm:tw-grid-cols-3 md:tw-grid-cols-2 tw-gap-4 sm:tw-gap-4px tw-px-8 sm:tw-px-8px tw-pt-12 sm:tw-pt-12px tw-pb-2 sm:tw-pb-2px tw-mx-2 sm:tw-mx-2px;

  &::before {
    @apply tw-absolute tw-top-0 tw-left-8 sm:tw-left-8px tw-right-8 sm:tw-right-8px tw-block tw-bg-gray-200 tw-mx-6 sm:tw-mx-6px;

    content: "";
    height: 1px;
  }
}

.menuProductItem {
  @apply tw-cursor-pointer;
}

.categoryProductList .menuProductItem {
  &::before {
    @apply tw-block tw-bg-gray-200 tw-mx-6 sm:tw-mx-6px;

    content: "";
    height: 1px;
  }
}

.menuProductItemTitle,
.menuProductItemDescription {
  @apply tw-font-bold tw-leading-relaxed tw-mr-12 sm:tw-mr-12px;
  @include text-line-clamp(2, 3em);
}

.menuProductItemNoImage {
  @apply tw-flex tw-items-start;

  .menuProductItemCartQuantity {
    @apply tw-static tw-flex-shrink-0;
  }
}

.menuProductItemImageContainer {
  @apply tw-m-2 sm:tw-m-2px;
}

.categoryProductList .menuProductItemImageContainer {
  max-width: 160px;
}

.menuProductItemPrice {
  @apply tw-px-2 sm:tw-px-2px tw-leading-relaxed;
}

.menuProductItemCartQuantity {
  @apply tw-absolute tw-top-0 tw-right-0;
}

.menuProductItemContentDisabled {
  .menuProductItemImageContainer,
  .menuProductItemTitle,
  .menuProductItemDescription,
  .menuProductItemPrice,
  .menuProductItemCartQuantity {
    @apply tw-opacity-40;
  }
}

.menuProductItemBestSellerTag {
  @apply tw-inline-block;

  width: 31%;
  max-width: 77px;
  min-width: 63px;
  // original image size is 77x18
  aspect-ratio: 77 / 18;
}

.menuProductNoResult {
  @apply tw-relative tw-text-center tw-py-8 sm:tw-py-8px;

  top: 15vh;
  // No result height is not enough, so we need to set height to 100%
  height: calc(85vh - 70px);
}

.menuProductNoResultImage {
  @apply tw-w-3/10 tw-m-8 sm:tw-m-8px;

  max-width: 120px;
}

.menuProductNoResultImage {
  @apply tw-w-3/10 tw-m-8 sm:tw-m-8px;

  max-width: 120px;
}
