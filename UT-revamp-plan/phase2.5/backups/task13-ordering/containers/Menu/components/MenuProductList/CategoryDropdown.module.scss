@use "../../../../../common/styles/variables" as *;

.menuProductCategoryButton:global(.type-text-ghost) {
  @apply tw-px-16 sm:tw-px-16px tw-py-4 sm:tw-py-4px tw-border tw-border-solid tw-border-gray-200  tw-bg-gray-200 tw-rounded-2xl;

  height: 40px;
  border-radius: 24px;
}

.menuProductCategoryContentButton {
  @apply tw-flex tw-flex-grow tw-items-center tw-justify-between tw-max-w-full;
}

.menuProductCategoryIcon {
  @apply tw-flex-shrink-0 tw-text-2xl;

  width: 18px;
  height: auto;
  aspect-ratio: 9 / 8;
}

.menuProductHighlightedCategory {
  @apply tw-flex-1 tw-text-left tw-px-6 sm:tw-px-6px tw-text-gray-800 tw-font-bold;
  @include text-line-clamp(1);
}

.menuProductCategoryDrawer {
  @apply tw-p-0;
}

.menuProductCategoryDrawerHeaderCloseButton:global(.type-text-ghost) {
  @apply tw-flex-shrink-0 tw--mx-12 sm:tw--mx-12px;
}

.menuProductCategoryDrawerHeaderCloseButtonContent {
  font-size: 0;
}

.menuProductCategoryItem {
  @apply tw-items-center tw-py-16;

  &:global(.active) > &Text {
    @apply tw-text-orange tw-font-bold;
  }
}

.menuProductCategoryItemText {
  @apply tw-flex-1;
  @include text-line-clamp(1);
}

@media (min-width: 420px) {
  .menuProductCategoryDrawerHeader::after {
    @apply tw-p-16px;
  }

  .menuProductCategoryItem {
    @apply tw-py-16px;
  }

  .menuProductItem {
    &::before {
      @apply tw-mx-6px;
    }
  }
}
