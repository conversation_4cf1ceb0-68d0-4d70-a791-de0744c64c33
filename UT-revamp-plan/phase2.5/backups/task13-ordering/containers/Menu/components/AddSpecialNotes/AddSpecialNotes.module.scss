.AddSpecialNotes {
  @apply tw-p-0;
}

.AddSpecialNotesEditButton:global(.type-text-info) {
  @apply tw--m-12 sm:tw--m-12px;
}

.AddSpecialNotesInputContainer {
  @apply tw-border-solid tw-border tw-border-gray-500 tw-rounded tw-pt-12 sm:tw-pt-12px tw-px-12 sm:tw-px-12px tw-pb-8 sm:tw-pb-8px tw-m-16 sm:tw-m-16px tw-overflow-auto;
}

.AddSpecialNotesInputContent {
  @apply tw-w-full tw-border-0  tw-resize-none;

  height: 80px;
}

.AddSpecialNotesLimitLength {
  @apply tw-text-right tw-pt-8 sm:tw-pt-8px tw-opacity-50 tw-text-sm;
}

.AddSpecialNotesSaveContainer {
  @apply tw-mx-16 sm:tw-mx-16px tw-my-8 sm:tw-my-8px;
}

.AddSpecialNotesSaveButton {
  @apply tw-w-full tw-bg-gray-500 tw-uppercase;
}
