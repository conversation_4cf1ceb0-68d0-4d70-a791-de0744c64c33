@use "../../../../../common/styles/variables" as *;

.header {
  @apply tw-relative tw-text-center tw-p-16px tw-border-gray-300;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.miniCartDrawer {
  @apply tw-p-0;
}

.removeAllButton:global(.type-text-danger) {
  @apply tw-p-0 tw-border-none tw-text-sm tw-capitalize;
}

.removeItemButton:global(.type-text-danger) {
  @apply tw-flex-shrink-0 tw-px-2 sm:tw-px-2px tw-py-0 tw-mt-8 sm:tw-mt-8px tw-border-none tw-text-sm tw-capitalize;
}

.cartItem {
  @apply tw-py-16 sm:tw-py-16px tw-border-gray-200;

  border-top-width: 1px;
  border-top-style: solid;
  &:first-child {
    border-top-width: 0;
  }
}

.cartItemInfoContent {
  @apply tw-flex tw-items-start;
}

.cartItemTitle {
  @apply tw-px-2 sm:tw-px-2px tw-font-bold tw-leading-relaxed;
  @include text-line-clamp(2, 3em);
}

.cartItemDescription {
  @apply tw-px-2 sm:tw-px-2px tw-my-2 sm:tw-my-2px tw-leading-loose tw-text-orange tw-text-sm;
}

.cartItemTakeVariant {
  @apply tw-leading-loose tw-text-orange-dark tw-text-sm tw-font-bold;
}

.cartItemComments {
  @apply tw-px-2 sm:tw-px-2px tw-text-sm tw-text-gray-600;

  word-break: break-word;
}

.cartItemImageContainer {
  @apply tw-m-2 sm:tw-m-2px tw-ml-16 sm:tw-ml-16px;

  width: 50px;
}

.cartItemOutStock {
  .cartItemTitle,
  .cartItemDescription,
  .cartItemPrice,
  .cartItemImageContainer {
    @apply tw-opacity-40;
  }
}
