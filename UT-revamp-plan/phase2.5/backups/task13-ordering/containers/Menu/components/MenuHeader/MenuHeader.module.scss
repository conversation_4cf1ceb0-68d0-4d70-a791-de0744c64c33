.MenuHeader {
  &__RightPlaceholder {
    @apply tw-pr-24 sm:tw-pr-24px;
  }
}

.MenuHeaderLogoContainer {
  @apply tw-flex-shrink-0 tw-p-16 sm:tw-p-16px tw-w-3/10;

  min-width: 116px;
  max-width: 130px;
}

.MenuHeaderLogo {
  width: 100%;
  // original image size is 83x11
  aspect-ratio: 83 / 11;
}

.MenuHeaderBackArrow {
  width: 20%;
}

.MenuHeaderButton {
  &__default:global(.type-text-ghost) {
    @apply tw-px-16 sm:tw-px-16px tw-py-12 sm:tw-py-12px tw-border-0 tw-text-black hover:tw-text-black active:tw-text-gray focus:tw-text-black focus:hover:tw-text-black  focus:active:tw-text-gray;
  }

  &__danger:global(.type-text-danger) {
    @apply tw-p-16 sm:tw-p-16px tw-border-0;
  }
}

.MenuHeaderLogoOffline {
  @apply tw-flex tw-flex-shrink-0 tw-items-stretch tw-p-12 sm:tw-p-12px
    tw-border-none tw-outline-none tw-bg-transparent tw-text-gray tw-cursor-pointer;
}
