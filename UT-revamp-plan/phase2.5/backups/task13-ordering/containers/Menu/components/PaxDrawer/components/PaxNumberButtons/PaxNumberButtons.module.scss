.PaxDrawerButtonGroup {
  @apply tw-flex tw-flex-wrap tw-justify-center tw-p-8 sm:tw-p-8px;
}

.PaxDrawerButtonContainer {
  @apply tw-relative tw-flex-shrink-0;

  width: 20%;

  &::before {
    @apply tw-block;

    content: "";
    padding: 50% 0;
  }
}

.PaxDrawerButton {
  @apply tw-absolute tw-inset-8 sm:tw-inset-8px;

  height: auto;

  &:global(.type-primary-default),
  &:global(.type-secondary-default) {
    height: auto;
  }

  &:global(.type-secondary-default) {
    @apply tw-text-gray-800 tw-border-gray-800;
  }
}
