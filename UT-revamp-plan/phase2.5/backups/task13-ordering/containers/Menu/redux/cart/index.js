import { createSlice } from '@reduxjs/toolkit';
import { API_REQUEST_STATUS } from '../../../../../common/utils/constants';
import { fetchCartPax, saveCartPax, hideMiniCartDrawer, showMiniCartDrawer } from './thunks';

const initialState = {
  loadCartPaxRequest: {
    data: null,
    status: null,
    error: null,
  },
  saveCartPaxRequest: {
    data: null,
    status: null,
    error: null,
  },
  miniCartDrawerVisible: false,
};

export const { reducer, actions } = createSlice({
  name: 'ordering/menu/cart',
  initialState,
  extraReducers: {
    [fetchCartPax.pending.type]: state => {
      state.loadCartPaxRequest.status = API_REQUEST_STATUS.PENDING;
      state.loadCartPaxRequest.error = null;
    },
    [fetchCartPax.fulfilled.type]: (state, { payload }) => {
      state.loadCartPaxRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.loadCartPaxRequest.data = payload;
      state.loadCartPaxRequest.error = null;
    },
    [fetchCartPax.rejected.type]: (state, { error }) => {
      state.loadCartPaxRequest.status = API_REQUEST_STATUS.REJECTED;
      state.loadCartPaxRequest.error = error;
    },
    [saveCartPax.pending.type]: state => {
      state.saveCartPaxRequest.status = API_REQUEST_STATUS.PENDING;
      state.saveCartPaxRequest.error = null;
    },
    [saveCartPax.fulfilled.type]: (state, { payload }) => {
      state.saveCartPaxRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.saveCartPaxRequest.data = payload;
      state.saveCartPaxRequest.error = null;
    },
    [saveCartPax.rejected.type]: (state, { error }) => {
      state.saveCartPaxRequest.status = API_REQUEST_STATUS.REJECTED;
      state.saveCartPaxRequest.error = error;
    },
    [showMiniCartDrawer.fulfilled.type]: state => {
      state.miniCartDrawerVisible = true;
    },
    [hideMiniCartDrawer.fulfilled.type]: state => {
      state.miniCartDrawerVisible = false;
    },
  },
});

export default reducer;
