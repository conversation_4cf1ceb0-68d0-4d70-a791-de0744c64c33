import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles } from 'react-use';
import usePrefetch from '../../../common/utils/hooks/usePrefetch';
import Frame from '../../../common/components/Frame';
import MenuShippingInfoBar from './components/MenuShippingInfoBar';
import MenuHeader from './components/MenuHeader';
import MenuStoreInfo from './components/MenuStoreInfo';
import PromotionBar from './components/PromotionBar';
import MenuProductList from './components/MenuProductList';
import ProductDetailDrawer from './components/ProductDetailDrawer';
import MenuFooter from './components/MenuFooter';
import MiniCart from './components/MiniCart';
import AlcoholModal from './components/AlcoholModal';
import MenuOfflineModal from './components/MenuOfflineModal';
import MenuSkeleton from './components/MenuSkeleton';
import DisabledResult from './components/DisabledResult';
import PaxDrawer from './components/PaxDrawer';
import { getEnablePayLater, getIsPayLaterSSEEnabled } from '../../redux/modules/app';
import { loadCart } from '../../redux/modules/cart/thunks';
import {
  getIsCoreBusinessAPIPending,
  getIsSearchingBannerVisible,
  getShouldShowOfflineMenu,
  getDisabledResultInfo,
  getIsSelectPaxFlowEnabled,
} from './redux/common/selectors';
import { mounted, willUnmount } from './redux/common/thunks';

const Menu = () => {
  const dispatch = useDispatch();
  // for whether display searching banner, if not header, store info and promo banner display
  const isSearchingBannerVisible = useSelector(getIsSearchingBannerVisible);
  const shouldShowOfflineMenu = useSelector(getShouldShowOfflineMenu);
  const shouldShowMenuSkeleton = useSelector(getIsCoreBusinessAPIPending);
  const disabledResultInfo = useSelector(getDisabledResultInfo);
  const isSelectPaxFlowEnabled = useSelector(getIsSelectPaxFlowEnabled);
  const isPayLaterSSEEnabled = useSelector(getIsPayLaterSSEEnabled);
  const isEnablePayLater = useSelector(getEnablePayLater);
  const handleTabVisibleLoadCart = useCallback(() => {
    if (!document.hidden) {
      dispatch(loadCart());
    }
  }, [dispatch]);

  useLifecycles(
    () => {
      dispatch(mounted());
    },
    async () => {
      await dispatch(willUnmount());
    }
  );

  useEffect(() => {
    if (isEnablePayLater && isPayLaterSSEEnabled) {
      // Load cart when tab is visible
      document.addEventListener('visibilitychange', handleTabVisibleLoadCart);
    }

    return () => {
      // remove event listener when component unmount
      document.removeEventListener('visibilitychange', handleTabVisibleLoadCart);
    };
  }, [isEnablePayLater, isPayLaterSSEEnabled, handleTabVisibleLoadCart]);

  usePrefetch(['ORD_SC', 'ORD_TS'], ['OrderingCart', 'OrderingPromotion', 'OrderingTableSummary']);

  return (
    <Frame>
      {shouldShowMenuSkeleton ? (
        <MenuSkeleton />
      ) : shouldShowOfflineMenu ? (
        <>
          {' '}
          <MenuHeader />
          <MenuOfflineModal />
        </>
      ) : disabledResultInfo ? (
        <DisabledResult />
      ) : (
        <>
          <MenuHeader webHeaderVisibility={!isSearchingBannerVisible} />
          <MenuShippingInfoBar />
          <section className="tw-py-16 sm:tw-py-16px">
            {isSearchingBannerVisible ? null : (
              <>
                <MenuStoreInfo />
                <PromotionBar />
              </>
            )}
            <MenuProductList />
          </section>
          <MenuFooter />
          <ProductDetailDrawer />
          <MiniCart />
          <AlcoholModal />
          {isSelectPaxFlowEnabled && <PaxDrawer />}
        </>
      )}
    </Frame>
  );
};

Menu.displayName = 'Menu';

export default Menu;
