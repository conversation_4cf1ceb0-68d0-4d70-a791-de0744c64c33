import React from 'react';
import Skeleton, { SkeletonTheme } from '../../../../../../../common/components/ReactLoadingSkeleton';
import styles from './SkeletonLoader.module.scss';

const SkeletonLoader = () => (
  <SkeletonTheme duration={2}>
    <Skeleton
      count={1}
      containerClassName={styles.SkeletonLoaderTicketContainer}
      className={styles.SkeletonLoaderTicket}
    />

    <section className={styles.SkeletonLoaderUniquePromoLimitationSection}>
      <Skeleton
        containerClassName={styles.SkeletonLoaderUniquePromoLimitationTitleContainer}
        className={styles.SkeletonLoaderUniquePromoLimitationTitle}
        count={1}
      />
      <Skeleton
        containerClassName={styles.SkeletonLoaderUniquePromoLimitationDescriptionContainer}
        className={styles.SkeletonLoaderUniquePromoLimitationDescription}
        count={1}
      />
    </section>

    <section className={styles.SkeletonLoaderUniquePromoLimitationSection}>
      <Skeleton
        containerClassName={styles.SkeletonLoaderUniquePromoLimitationTitleContainer}
        className={styles.SkeletonLoaderUniquePromoLimitationTitle}
        count={1}
      />
      <Skeleton
        containerClassName={styles.SkeletonLoaderUniquePromoLimitationDescriptionContainer}
        className={styles.SkeletonLoaderUniquePromoLimitationDescription}
        count={1}
      />
    </section>

    <section className={styles.SkeletonLoaderHowToUseSection}>
      <Skeleton
        containerClassName={styles.SkeletonLoaderHowToUseTitleContainer}
        className={styles.SkeletonLoaderHowToUseTitle}
        count={1}
      />
      <Skeleton
        containerClassName={styles.SkeletonLoaderHowToUseSubtitleContainer}
        className={styles.SkeletonLoaderHowToUseSubtitle}
        count={1}
      />
      <Skeleton
        containerClassName={styles.SkeletonLoaderHowToUseDescriptionContainer}
        className={styles.SkeletonLoaderHowToUseDescription}
        count={1}
      />
      <Skeleton
        containerClassName={styles.SkeletonLoaderHowToUseItemsContainer}
        className={styles.SkeletonLoaderHowToUseItems}
        count={3}
      />
    </section>
  </SkeletonTheme>
);

SkeletonLoader.displayName = 'SkeletonLoader';

export default SkeletonLoader;
