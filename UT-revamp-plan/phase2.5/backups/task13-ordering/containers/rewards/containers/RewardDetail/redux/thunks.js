import { createAsyncThunk } from '@reduxjs/toolkit';
import { push, replace, goBack as historyGoBack } from 'connected-react-router';
import { PATH_NAME_MAPPING } from '../../../../../../common/utils/constants';
import { REWARD_DETAIL_QUERY_PARAMS } from '../../../utils/constants';
import { getFilteredQueryString } from '../../../../../../common/utils';
import CleverTap from '../../../../../../utils/clevertap';
import { goBack as nativeGoBack } from '../../../../../../utils/native-methods';
import {
  getIsApplyPromoFulfilled,
  getIsApplyVoucherFulfilled,
  getIsApplyPayLaterPromoFulfilled,
  getIsApplyPayLaterVoucherFulfilled,
  getIsRewardDetailTypeVoucher,
  getRewardDetailId,
  getRewardDetailUniquePromotionCodeId,
  getRewardDetailCode,
} from '../../../../../../redux/modules/rewards/selectors';
import {
  fetchRewardDetail,
  applyPromo,
  applyVoucher,
  applyPayLaterPromo,
  applyPayLaterVoucher,
} from '../../../../../../redux/modules/rewards/thunks';
import {
  getIsWebview,
  getApiRequestShippingType,
  getIsAlipayMiniProgram,
  getLocationSearch,
  getUserIsLogin,
  getIsNotLoginInWeb,
  actions as appActions,
} from '../../../../../redux/modules/app';
import { getApplyRewardFulfillDate, getPayLaterReceiptNumber } from '../../../redux/selectors';
import { getRewardId, getRewardUniquePromotionCodeId, getRewardType } from './selectors';

export const mounted = createAsyncThunk('ordering/rewardDetail/mounted', async (_, { dispatch, getState }) => {
  const state = getState();
  const isWebview = getIsWebview(state);
  const isAlipayMiniProgram = getIsAlipayMiniProgram(state);
  const search = getLocationSearch(state);

  CleverTap.pushEvent('Voucher & Promo Details - View Page');

  await dispatch(appActions.getLoginStatus());

  if (isWebview) {
    await dispatch(appActions.loginByBeepApp());
  }

  if (isAlipayMiniProgram) {
    await dispatch(appActions.loginByAlipayMiniProgram());
  }

  const isLogin = getUserIsLogin(getState());
  const isNotLoginInWeb = getIsNotLoginInWeb(getState());

  if (isNotLoginInWeb) {
    dispatch(push(`${PATH_NAME_MAPPING.REWARDS_LOGIN}${search}`, { shouldGoBack: true }));

    return;
  }

  if (isLogin) {
    const id = getRewardId(getState());
    const uniquePromotionCodeId = getRewardUniquePromotionCodeId(getState());
    const type = getRewardType(getState());

    dispatch(fetchRewardDetail({ id, uniquePromotionCodeId, type }));
  }
});

export const goPageBack = createAsyncThunk('ordering/rewardDetail/goPageBack', async (_, { dispatch, getState }) => {
  const isWebview = getIsWebview(getState());

  if (isWebview) {
    dispatch(nativeGoBack());
    return;
  }

  dispatch(historyGoBack());
});

export const backButtonClicked = createAsyncThunk(
  'ordering/rewardDetail/backButtonClicked',
  async (_, { dispatch }) => {
    CleverTap.pushEvent('Voucher & Promo Details - Click Back');

    dispatch(goPageBack());
  }
);

export const applyReward = createAsyncThunk('ordering/rewardDetail/applyReward', async (_, { dispatch, getState }) => {
  const state = getState();
  const isRewardDetailTypeVoucher = getIsRewardDetailTypeVoucher(state);
  const id = getRewardDetailId(state);
  const uniquePromotionCodeId = getRewardDetailUniquePromotionCodeId(state);
  const code = getRewardDetailCode(state);
  const fulfillDate = getApplyRewardFulfillDate(state);
  const shippingType = getApiRequestShippingType(state);
  const search = getLocationSearch(state);
  const goBackReviewCartPage = () => {
    const cartSearch = getFilteredQueryString(Object.values(REWARD_DETAIL_QUERY_PARAMS), search);

    dispatch(replace(`${PATH_NAME_MAPPING.ORDERING_CART}${cartSearch}`));
  };

  if (isRewardDetailTypeVoucher) {
    await dispatch(applyVoucher({ fulfillDate, shippingType, code }));

    const isApplyVoucherFulfilled = getIsApplyVoucherFulfilled(getState());

    // PO design failure back to list page
    isApplyVoucherFulfilled ? goBackReviewCartPage() : dispatch(goPageBack());

    return;
  }

  await dispatch(applyPromo({ id, fulfillDate, shippingType, uniquePromotionCodeId }));

  const isApplyPromoFulfilled = getIsApplyPromoFulfilled(getState());

  // PO design failure back to list page
  isApplyPromoFulfilled ? goBackReviewCartPage() : dispatch(goPageBack());
});

export const applyPayLaterReward = createAsyncThunk(
  'ordering/rewardDetail/applyPayLaterReward',
  async (_, { dispatch, getState }) => {
    const state = getState();
    const receiptNumber = getPayLaterReceiptNumber(state);
    const isRewardDetailTypeVoucher = getIsRewardDetailTypeVoucher(state);
    const id = getRewardDetailId(state);
    const uniquePromotionCodeId = getRewardDetailUniquePromotionCodeId(state);
    const code = getRewardDetailCode(state);
    const search = getLocationSearch(state);
    const goBackReviewTableSummaryPage = () => {
      const tableSummarySearch = getFilteredQueryString(Object.values(REWARD_DETAIL_QUERY_PARAMS), search);

      dispatch(replace(`${PATH_NAME_MAPPING.ORDERING_TABLE_SUMMARY}${tableSummarySearch}`));
    };

    if (isRewardDetailTypeVoucher) {
      await dispatch(applyPayLaterVoucher({ receiptNumber, code }));

      const isApplyPayLaterVoucherFulfilled = getIsApplyPayLaterVoucherFulfilled(getState());

      // PO design failure back to list page
      isApplyPayLaterVoucherFulfilled ? goBackReviewTableSummaryPage() : dispatch(goPageBack());

      return;
    }

    await dispatch(applyPayLaterPromo({ receiptNumber, id, uniquePromotionCodeId }));
    const isApplyPayLaterPromoFulfilled = getIsApplyPayLaterPromoFulfilled(getState());

    // PO design failure back to list page
    isApplyPayLaterPromoFulfilled ? goBackReviewTableSummaryPage() : dispatch(goPageBack());
  }
);
