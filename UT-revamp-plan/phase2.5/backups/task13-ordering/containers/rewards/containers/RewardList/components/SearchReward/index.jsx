import _isEmpty from 'lodash/isEmpty';
import React, { useRef, useCallback, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMount } from 'react-use';
import { useTranslation } from 'react-i18next';
import { REWARD_APPLIED_CODE_ERRORS } from '../../../../../../../common/utils/rewards/constants';
import { getClassName, searchUpdateDebounce } from '../../../../../../../common/utils/ui';
import CleverTap from '../../../../../../../utils/clevertap';
import { getIsWebview } from '../../../../../../redux/modules/app';
import { getApplyRewardError } from '../../redux/selectors';
import { actions as rewardListActions } from '../../redux';
import { searchPromos } from '../../redux/thunks';
import Search from '../../../../../../../common/components/Input/Search';
import styles from './SearchReward.module.scss';

const SearchReward = () => {
  const searchInputRef = useRef(null);
  const { t } = useTranslation(['OrderingPromotion']);
  const dispatch = useDispatch();
  const [isChangingKeyword, setIsChangingKeyword] = useState(false);
  const [keyword, setKeyword] = useState(null);
  const applyRewardError = useSelector(getApplyRewardError);
  const isWebview = useSelector(getIsWebview);
  const handleChangeSearchKeyword = useCallback(
    searchKeyword => {
      const regex = /^[A-Za-z0-9]*$/;
      const isAvailableSearchKeyword = regex.test(searchKeyword);

      setKeyword(searchKeyword);

      dispatch(
        rewardListActions.searchBoxErrorUpdate(
          isAvailableSearchKeyword ? '' : REWARD_APPLIED_CODE_ERRORS.ENTER_INVALID_PROMO_CODE
        )
      );
      dispatch(rewardListActions.setIsSearchBoxEmpty(_isEmpty(searchKeyword)));

      if (isAvailableSearchKeyword) {
        setIsChangingKeyword(true);
        searchUpdateDebounce(searchKeyword, currentKeyword => {
          dispatch(searchPromos(currentKeyword));
          setIsChangingKeyword(false);
        });
      }
    },
    [dispatch]
  );
  const handleClearSearchKeyword = useCallback(() => {
    dispatch(searchPromos(''));
  }, [dispatch]);

  useMount(() => {
    searchInputRef.current?.focus();
  });

  useEffect(() => {
    // Only track the event when the search keyword is not empty and not changing
    if (!isChangingKeyword && keyword) {
      CleverTap.pushEvent('My Vouchers & Promos Page - Search tab');
    }
  }, [isChangingKeyword, keyword]);

  return (
    <section className={getClassName([isWebview ? styles.SearchRewardWebview : styles.SearchReward])}>
      <Search
        allowClear
        ref={searchInputRef}
        className={applyRewardError ? styles.SearchRewardSearchBox : null}
        placeholder={t('EnterPromoCodeHere')}
        searching={isChangingKeyword}
        onChangeInputValue={handleChangeSearchKeyword}
        onClearInput={handleClearSearchKeyword}
      />
      <span className={styles.SearchRewardApplyErrorMessage}>{applyRewardError}</span>
    </section>
  );
};

SearchReward.displayName = 'SearchReward';

export default SearchReward;
