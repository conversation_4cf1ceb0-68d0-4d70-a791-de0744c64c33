@use "../../../../../Variables" as *;
@use "../../../../../Animates" as *;

$rewards-colors: #f04b23;

.ordering-cart {
  height: 100vh;
  background-color: $gray-5;

  &__alert {
    .alert__content {
      max-width: 350px;
    }

    .alert__description {
      margin: auto;
      max-width: 300px;
      font-size: 1.1428rem;
      color: $gray-2;
    }

    .alert__buttons-group {
      padding: 0;
    }

    .alert__button {
      border-radius: 0;
    }
  }

  &__warning {
    background-color: #fffbe6;
  }

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
  }

  &__products-container {
    padding-bottom: 6.2vw;
  }

  &__additional-comments {
    background-color: $white;
  }

  &__textarea {
    width: 100%;
    border: 0;
  }

  &__button-back {
    width: 33.333333%;
  }
}

.cart-item {
  &__image-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: $white;
    background-color: rgba(0, 0, 0, 0.35);
    white-space: pre-line;
  }

  &__comments {
    word-break: break-word;
    color: $gray-3;
  }
}

.cart-cashback {
  &__button-login {
    height: 30px;
  }

  &__info-button {
    outline: none;
    cursor: pointer;
    border-style: none;
    background-color: transparent;
    color: $main-color;
  }

  &__item-primary {
    background-color: $theme-color-translucent-light;
  }

  &__switch-container {
    cursor: pointer;
    display: inline-block;
  }

  &__switch-label {
    &__active {
      color: $basic-text-basic;

      span {
        font-weight: $font-weight-bolder;
      }
    }

    &__inactive {
      color: $gray-2;
    }
  }

  &__toggle-checkbox {
    position: absolute;
    visibility: hidden;

    &:checked {
      + .cart-cashback__toggle-switch {
        background: $theme-color;

        &:before {
          left: 22px;
        }
      }
    }
  }

  &__toggle-switch {
    display: inline-block;
    background: $gray-4;
    border-radius: 18px;
    width: 48px;
    height: 28px;
    position: relative;
    vertical-align: middle;
    transition: background 0.25s;

    &:before,
    &:after {
      content: "";
    }

    &:before {
      display: block;
      background: $white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      position: absolute;
      top: 2px;
      left: 2px;
      transition: left 0.25s;
    }
  }
}

.cart-promotion {
  &__promotion-content {
    flex: 1;
    overflow: hidden;
  }

  &__button-acquisition {
    max-height: 50px;
  }

  &__rewards-number-text-container {
    position: relative;
    display: inline-flex;

    &::before,
    &::after {
      content: "";
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: $rewards-colors;
      border-width: 0;
      border-style: solid;
      border-color: $rewards-colors;
      border-radius: 24px;

      @extend .beep-animated;
      @extend .beep-animated--infinite;
      @extend .beep-scale-rectangle;
      @extend .beep-animated-default-duration;
      @include scale-rectangle(95, 25);
    }

    &::after {
      animation-delay: 0.4s;
    }
  }

  &__rewards-number-text {
    position: relative;
    padding-left: 12px;
    padding-right: 12px;
    color: $white;
    background-color: $rewards-colors;
    border-radius: 24px;
    z-index: 1;
  }
}

@media (min-width: 420px) {
  .ordering-cart {
    &__products-container {
      padding-bottom: 24px;
    }
  }
}
