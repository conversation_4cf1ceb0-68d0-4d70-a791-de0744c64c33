import i18next from 'i18next';
import { createAsyncThunk } from '@reduxjs/toolkit';
import logger from '../../../../utils/monitoring/logger';
import { updatePayLaterOrderRedirectUrl } from '../../../redux/modules/order/thunks';
import { getReceiptNumber, getOffline } from './selector';
import { postPayLaterOrderSubmission, getOrderStoreReview, postOrderStoreReview } from './api-info';
import { alert } from '../../../../common/utils/feedback';

export const submitPayLaterOrder = createAsyncThunk(
  'ordering/orderStatus/common/submitPayLaterOrder',
  async ({ receiptNumber, data }, { dispatch }) => {
    try {
      const result = await postPayLaterOrderSubmission(receiptNumber, data);

      if (result?.redirectUrl) {
        dispatch(updatePayLaterOrderRedirectUrl(result?.redirectUrl));
      }

      return result;
    } catch (error) {
      logger.error('Ordering_OrderStatus_submitPayLaterOrderFailed', {
        message: error?.message,
      });

      throw error;
    }
  }
);
// Store Review
export const showStoreReviewWarningModal = createAsyncThunk(
  'ordering/orderStatus/common/showStoreReviewWarningModal',
  async () => {}
);

export const hideStoreReviewWarningModal = createAsyncThunk(
  'ordering/orderStatus/common/hideStoreReviewWarningModal',
  async () => {}
);

export const showStoreReviewLoadingIndicator = createAsyncThunk(
  'ordering/orderStatus/common/showStoreReviewLoadingIndicator',
  async () => {}
);

export const hideStoreReviewLoadingIndicator = createAsyncThunk(
  'ordering/orderStatus/common/hideStoreReviewLoadingIndicator',
  async () => {}
);

export const loadOrderStoreReview = createAsyncThunk(
  'ordering/orderStatus/common/loadOrderStoreReview',
  async (_, { getState }) => {
    const offline = getOffline(getState());
    const orderId = getReceiptNumber(getState());
    const { data } = await getOrderStoreReview(orderId, offline);

    return data;
  }
);

export const saveOrderStoreReview = createAsyncThunk(
  'ordering/orderStatus/common/saveOrderStoreReview',
  async ({ rating, comments, allowMerchantContact }, { dispatch, getState }) => {
    const state = getState();
    const orderId = getReceiptNumber(state);
    const offline = getOffline(getState());

    try {
      await dispatch(showStoreReviewLoadingIndicator());
      await postOrderStoreReview({ orderId, rating, comments, allowMerchantContact, offline });
      await dispatch(hideStoreReviewLoadingIndicator());

      return { rating, comments, allowMerchantContact };
    } catch (e) {
      await dispatch(hideStoreReviewLoadingIndicator());

      if (e.code === '40028') {
        alert(i18next.t('ApiError:40028Description'), {
          title: i18next.t('ApiError:40028Title'),
        });
      } else {
        alert(i18next.t('OrderingThankYou:SubmissionFailedDescription'), {
          title: i18next.t('OrderingThankYou:SubmissionFailedTitle'),
        });
      }
      throw e;
    }
  }
);
