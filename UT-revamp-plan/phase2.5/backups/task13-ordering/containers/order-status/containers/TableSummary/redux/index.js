/* eslint-disable no-param-reassign */
import { createSlice } from '@reduxjs/toolkit';
import { API_REQUEST_STATUS } from '../../../../../../utils/constants';
import {
  payByCoupons,
  showRedirectLoader,
  hideRedirectLoader,
  showProcessingLoader,
  hideProcessingLoader,
  reloadBillingByCashback,
  removeOrderPromotion,
  removeOrderVoucher,
} from './thunks';

const initialState = {
  removeOrderPromotionRequest: {
    status: null,
    error: null,
  },
  removeOrderVoucherRequest: {
    status: null,
    error: null,
  },
  reloadBillingByCashbackRequest: {
    status: null,
    error: null,
  },

  payByCouponsRequest: {
    status: null,
    error: null,
  },

  redirectLoaderVisible: false,
  processingLoaderVisible: false,
};

export const { reducer, actions } = createSlice({
  name: 'ordering/tableSummary',
  initialState,
  reducers: {},
  extraReducers: {
    [removeOrderPromotion.pending.type]: state => {
      state.removeOrderPromotionRequest.status = API_REQUEST_STATUS.PENDING;
      state.removeOrderPromotionRequest.error = null;
    },
    [removeOrderPromotion.fulfilled.type]: state => {
      state.removeOrderPromotionRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.removeOrderPromotionRequest.error = null;
    },
    [removeOrderPromotion.rejected.type]: (state, { error }) => {
      state.removeOrderPromotionRequest.error = error;
      state.removeOrderPromotionRequest.status = API_REQUEST_STATUS.REJECTED;
    },
    [removeOrderVoucher.pending.type]: state => {
      state.removeOrderVoucherRequest.status = API_REQUEST_STATUS.PENDING;
      state.removeOrderVoucherRequest.error = null;
    },
    [removeOrderVoucher.fulfilled.type]: state => {
      state.removeOrderVoucherRequest.status = API_REQUEST_STATUS.FULFILLED;
      state.removeOrderVoucherRequest.error = null;
    },
    [removeOrderVoucher.rejected.type]: (state, { error }) => {
      state.removeOrderVoucherRequest.error = error;
      state.removeOrderVoucherRequest.status = API_REQUEST_STATUS.REJECTED;
    },
    [showRedirectLoader.fulfilled.type]: state => {
      state.redirectLoaderVisible = true;
    },
    [hideRedirectLoader.fulfilled.type]: state => {
      state.redirectLoaderVisible = false;
    },
    [showProcessingLoader.fulfilled.type]: state => {
      state.processingLoaderVisible = true;
    },
    [hideProcessingLoader.fulfilled.type]: state => {
      state.processingLoaderVisible = false;
    },
    [reloadBillingByCashback.pending.type]: state => {
      state.reloadBillingByCashbackRequest.error = null;
      state.reloadBillingByCashbackRequest.status = API_REQUEST_STATUS.PENDING;
    },
    [reloadBillingByCashback.fulfilled.type]: state => {
      state.reloadBillingByCashbackRequest.status = API_REQUEST_STATUS.FULFILLED;
    },
    [reloadBillingByCashback.rejected.type]: (state, { error }) => {
      state.reloadBillingByCashbackRequest.error = error;
      state.reloadBillingByCashbackRequest.status = API_REQUEST_STATUS.REJECTED;
    },
    [payByCoupons.pending.type]: state => {
      state.payByCouponsRequest.error = null;
      state.payByCouponsRequest.status = API_REQUEST_STATUS.PENDING;
    },
    [payByCoupons.fulfilled.type]: state => {
      state.payByCouponsRequest.status = API_REQUEST_STATUS.FULFILLED;
    },
    [payByCoupons.rejected.type]: (state, { error }) => {
      state.payByCouponsRequest.error = error;
      state.payByCouponsRequest.status = API_REQUEST_STATUS.REJECTED;
    },
  },
});

export default reducer;
