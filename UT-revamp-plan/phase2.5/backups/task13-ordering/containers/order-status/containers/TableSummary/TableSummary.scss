@use "../../../../../Variables" as *;
@use "../../../../../Animates" as *;
$table-summary-background: #fffbe6;
$rewards-colors: #f04b23;

.table-summary {
  height: 100vh;
  background-color: $gray-5;

  &__header-content::after {
    content: "";
    width: 24px;
    height: 24px;
    padding: 3.8vw;
  }

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    padding-bottom: 120px;
  }

  &__base-info {
    background-color: $white;
  }

  &__base-info-status--locked {
    color: $theme-color;
  }

  &__base-info-list {
    border-top: 1px dashed $gray-4;
    border-bottom: 1px dashed $gray-4;
  }

  &__sub-order {
    margin-bottom: 2.4vw;
    background-color: $white;
  }

  &__items {
    display: block;
  }

  &__image-container {
    position: relative;
    width: 18vw;
    height: 18vw;
    min-width: 80px;
    height: 80px;
    overflow: hidden;
  }

  &__image {
    width: 100%;
    height: 100%;
  }

  &__item-title {
    width: 100%;
    word-break: break-word;
    hyphens: none;
  }

  &__item-variations {
    color: $theme-color;
  }

  &__takeaway-variant {
    color: $status-primary-basic;
    font-weight: 700;
  }

  &__textarea {
    width: 100%;
    border: 0;
  }

  &__billing-container {
    margin-top: 2.4vw;
  }

  &__outline-button {
    color: $white;
  }

  &__promotion-content {
    flex: 1;
    overflow: hidden;
  }

  &__button-acquisition {
    max-height: 50px;
  }

  &__comments {
    word-break: break-word;
    hyphens: none;
    color: $gray-3;
  }

  &__pay-by-cash-only {
    position: sticky;
    background-color: $table-summary-background;
    box-shadow: 0px -4px 24px 0 rgba(0, 0, 0, 0.08);
    z-index: 1;
  }

  &__item-primary {
    background-color: $theme-color-translucent-light;
  }

  &__button-login {
    height: 30px;
  }

  &__cashback-info-button {
    outline: none;
    cursor: pointer;
    border-style: none;
    background-color: transparent;
    color: $main-color;
  }

  &__switch-container {
    cursor: pointer;
    display: inline-block;
  }

  &__switch-label {
    &__active {
      color: $basic-text-basic;

      span {
        font-weight: $font-weight-bolder;
      }
    }

    &__inactive {
      color: $gray-2;
    }
  }

  &__toggle-checkbox {
    position: absolute;
    visibility: hidden;

    &:checked {
      + .table-summary__toggle-switch {
        background: $theme-color;

        &:before {
          left: 22px;
        }
      }
    }
  }

  &__toggle-switch {
    display: inline-block;
    background: $gray-4;
    border-radius: 18px;
    width: 48px;
    height: 28px;
    position: relative;
    vertical-align: middle;
    transition: background 0.25s;

    &:before,
    &:after {
      content: "";
    }

    &:before {
      display: block;
      background: $white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      position: absolute;
      top: 2px;
      left: 2px;
      transition: left 0.25s;
    }
  }

  &__rewards-number-text-container {
    position: relative;
    display: inline-flex;

    &::before,
    &::after {
      content: "";
      display: flex;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: $rewards-colors;
      border-width: 0;
      border-style: solid;
      border-color: $rewards-colors;
      border-radius: 24px;

      @extend .beep-animated;
      @extend .beep-animated--infinite;
      @extend .beep-scale-rectangle;
      @extend .beep-animated-default-duration;
      @include scale-rectangle(95, 25);
    }

    &::after {
      animation-delay: 0.4s;
    }
  }

  &__rewards-number-text {
    position: relative;
    padding-left: 12px;
    padding-right: 12px;
    color: $white;
    background-color: $rewards-colors;
    border-radius: 24px;
    z-index: 1;
  }
}

.submit-order-confirm {
  top: 0;

  &__description {
    color: $gray-2;
    margin-left: auto;
    margin-right: auto;
    max-width: 280px;
  }

  &__default-button,
  &__fill-button {
    border-radius: 0;
    border: 0;
    border-top: 1px solid;
  }

  &__default-button {
    border-top-color: $gray-4;
  }

  &__fill-button {
    border-top-color: $theme-color;
  }
}

@media (min-width: 420px) {
  .table-summary {
    &__header-content::after {
      padding: 12px;
    }

    &__sub-order {
      margin-bottom: 8px;
    }

    &__billing-container {
      margin-top: 8px;
    }
  }
}

@media (min-width: 770px) {
  .table-summary {
    &__image-container {
      width: 72px;
      height: 72px;
    }
  }
}
