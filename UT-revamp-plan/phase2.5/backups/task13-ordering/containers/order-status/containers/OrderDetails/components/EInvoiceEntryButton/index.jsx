import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { CaretRight } from 'phosphor-react';
import { E_INVOICE_ENTRY_I18N_KEYS } from '../../../../constants';
import { openBrowserURL } from '../../../../../../../utils/native-methods';
import { getIsWebview } from '../../../../../../redux/modules/app';
import { getOrderEInvoiceLinkType, getOrderEInvoiceEntryLink } from '../../../../../../redux/modules/order/selectors';
import { getIsEInvoiceEntryButtonShow } from '../../../../redux/selector';
import styles from './EInvoiceEntryButton.module.scss';

const EInvoiceEntryButton = ({ pushCleverTapEvent }) => {
  const { t } = useTranslation(['OrderingThankYou']);
  const isWebview = useSelector(getIsWebview);
  const linkType = useSelector(getOrderEInvoiceLinkType);
  const entryLink = useSelector(getOrderEInvoiceEntryLink);
  const isEInvoiceEntryButtonShow = useSelector(getIsEInvoiceEntryButtonShow);
  const handleClickEInvoiceEntryButton = useCallback(() => {
    pushCleverTapEvent('Order Details - click e-invoice button');

    if (isWebview) {
      openBrowserURL({ url: entryLink });
      return;
    }

    window.location.href = entryLink;
  }, [pushCleverTapEvent, entryLink, isWebview]);

  if (!isEInvoiceEntryButtonShow) {
    return null;
  }

  return (
    <button
      className={styles.EInvoiceEntryButton}
      data-test-id="ordering.order-detail.e-invoice-entry-btn"
      onClick={handleClickEInvoiceEntryButton}
    >
      <span className={styles.EInvoiceEntryButtonText}>{t(E_INVOICE_ENTRY_I18N_KEYS[linkType])}</span>
      <CaretRight size={24} />
    </button>
  );
};

EInvoiceEntryButton.displayName = 'EInvoiceEntryButton';

EInvoiceEntryButton.propTypes = {
  pushCleverTapEvent: PropTypes.func,
};

EInvoiceEntryButton.defaultProps = {
  pushCleverTapEvent: () => {},
};

export default EInvoiceEntryButton;
