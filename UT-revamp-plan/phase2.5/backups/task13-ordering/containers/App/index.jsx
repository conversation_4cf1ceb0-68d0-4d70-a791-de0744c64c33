import _isEqual from 'lodash/isEqual';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators, compose } from 'redux';
import { withTranslation } from 'react-i18next';
import { SSE_ERROR_TYPES, SSE_EVENT_NAMES } from '../../../common/utils/sse/constants';
import {
  SSE_EVENT_LISTENERS_LIMITATIONS,
  SSE_SUBSCRIPTION_GROUPS,
  SSE_EVENT_LISTENER_PATHNAME_KEYS,
} from '../../utils/constants';
import { getQueryString } from '../../../common/utils';
import SSEClient from '../../../common/utils/sse';
import { getIsEnableSSE, getSSESubscriptionQueryParams, registerAddListeners } from '../../utils';
import { setQuerySourceToSessionStorage as setQuerySourceToSessionStorageThunk } from '../../redux/modules/common/thunks';
import { loadCart as loadCartThunk } from '../../redux/modules/cart/thunks';
import {
  syncPayLaterOrderAndStatus as syncPayLaterOrderAndStatusThunk,
  fetchOrder as fetchOrderThunk,
} from '../../redux/modules/order/thunks';
import {
  actions as appActionCreators,
  getOnlineStoreInfo,
  getError,
  getUser,
  getApiError,
  getBusinessInfo,
  getIsDynamicUrlExpiredResultShown,
  getIsDynamicUrl,
  getBusiness,
  getTableId,
  getEnablePayLater,
  getIsPayLaterSSEEnabled,
  getMerchantStoreId,
  getIsCoreBusinessAPICompleted,
  getLocation,
} from '../../redux/modules/app';
import {
  getAddressInfo as getAddressInfoThunk,
  setAddressInfo as setAddressInfoThunk,
} from '../../../redux/modules/address/thunks';
import { getIfAddressInfoExists } from '../../../redux/modules/address/selectors';
import { getPageError } from '../../../redux/modules/entities/error';
import Constants from '../../../utils/constants';
import '../../../Common.scss';
import Routes from '../Routes';
import DocumentFavicon from '../../../components/DocumentFavicon';
import MessageModal from '../../components/MessageModal';
import { gtmSetUserProperties } from '../../../utils/gtm';
import faviconImage from '../../../images/favicon.ico';
import Utils from '../../../utils/utils';
import * as NativeMethods from '../../../utils/native-methods';
import logger from '../../../utils/monitoring/logger';
import BeepWarningImage from '../../../images/beep-warning.svg';
import { alert, toast } from '../../../common/utils/feedback';
import Result from '../../../common/components/Result';
import ResultContent from '../../../common/components/Result/ResultContent';
import styles from './App.module.scss';

const { ROUTER_PATHS } = Constants;

class App extends Component {
  constructor(props) {
    super(props);

    const { setQuerySourceToSessionStorage } = props;

    this.sseClient = null;
    // Must set query source to session storage before in constructor, otherwise it will be reset to null
    setQuerySourceToSessionStorage();
  }

  async componentDidMount() {
    const { appActions } = this.props;
    const { pathname } = window.location;
    const isThankYouPage = pathname.includes(`${ROUTER_PATHS.THANK_YOU}`);
    const isOrderDetailPage = pathname.includes(`${ROUTER_PATHS.ORDER_DETAILS}`);
    const isMerchantInfPage = pathname.includes(`${ROUTER_PATHS.MERCHANT_INFO}`);
    const isReportIssuePage = pathname.includes(`${ROUTER_PATHS.REPORT_DRIVER}`);
    const { browser } = Utils.getUserAgentInfo();

    if (
      !(isThankYouPage || isOrderDetailPage || isMerchantInfPage || isReportIssuePage) &&
      (browser.includes('Safari') || browser.includes('AppleWebKit') || Utils.isIOSWebview())
    ) {
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.height = '100%';
      document.body.style.overflow = 'hidden';
    }

    this.visitErrorPage();

    try {
      this.checkIfDineInUrlExpired();

      window.addEventListener('sh-location-change', this.checkIfDineInUrlExpired);

      const initRequests = [this.initAddressInfo(), appActions.getLoginStatus(), appActions.fetchOnlineStoreInfo()];

      if (Utils.notHomeOrLocationPath(window.location.pathname)) {
        initRequests.push(appActions.loadCoreBusiness());
      }

      await Promise.all(initRequests);

      const { isCoreBusinessAPICompleted } = this.props;

      if (isCoreBusinessAPICompleted && !this.sseClient) {
        this.initSSE();
      }

      // Must go after getLoginStatus finishes
      // Potentially change consumerId through CREATE_LOGIN_SUCCESS, so go before initDeliveryDetails
      if (Utils.isWebview()) {
        await appActions.syncLoginFromNative();
      }

      // Must go after initAddressInfo & getLoginStatus & syncLoginFromNative finish
      await appActions.initDeliveryDetails();

      const { user, businessInfo, onlineStoreInfo } = this.props;

      const thankYouPageUrl = `${Constants.ROUTER_PATHS.ORDERING_BASE}${Constants.ROUTER_PATHS.THANK_YOU}`;

      if (window.location.pathname !== thankYouPageUrl) {
        this.setGtmData({
          onlineStoreInfo,
          userInfo: user,
          businessInfo,
        });
      }
    } catch (e) {
      // we don't need extra actions for exceptions, the state is already in redux.
    }
  }

  componentDidUpdate(prevProps) {
    const { pageError, isCoreBusinessAPICompleted, location } = this.props;
    const { isCoreBusinessAPICompleted: prevIsCoreBusinessAPICompleted, location: prevLocation } = prevProps;
    const { code } = prevProps.pageError || {};

    if (pageError.code && pageError.code !== code) {
      this.visitErrorPage();
    }

    if (
      (isCoreBusinessAPICompleted && !prevIsCoreBusinessAPICompleted) ||
      location.pathname !== prevLocation.pathname
    ) {
      this.initSSE();
    }
  }

  componentWillUnmount() {
    this.cleanUpSSE();
    window.removeEventListener('sh-location-change', this.checkIfDineInUrlExpired);
  }

  checkIfDineInUrlExpired = async () => {
    const { appActions, isDynamicUrl } = this.props;

    if (!isDynamicUrl) {
      return;
    }

    await appActions.checkUrlsValidation();
  };

  // SSE started
  handlePayLaterSSEError = error => {
    const { t } = this.props;
    const { type } = error;

    if (Object.values(SSE_ERROR_TYPES).includes(type)) {
      alert(t('StoreIsNotAvailableDescription'), {
        title: t('StoreIsNotAvailableTitle'),
        onClose: () => {
          window.location.href = `${window.location.origin}${ROUTER_PATHS.DINE}`;
        },
      });
    } else {
      toast(t('SSESyncUpUnavailable'), {
        duration: 3600000,
      });
    }
  };

  addPayLaterSSEEventsListener = () => {
    const currentLocationPathname = window.location.pathname;
    const receiptNumber = getQueryString('receiptNumber');
    const { loadCart, syncPayLaterOrderAndStatus } = this.props;
    const limitations = SSE_EVENT_LISTENERS_LIMITATIONS[SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING];
    const limitationsKeys = Object.keys(limitations);
    const eventFunctions = {
      [SSE_EVENT_NAMES.SHOPPING_CART_UPDATED]: loadCart,
      [SSE_EVENT_NAMES.ORDER_UPDATED]: () => {
        loadCart();
        syncPayLaterOrderAndStatus(receiptNumber);
      },
    };

    limitationsKeys.forEach(key => {
      const { eventName, pathnames } = limitations[key];
      const isPathnameMatched = pathnames.some(pathname => currentLocationPathname.includes(pathname));

      if (isPathnameMatched) {
        this.sseClient.addEventListener(eventName, eventFunctions[eventName]);
      } else {
        this.sseClient.removeEventListener(eventName);
      }
    });
  };

  removePayLaterSSEEventsListener = () => {
    if (this.sseClient) {
      this.sseClient.removeEventListener(SSE_EVENT_NAMES.SHOPPING_CART_UPDATED);
      this.sseClient.removeEventListener(SSE_EVENT_NAMES.TABLE_SUMMARY_UPDATED);
    }
  };

  addOrderedSSEEventsListener = () => {
    const currentLocationPathname = window.location.pathname;
    const receiptNumber = getQueryString('receiptNumber');
    const { fetchOrder } = this.props;
    const { eventName, pathnames } = SSE_EVENT_LISTENERS_LIMITATIONS[SSE_SUBSCRIPTION_GROUPS.ORDERED][
      SSE_EVENT_LISTENER_PATHNAME_KEYS.ORDER_UPDATED
    ];
    const isPathnameMatched = pathnames.some(pathname => currentLocationPathname.includes(pathname));

    if (isPathnameMatched) {
      this.sseClient.addEventListener(eventName, () => {
        fetchOrder(receiptNumber);
      });
    }
  };

  removeOrderedSSEEventsListener = () => {
    if (this.sseClient) {
      this.sseClient.removeEventListener(SSE_EVENT_NAMES.ORDER_UPDATED);
    }
  };

  handleSSEError = error => {
    this.handlePayLaterSSEError(error);
  };

  cleanUpSSE = () => {
    this.removePayLaterSSEEventsListener();
    this.removeOrderedSSEEventsListener();

    if (this.sseClient) {
      this.sseClient.close();
      this.sseClient = null;
    }
  };

  initSSE = () => {
    const currentLocationPathname = window.location.pathname;
    const receiptNumber = getQueryString('receiptNumber');
    const { isPayLaterEnabled, isPayLaterSSEEnabled, business, merchantStoreId, tableId } = this.props;
    const queryParams = getSSESubscriptionQueryParams(currentLocationPathname, {
      merchantName: business,
      storeId: merchantStoreId,
      tableId,
      receiptNumber,
    });

    if (!queryParams) {
      return;
    }

    if (this.sseClient) {
      const { topics: prevTopics, subscriberGroups: prevSubscriberGroups } = this.sseClient.queryParams;
      const { topics, subscriberGroups } = queryParams;

      if (subscriberGroups === prevSubscriberGroups && _isEqual([...prevTopics].sort(), [...topics].sort())) {
        return;
      }
    }

    if (!isPayLaterSSEEnabled) {
      return;
    }

    if (!getIsEnableSSE(isPayLaterEnabled, currentLocationPathname)) {
      return;
    }

    this.cleanUpSSE(); // Ensure no multiple instances by cleaning up the previous SSE connection

    this.sseClient = new SSEClient(process.env.REACT_APP_SSE_API_URL, {
      queryParams,
      onError: this.handleSSEError,
    });

    this.sseClient.connect();

    const groupCallbacks = {
      [SSE_SUBSCRIPTION_GROUPS.PAY_LATER_ORDERING]: this.addPayLaterSSEEventsListener,
      [SSE_SUBSCRIPTION_GROUPS.ORDERED]: this.addOrderedSSEEventsListener,
    };

    registerAddListeners(currentLocationPathname, groupCallbacks);
  };
  // SSE end

  initAddressInfo = async () => {
    const { getAddressInfo, setAddressInfo } = this.props;

    if (!Utils.isWebview()) {
      await getAddressInfo();
      return;
    }

    // TODO: For backward compatible sake, the code block can be deleted once the app is forced to update.
    try {
      const { BEEP_MODULE_METHODS } = NativeMethods;
      const hasSetAddressSupport = NativeMethods.hasMethodInNative(BEEP_MODULE_METHODS.SET_ADDRESS);

      if (!hasSetAddressSupport) {
        await getAddressInfo();
        const { ifAddressInfoExists } = this.props;
        if (ifAddressInfoExists) return;
      }
    } catch (error) {
      console.error(`Ordering App initAddressInfo: ${error?.message || ''}`);
    }

    try {
      const nativeAddressInfo = NativeMethods.getAddress();

      if (!nativeAddressInfo) return;

      const {
        savedAddressId,
        addressName: shortName,
        address: fullName,
        countryCode,
        postCode,
        lat,
        lng,
        city,
      } = nativeAddressInfo;

      await setAddressInfo({
        savedAddressId,
        shortName,
        fullName,
        coords: { lng: Number(lng), lat: Number(lat) },
        countryCode,
        postCode,
        city,
      });
    } catch (e) {
      logger.error('Ordering_App_GetAddress', { message: e.message, code: e.code, extra: e.extra });
    }
  };

  setGtmData = ({ onlineStoreInfo, userInfo, businessInfo }) => {
    const userProperties = { onlineStoreInfo, userInfo };

    if (businessInfo && businessInfo.stores && businessInfo.stores.length && businessInfo.stores[0].id) {
      userProperties.store = {
        id: businessInfo.stores[0].id,
      };
    }

    gtmSetUserProperties(userProperties);
  };

  handleCloseMessageModal = () => {
    const { appActions } = this.props;
    appActions.hideMessageModal();
  };

  handleApiErrorHide = apiError => {
    const { appActions } = this.props;
    const { redirectUrl } = apiError;
    const { ORDERING_BASE, ORDERING_LOCATION_AND_DATE, ORDERING_HOME } = ROUTER_PATHS;
    const h = getQueryString('h');
    const type = getQueryString('type');

    appActions.hideApiMessageModal();
    if (redirectUrl && window.location.pathname !== redirectUrl) {
      switch (redirectUrl) {
        case ORDERING_BASE + ORDERING_LOCATION_AND_DATE:
          window.location.href = `${
            window.location.origin
          }${redirectUrl}?h=${h}&type=${type}&callbackUrl=${encodeURIComponent(ORDERING_HOME)}`;
          break;
        default:
          window.location.href = `${window.location.origin}${redirectUrl}?h=${h}&type=${type}`;
      }
    }
  };

  handleExpiredUrlPageButtonClick = () => {
    if (Utils.isWebview()) {
      NativeMethods.closeWebView();
    } else {
      window.location.href = `${window.location.protocol}//${process.env.REACT_APP_QR_SCAN_DOMAINS}${ROUTER_PATHS.QRSCAN}`;
    }
  };

  visitErrorPage() {
    const { pageError } = this.props;
    const errorPageUrl = `${Constants.ROUTER_PATHS.ORDERING_BASE}${
      pageError && pageError.code && pageError.code !== '40011'
        ? Constants.ROUTER_PATHS.ERROR
        : `/${window.location.search}`
    }`;

    if (pageError && pageError.code && window.location.pathname !== errorPageUrl) {
      Utils.setSessionVariable('errorMessage', pageError.message);

      window.location.href = errorPageUrl;
    }
  }

  render() {
    const { t, onlineStoreInfo, apiError, isDynamicUrlExpiredResultShown } = this.props;
    const { favicon } = onlineStoreInfo || {};

    return (
      <main
        id="beep-app-container"
        className="table-ordering fixed-wrapper fixed-wrapper__main"
        data-test-id="ordering.app.container"
      >
        {apiError.show ? (
          <MessageModal
            data={apiError}
            onHide={() => {
              this.handleApiErrorHide(apiError);
            }}
          />
        ) : null}
        {isDynamicUrlExpiredResultShown ? (
          <Result
            customizeContent
            closeButtonClassName={styles.UrlExpiredButton}
            closeButtonContent={t('UrlExpiredButton')}
            zIndex={1000}
            onClose={this.handleExpiredUrlPageButtonClick}
          >
            <ResultContent
              content={t('UrlExpiredDescription')}
              title={t('UrlExpiredTitle')}
              imageSrc={BeepWarningImage}
            />
          </Result>
        ) : (
          <Routes />
        )}
        <DocumentFavicon icon={favicon || faviconImage} />
      </main>
    );
  }
}

App.displayName = 'OrderingApp';

App.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  location: PropTypes.object,
  user: PropTypes.shape({
    isExpired: PropTypes.bool,
    isWebview: PropTypes.bool,
  }),
  pageError: PropTypes.shape({
    code: PropTypes.string,
    message: PropTypes.string,
  }),
  apiError: PropTypes.shape({
    show: PropTypes.bool,
    redirectUrl: PropTypes.string,
  }),
  appActions: PropTypes.shape({
    getLoginStatus: PropTypes.func,
    loadCoreBusiness: PropTypes.func,
    syncLoginFromNative: PropTypes.func,
    initDeliveryDetails: PropTypes.func,
    fetchOnlineStoreInfo: PropTypes.func,
    hideMessageModal: PropTypes.func,
    hideApiMessageModal: PropTypes.func,
    checkUrlsValidation: PropTypes.func,
  }),
  business: PropTypes.string,
  merchantStoreId: PropTypes.string,
  tableId: PropTypes.string,
  /* eslint-disable react/forbid-prop-types */
  businessInfo: PropTypes.object,
  onlineStoreInfo: PropTypes.object,
  isPayLaterEnabled: PropTypes.bool,
  isPayLaterSSEEnabled: PropTypes.bool,
  isCoreBusinessAPICompleted: PropTypes.bool,
  /* eslint-enable */
  ifAddressInfoExists: PropTypes.bool,
  isDynamicUrlExpiredResultShown: PropTypes.bool,
  isDynamicUrl: PropTypes.bool,
  getAddressInfo: PropTypes.func,
  setAddressInfo: PropTypes.func,
  setQuerySourceToSessionStorage: PropTypes.func,
  loadCart: PropTypes.func,
  syncPayLaterOrderAndStatus: PropTypes.func,
  fetchOrder: PropTypes.func,
};

App.defaultProps = {
  location: {},
  user: {
    isExpired: false,
    isWebview: false,
  },
  pageError: {
    code: '',
    message: '',
  },
  apiError: {
    show: false,
    redirectUrl: '',
  },
  appActions: {
    getLoginStatus: () => {},
    loadCoreBusiness: () => {},
    syncLoginFromNative: () => {},
    initDeliveryDetails: () => {},
    fetchOnlineStoreInfo: () => {},
    hideMessageModal: () => {},
    hideApiMessageModal: () => {},
    checkUrlsValidation: () => {},
  },
  business: null,
  merchantStoreId: null,
  tableId: null,
  businessInfo: {},
  onlineStoreInfo: {},
  isPayLaterEnabled: false,
  isPayLaterSSEEnabled: false,
  ifAddressInfoExists: false,
  isDynamicUrlExpiredResultShown: false,
  isDynamicUrl: false,
  isCoreBusinessAPICompleted: false,
  getAddressInfo: () => {},
  setAddressInfo: () => {},
  setQuerySourceToSessionStorage: () => {},
  loadCart: () => {},
  syncPayLaterOrderAndStatus: () => {},
  fetchOrder: () => {},
};

export default compose(
  withTranslation(['ApiError', 'Common']),
  connect(
    state => ({
      location: getLocation(state),
      business: getBusiness(state),
      merchantStoreId: getMerchantStoreId(state),
      tableId: getTableId(state),
      onlineStoreInfo: getOnlineStoreInfo(state),
      businessInfo: getBusinessInfo(state),
      user: getUser(state),
      error: getError(state),
      pageError: getPageError(state),
      apiError: getApiError(state),
      isPayLaterEnabled: getEnablePayLater(state),
      isPayLaterSSEEnabled: getIsPayLaterSSEEnabled(state),
      ifAddressInfoExists: getIfAddressInfoExists(state),
      isDynamicUrlExpiredResultShown: getIsDynamicUrlExpiredResultShown(state),
      isDynamicUrl: getIsDynamicUrl(state),
      isCoreBusinessAPICompleted: getIsCoreBusinessAPICompleted(state),
    }),
    dispatch => ({
      getAddressInfo: bindActionCreators(getAddressInfoThunk, dispatch),
      setAddressInfo: bindActionCreators(setAddressInfoThunk, dispatch),
      setQuerySourceToSessionStorage: bindActionCreators(setQuerySourceToSessionStorageThunk, dispatch),
      appActions: bindActionCreators(appActionCreators, dispatch),
      loadCart: bindActionCreators(loadCartThunk, dispatch),
      syncPayLaterOrderAndStatus: bindActionCreators(syncPayLaterOrderAndStatusThunk, dispatch),
      fetchOrder: bindActionCreators(fetchOrderThunk, dispatch),
    })
  )
)(App);
