import ChuImage from '../../../../../images/food-court-temp/merchant-Chu.jpg';
import ThreeFiveImage from '../../../../../images/food-court-temp/merchant-555.jpg';
import RexBarImage from '../../../../../images/food-court-temp/merchant-Rex.jpg';
import CheongSamImage from '../../../../../images/food-court-temp/merchant-CheongSam.jpg';
import ChickenRiceImage from '../../../../../images/food-court-temp/merchant-chickenrice.jpg';
import ClaypotImage from '../../../../../images/food-court-temp/merchant-claypot.jpg';
import EmpireImage from '../../../../../images/food-court-temp/merchant-empire.jpg';
import GeprekImage from '../../../../../images/food-court-temp/merchant-geprek.jpg';
import HabibiImage from '../../../../../images/food-court-temp/merchant-habibi.jpg';
import IronImage from '../../../../../images/food-court-temp/merchant-iron.jpg';
import KeeImage from '../../../../../images/food-court-temp/merchant-kee.jpg';
import NasikandarImage from '../../../../../images/food-court-temp/merchant-nasikandar.jpg';
import SushiImage from '../../../../../images/food-court-temp/merchant-sushi.jpg';
import TeppanyakiImage from '../../../../../images/food-court-temp/merchant-teppanyaki.jpg';
import ThaifoodImage from '../../../../../images/food-court-temp/merchant-thaifood.jpg';
import ToastImage from '../../../../../images/food-court-temp/merchant-toast.jpg';
import WesternImage from '../../../../../images/food-court-temp/merchant-western.jpg';
import YewyewImage from '../../../../../images/food-court-temp/merchant-yewyew.jpg';
import GRunImage from '../../../../../images/food-court-temp/merchant-grun.jpg';
import StellarKLImage from '../../../../../images/food-court-temp/merchant-stellar-kl.jpg';
import AltaburgerImage from '../../../../../images/food-court-temp/merchant-altaburger.jpg';
import ThebaoguysImage from '../../../../../images/food-court-temp/merchant-thebaoguys.jpg';
import HijauKualaLumpurImage from '../../../../../images/food-court-temp/merchant-hijau.jpeg';
import FaceImage from '../../../../../images/food-court-temp/merchant-face.png';
import JuiceImage from '../../../../../images/food-court-temp/merchant-Juice.jpeg';
import SeoulImage from '../../../../../images/food-court-temp/merchant-Seoul.png';
import IronBarImage from '../../../../../images/food-court-temp/merchant-Iron-Bar.png';
import YuByRainGardenImage from '../../../../../images/food-court-temp/merchant-Yu-by-Rain-Garden.png';
import FengImage from '../../../../../images/food-court-temp/merchant-Feng.png';
import ShunImage from '../../../../../images/food-court-temp/merchant-Shun.png';
import ShunYuanImage from '../../../../../images/food-court-temp/merchant-SHUN-YUAN .jpeg';
import BKTImage from '../../../../../images/food-court-temp/merchant-BKT.jpeg';
import FoodImage from '../../../../../images/food-court-temp/merchant-Food.jpeg';
import DrinkImage from '../../../../../images/food-court-temp/merchant-Drink.jpeg';
import NurulImage from '../../../../../images/food-court-temp/food-Nurul-logo.jpg';
import NurulAsamImage from '../../../../../images/food-court-temp/merchant-Nurul-Asam.png';
import JomTarikImage from '../../../../../images/food-court-temp/merchant-Jom-Tarik.jpg';
import XOXOCoffeeImage from '../../../../../images/food-court-temp/merchant-xoxo-coffee.png';
import TintoCafeImage from '../../../../../images/food-court-temp/merchant-Tinto-Cafe.png';
import PrestigeCafeImage from '../../../../../images/food-court-temp/merchant-Prestige-Cafe.jpg';
import JoesWesternImage from '../../../../../images/food-court-temp/merchant-joes-western.jpeg';
import MalacoImage from '../../../../../images/food-court-temp/merchant-malaco.jpg';
import ArmyNavyImage from '../../../../../images/food-court-temp/merchant-army-navy.png';
import BigChillImage from '../../../../../images/food-court-temp/merchant-big-chill.png';
import CloudKitchenImage from '../../../../../images/food-court-temp/merchant-cloud-kitchen.png';
import FigaroImage from '../../../../../images/food-court-temp/merchant-figaro.png';
import NachoKingImage from '../../../../../images/food-court-temp/merchant-nacho-king.png';
import PotatoCornerImage from '../../../../../images/food-court-temp/merchant-potato-corner.png';
import SandBarImage from '../../../../../images/food-court-temp/merchant-sand-bar.png';
import SeasideBbqGrillImage from '../../../../../images/food-court-temp/merchant-seaside-bbq-grill.png';
import SnackBarImage from '../../../../../images/food-court-temp/merchant-snack-bar.png';
import TullysImage from '../../../../../images/food-court-temp/merchant-tullys.png';
import TwpImage from '../../../../../images/food-court-temp/merchant-twp.png';
import YellowCabImage from '../../../../../images/food-court-temp/merchant-yellow-cab.png';
import RoofDeckImage from '../../../../../images/food-court-temp/merchant-roof-deck.png';

export const AllFoodCourtStoreList = {
  '6294429ddf0225000788e6e5': {
    id: '6294429ddf0225000788e6e5',
    imageNoCompression: GRunImage,
    title: 'G-Run',
    tags: ['Pastry', 'Juice'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:30',
    },
  },
  '5ec4e07cf35afc0ca557a544': {
    id: '5ec4e07cf35afc0ca557a544',
    imageNoCompression: StellarKLImage,
    title: 'Stellar KL',
    tags: ['Coffee', 'Lemonade', 'Kombucha'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '21:00',
    },
  },
  '6197240d9c8fb2000690ea3c': {
    id: '6197240d9c8fb2000690ea3c',
    imageNoCompression: ChuImage,
    title: 'Chu By Fifty Tales',
    tags: ['Asian', 'Noodles', 'Non-halal'],
    businessHours: {
      validTimeFrom: '12:00',
      validTimeTo: '21:00',
    },
  },
  '6279de1c1171c90007dc1505': {
    id: '6279de1c1171c90007dc1505',
    imageNoCompression: ThreeFiveImage,
    title: '555 Local Snack Bar',
    tags: ['Bar', 'Grilled Food', 'Nasi Lemak', 'Non-Halal'],
    businessHours: {
      validTimeFrom: '12:00',
      validTimeTo: '21:00',
    },
  },
  '6093d8d3de125500068db4b2': {
    id: '6093d8d3de125500068db4b2',
    imageNoCompression: RexBarImage,
    title: 'The REX Bar',
    tags: ['Craft Beer', 'Pale Ale', 'Liquor'],
    businessHours: {
      validTimeFrom: '08:00',
      validTimeTo: '23:59',
    },
  },
  '6093c9996dc7070006af7497': {
    id: '6093c9996dc7070006af7497',
    imageNoCompression: CheongSamImage,
    title: 'Mezzanine - Cheong Somm',
    tags: ['Wine', 'Tapas'],
    businessHours: {
      validTimeFrom: '08:00',
      validTimeTo: '23:59',
    },
  },
  '633a4f52378c39000766b2a7': {
    id: '633a4f52378c39000766b2a7',
    imageNoCompression: FaceImage,
    title: 'Face To Face',
    tags: ['Asian', 'Noodles'],
    businessHours: {
      validTimeFrom: '09:00',
      validTimeTo: '21:00',
    },
  },
  '5935311ca80222a3745165f2': {
    id: '5935311ca80222a3745165f2',
    imageNoCompression: JuiceImage,
    title: 'Juicelab',
    tags: ['Juice', 'Fruit Bowls', 'Healthy'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:00',
    },
  },
  '625636f66d26b300084124eb': {
    id: '625636f66d26b300084124eb',
    imageNoCompression: SeoulImage,
    title: 'Seoul Korean Street Foods',
    tags: ['Korean', 'Asian'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:00',
    },
  },
  '63974c018f6d940007f79bb9': {
    id: '63974c018f6d940007f79bb9',
    title: 'Food',
    imageNoCompression: FoodImage,
    tags: ['Food'],
    businessHours: {
      validTimeFrom: '0:00',
      validTimeTo: '24:00',
    },
  },
  '63b63252202918000780b2db': {
    id: '63b63252202918000780b2db',
    title: 'Drink',
    imageNoCompression: DrinkImage,
    tags: ['Drinks', 'Beverage'],
    businessHours: {
      validTimeFrom: '0:00',
      validTimeTo: '24:00',
    },
  },
  '63b6324b95cea90007c31bf2': {
    id: '63b6324b95cea90007c31bf2',
    title: 'Drink',
    imageNoCompression: DrinkImage,
    tags: ['Drinks', 'Beverage'],
    businessHours: {
      validTimeFrom: '0:00',
      validTimeTo: '24:00',
    },
  },
  '60750940db81da0007dd1f70': {
    id: '60750940db81da0007dd1f70',
    title: 'Nurul Sate Batang Pinang',
    imageNoCompression: NurulImage,
    tags: ['Drinks', 'Rice', 'Noodles'],
    businessHours: {
      validTimeFrom: '07:30',
      validTimeTo: '14:00',
    },
  },
  '636c5b0fd4d68200074dc451': {
    id: '636c5b0fd4d68200074dc451',
    title: 'Nurul Asam Pedas Johor',
    imageNoCompression: NurulAsamImage,
    tags: ['Rice', 'Seafood', 'Local Food', 'Drinks'],
    businessHours: {
      validTimeFrom: '08:30',
      validTimeTo: '14:00',
    },
  },
  '63c76dcefd60c7000810a5c2': {
    id: '63c76dcefd60c7000810a5c2',
    imageNoCompression: JomTarikImage,
    title: 'Jom Tarik',
    tags: ['Local Favourites', 'Rice', 'Noodles'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '21:00',
    },
  },
  '642e864d6530b60008775fcc': {
    id: '642e864d6530b60008775fcc',
    imageNoCompression: XOXOCoffeeImage,
    title: 'XOXO Coffee',
    tags: ['Fusion Food', 'Coffee', 'Cakes', 'Pastries'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:00',
    },
  },
  '646dcd32356d2100070d16cc': {
    id: '646dcd32356d2100070d16cc',
    imageNoCompression: TintoCafeImage,
    title: 'Tinto Cafe',
    tags: ['Local Delicacies', 'Matcha', 'Coffee'],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:00',
    },
  },
  '65e1b25906f9de00087db009': {
    id: '65e1b25906f9de00087db009',
    imageNoCompression: PrestigeCafeImage,
    title: 'Prestige Cafe',
    tags: [],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:00',
    },
  },
  '633a539bb537780006036edf': {
    id: '633a539bb537780006036edf',
    imageNoCompression: JoesWesternImage,
    title: 'Joe’s Western',
    tags: ['Western', 'Pasta'],
    businessHours: {
      validTimeFrom: '09:00',
      validTimeTo: '21:00',
    },
  },
  '67e13ad10265410007f9821a': {
    id: '67e13ad10265410007f9821a',
    imageNoCompression: MalacoImage,
    title: 'Malaco',
    tags: [],
    businessHours: {
      validTimeFrom: '10:00',
      validTimeTo: '20:00',
    },
  },
  '61b93058cef3c900060ca990': {
    id: '61b93058cef3c900060ca990',
    imageNoCompression: TeppanyakiImage,
    title: 'Teppanyaki',
    tags: ['grilled food', 'japanese'],
  },
  '61b987643aee120006eb19ca': {
    id: '61b987643aee120006eb19ca',
    imageNoCompression: ChickenRiceImage,
    title: 'Chicken Rice',
    tags: ['chinese', 'chicken rice'],
  },
  '61b98797e5962b0006a40be6': {
    id: '61b98797e5962b0006a40be6',
    imageNoCompression: EmpireImage,
    title: 'Empire Laksa',
    tags: ['noodles'],
  },
  '61b997bd438dfb00065319cf': {
    id: '61b997bd438dfb00065319cf',
    imageNoCompression: GeprekImage,
    title: 'Geprek & Penyet',
    tags: ['chicken rice', 'noodles'],
  },
  '61b967f4e5962b00069da7c1': {
    id: '61b967f4e5962b00069da7c1',
    imageNoCompression: HabibiImage,
    title: 'Habibi Kunafa',
    tags: ['middle eastern', 'desserts'],
  },
  '61b993e83c1b7f0006cf57a6': {
    id: '61b993e83c1b7f0006cf57a6',
    imageNoCompression: IronImage,
    title: 'Iron Pepper Rice',
    tags: ['japanese', 'rice meals'],
  },
  '61b99722f501550006a51dde': {
    id: '61b99722f501550006a51dde',
    imageNoCompression: KeeImage,
    title: 'Kee Ngu Yen',
    tags: ['nasi lemak', 'coffee'],
  },
  '61b996e03aee120006ee2cf6': {
    id: '61b996e03aee120006ee2cf6',
    imageNoCompression: NasikandarImage,
    title: 'Nasi Kandar',
    tags: ['briyani', 'noodles'],
  },
  '61b99632f501550006a4e4ce': {
    id: '61b99632f501550006a4e4ce',
    imageNoCompression: ThaifoodImage,
    title: 'Sawasdee Grub',
    tags: ['thai', 'rice meals', 'noodles'],
  },
  '61b995e13aee120006edf90c': {
    id: '61b995e13aee120006edf90c',
    imageNoCompression: ClaypotImage,
    title: 'Sizzling & Claypot',
    tags: ['chinese', 'noodles', 'rice meals'],
  },
  '61b996a0438dfb000652eba3': {
    id: '61b996a0438dfb000652eba3',
    imageNoCompression: SushiImage,
    title: 'Kaiju Kare',
    tags: ['japanese', 'sushi', 'bento'],
  },
  '61b99758438dfb00065307c5': {
    id: '61b99758438dfb00065307c5',
    imageNoCompression: ToastImage,
    title: 'Toast & Beverages',
    tags: ['toast', 'drinks'],
  },
  '61b9978caf89aa00064225c4': {
    id: '61b9978caf89aa00064225c4',
    imageNoCompression: WesternImage,
    title: 'Western',
    tags: ['pasta'],
  },
  '61b967a59de26d0006d64b97': {
    id: '61b967a59de26d0006d64b97',
    imageNoCompression: YewyewImage,
    title: 'YewYew Coffee',
    tags: ['pastries', 'coffee'],
  },
  '6270db86f870ac000754deb6': {
    id: '6270db86f870ac000754deb6',
    imageNoCompression: AltaburgerImage,
    title: 'Alta Burger',
    tags: ['Burgers', 'Fries', 'Wine'],
  },
  '62ac9e5453c8fb0008d217bf': {
    id: '62ac9e5453c8fb0008d217bf',
    imageNoCompression: ThebaoguysImage,
    title: 'The Bao Guys',
    tags: ['Asian', 'Fusion', 'Baos'],
  },
  '62a93c5feaddfd0007134720': {
    id: '62a93c5feaddfd0007134720',
    imageNoCompression: HijauKualaLumpurImage,
    title: 'Hijau Kuala Lumpur',
    tags: ['Asian', 'Vegan', 'Sambal'],
  },
  '638eaf1ae3ad8a0009890c1d': {
    id: '638eaf1ae3ad8a0009890c1d',
    imageNoCompression: YuByRainGardenImage,
    title: '雨 Yu By Rain Garden',
    tags: ['Local Food', 'Beverages'],
  },
  '639008f3fe077600070ddf07': {
    id: '639008f3fe077600070ddf07',
    imageNoCompression: FengImage,
    title: '峰 Feng Western Food',
    tags: ['Western food'],
  },
  '639023ced5944b00074f3a23': {
    id: '639023ced5944b00074f3a23',
    imageNoCompression: ShunImage,
    title: '顺 中国味道	',
    tags: ['Chinese food'],
  },
  '6390241ec596ab000733c1a9': {
    id: '6390241ec596ab000733c1a9',
    imageNoCompression: ShunYuanImage,
    title: '顺原宫保',
    tags: ['Asian'],
  },
  '63902438f87109000728b09e': {
    id: '63902438f87109000728b09e',
    imageNoCompression: BKTImage,
    title: '肉骨茶',
    tags: ['Pork'],
  },
  '63902490e56c4c000880cede': {
    id: '63902490e56c4c000880cede',
    imageNoCompression: IronBarImage,
    title: 'Iron Bar',
    tags: ['Alcohol'],
  },
  '65ee68464fd4b000070fae61': {
    id: '65ee68464fd4b000070fae61',
    imageNoCompression: ArmyNavyImage,
    title: 'ARMY NAVY',
    tags: [],
  },
  '65ee68356f5f290007ab0b2e': {
    id: '65ee68356f5f290007ab0b2e',
    imageNoCompression: BigChillImage,
    title: 'BIG CHILL',
    tags: [],
  },
  '65ee68874fd4b000070fb954': {
    id: '65ee68874fd4b000070fb954',
    imageNoCompression: FigaroImage,
    title: 'FIGARO',
    tags: [],
  },
  '65ee67cd8683b5000725751d': {
    id: '65ee67cd8683b5000725751d',
    imageNoCompression: TwpImage,
    title: "MAX'S / THE WOOD PAVILLION",
    tags: [],
  },
  '65ee686a7d16400007f9d4b6': {
    id: '65ee686a7d16400007f9d4b6',
    imageNoCompression: NachoKingImage,
    title: 'NACHO KING',
    tags: [],
  },
  '65d444b5e4f607000779c201': {
    id: '65d444b5e4f607000779c201',
    imageNoCompression: CloudKitchenImage,
    title: "PANCAKE HOUSE, TERIYAKI BOY, DENCIO'S, SIZZLING STEAK",
    tags: [],
  },
  '65ee6fff4811c30007dc332e': {
    id: '65ee6fff4811c30007dc332e',
    imageNoCompression: PotatoCornerImage,
    title: 'POTATO CORNER',
    tags: [],
  },
  '65ee68917d16400007f9db43': {
    id: '65ee68917d16400007f9db43',
    imageNoCompression: SandBarImage,
    title: 'SAND BAR',
    tags: [],
  },
  '65ee68506f5f290007ab0e92': {
    id: '65ee68506f5f290007ab0e92',
    imageNoCompression: SeasideBbqGrillImage,
    title: 'SEASIDE BBQ&GRILL',
    tags: [],
  },
  '65ee68270e4a800007d2b6c0': {
    id: '65ee68270e4a800007d2b6c0',
    imageNoCompression: SnackBarImage,
    title: 'SNACK BAR',
    tags: [],
  },
  '65ee681c81f42400070eeb60': {
    id: '65ee681c81f42400070eeb60',
    imageNoCompression: TullysImage,
    title: "TULLY'S",
    tags: [],
  },
  '65ee685a6368e10007490188': {
    id: '65ee685a6368e10007490188',
    imageNoCompression: YellowCabImage,
    title: 'YELLOW CAB',
    tags: [],
  },
  '65ee67ed1756cd000750a4c9': {
    id: '65ee67ed1756cd000750a4c9',
    imageNoCompression: RoofDeckImage,
    title: 'ROOF DECK',
    tags: [],
  },
  '608b7c77cc9fd00006d831f3': {
    id: '608b7c77cc9fd00006d831f3',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/mariassteakcafe/beep/logo/6e9ee2ba-9585-4718-ae1d-e7ed425bafdf',
    title: 'syaftest17',
    tags: ['Pork-Free', 'Grill', 'Steak', 'Pasta', 'Chicken'],
  },
  '608b7c77cc9fd00006d83205': {
    id: '608b7c77cc9fd00006d83205',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/vcr/product/590697312b052bde4e48d1b5/37e15bba-05e2-4215-9fb1-f704fc1cb7bf',
    title: 'syaftest17',
    tags: ['Fusion', 'Nasi Lemak', 'Cold Desserts', 'Taco'],
  },
  '61e0e1411c47a200070273f5': {
    id: '61e0e1411c47a200070273f5',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/okonomi/product/611cad4155f0e8000606d099/495d616f-28eb-488d-c4a8-9c1994da8529',
    title: 'syaftest17',
    tags: ['Pork-Free', 'Fusion', 'Crab', 'Steak', 'Pasta'],
  },
  '61e0eee88bbae3000721a344': {
    id: '61e0eee88bbae3000721a344',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/okonomi/product/611cad4155f0e8000606d099/495d616f-28eb-488d-c4a8-9c1994da8529',
    title: 'syaftest17',
    tags: [],
  },
  '60547cce6cec1a0006c8bb23': {
    id: '60547cce6cec1a0006c8bb23',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/finchkl/product/615f1335046c6700066df6df/30ca06d6-a7c7-492d-9ca1-ac8424076534',
    title: 'aishahtest',
    tags: [],
  },
  '60da8d1c294124000660bb12': {
    id: '60da8d1c294124000660bb12',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/mariassteakcafe/beep/logo/6e9ee2ba-9585-4718-ae1d-e7ed425bafdf',
    title: 'aishahtest',
    tags: [],
  },
  '60dc0a6f607d7e000680a49e': {
    id: '60dc0a6f607d7e000680a49e',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/salakualalumpur/product/5c2612b8b331de69c87a1bf5/64d491a5-b4e5-47c9-dc4d-22e8c6f1dff1',
    title: 'aishahtest',
    tags: [],
  },
  '61515b6035983000070382b9': {
    id: '61515b6035983000070382b9',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/okonomi/product/611cad4155f0e8000606d099/495d616f-28eb-488d-c4a8-9c1994da8529',
    title: 'aishahtest',
    tags: [],
  },
  '61add4c6d37a3e0007b95cef': {
    id: '61add4c6d37a3e0007b95cef',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/doplohtiga/product/6020bfe8c3b3c4000634ce40/0d995e6c-ddf7-44d2-90da-4c5b22f41307',
    title: 'aishahtest',
    tags: [],
  },
  '620a319b5926990007a9724c': {
    id: '620a319b5926990007a9724c',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/vcr/product/590697312b052bde4e48d1b5/37e15bba-05e2-4215-9fb1-f704fc1cb7bf',
    title: 'aishahtest',
    tags: [],
  },
  '61dd565a8dd9aa0008979af5': {
    id: '61dd565a8dd9aa0008979af5',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/finchkl/product/615f1335046c6700066df6df/30ca06d6-a7c7-492d-9ca1-ac8424076534',
    title: 'miratest1',
    tags: [],
  },
  '61e0f759f814be00099da376': {
    id: '61e0f759f814be00099da376',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/haidilao/beep/logo/2eb0e76a-0635-4a7b-9240-cf40b1f8fecc',
    title: 'miratest1',
    tags: [],
  },
  '61e0fc8d8bbae3000721a8b5': {
    id: '61e0fc8d8bbae3000721a8b5',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/mariassteakcafe/beep/logo/6e9ee2ba-9585-4718-ae1d-e7ed425bafdf',
    title: 'miratest1',
    tags: [],
  },
  '621717573cb9650007bbb692': {
    id: '621717573cb9650007bbb692',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/salakualalumpur/product/5c2612b8b331de69c87a1bf5/64d491a5-b4e5-47c9-dc4d-22e8c6f1dff1',
    title: 'miratest1',
    tags: [],
  },
  '6218506b3cb9650007bbbfe7': {
    id: '6218506b3cb9650007bbbfe7',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/okonomi/product/611cad4155f0e8000606d099/495d616f-28eb-488d-c4a8-9c1994da8529',
    title: 'miratest1',
    tags: [],
  },
  '5e12c66eee8fd200068b50d7': {
    id: '5e12c66eee8fd200068b50d7',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/mariassteakcafe/beep/logo/6e9ee2ba-9585-4718-ae1d-e7ed425bafdf',
    title: 'jw',
    tags: ['Pork-Free', 'Grill', 'Steak', 'Pasta', 'Chicken'],
  },
  '608983ec761b420006b90f80': {
    id: '608983ec761b420006b90f80',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/salakualalumpur/product/5c2612b8b331de69c87a1bf5/64d491a5-b4e5-47c9-dc4d-22e8c6f1dff1',
    title: 'jw',
    tags: ['Fusion', 'Nasi Lemak', 'Cold Desserts', 'Taco'],
  },
  '60ed669916c14b0006720d09': {
    id: '60ed669916c14b0006720d09',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/okonomi/product/611cad4155f0e8000606d099/495d616f-28eb-488d-c4a8-9c1994da8529',
    title: 'jw',
    tags: ['Pork-Free', 'Fusion', 'Crab', 'Steak', 'Pasta'],
  },
  '5e15be72470b493f40485311': {
    id: '5e15be72470b493f40485311',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/doplohtiga/product/6020bfe8c3b3c4000634ce40/0d995e6c-ddf7-44d2-90da-4c5b22f41307',
    title: 'hc',
    tags: [],
  },
  '5e15be72470b493f40485323': {
    id: '5e15be72470b493f40485323',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/haidilao/beep/logo/2eb0e76a-0635-4a7b-9240-cf40b1f8fecc',
    title: 'hc',
    tags: ['Pork-Free', 'Fast Food', 'Burgers', 'Beef', 'Chicken'],
  },
  '5f0ff2f4830934000640b230': {
    id: '5f0ff2f4830934000640b230',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/finchkl/product/615f1335046c6700066df6df/30ca06d6-a7c7-492d-9ca1-ac8424076534',
    title: 'hc',
    tags: ['Chinese', 'Hot pot', 'Pork', 'Seafood'],
  },
  '5fe1980b9645e00006d01323': {
    id: '5fe1980b9645e00006d01323',
    image: 'https://d2ncjxd2rk2vpl.cloudfront.net/mariassteakcafe/beep/logo/6e9ee2ba-9585-4718-ae1d-e7ed425bafdf',
    title: "Maria's Signature KLCC",
    tags: ['Pork-Free', 'Grill', 'Steak', 'Pasta', 'Chicken'],
  },
  '5e3be808b2ce4c1727f3ee8c': {
    id: '5e3be808b2ce4c1727f3ee8c',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/salakualalumpur/product/5c2612b8b331de69c87a1bf5/64d491a5-b4e5-47c9-dc4d-22e8c6f1dff1',
    title: 'SALA (THE ROW)',
    tags: ['Fusion', 'Nasi Lemak', 'Cold Desserts', 'Taco'],
  },
  '6113395ce90787000600d58b': {
    id: '6113395ce90787000600d58b',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/okonomi/product/611cad4155f0e8000606d099/495d616f-28eb-488d-c4a8-9c1994da8529',
    title: 'Okonomi Pavilion (Pavillion Kuala Lumpur)',
    tags: [],
  },
  '601d7da4b3fc4b000657a089': {
    id: '601d7da4b3fc4b000657a089',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/doplohtiga/product/6020bfe8c3b3c4000634ce40/0d995e6c-ddf7-44d2-90da-4c5b22f41307',
    title: 'Doplohtiga (Titiwangsa)',
    tags: ['Pork-Free', 'Fast Food', 'Burgers', 'Beef', 'Chicken'],
  },
  '60e54941f118b900075321f3': {
    id: '60e54941f118b900075321f3',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/finchkl/product/615f1335046c6700066df6df/30ca06d6-a7c7-492d-9ca1-ac8424076534',
    title: 'Finch KL (The Westin KL)',
    tags: ['Pork-Free', 'Fusion', 'Crab', 'Steak', 'Pasta'],
  },
  '54b512762159c24803762acd': {
    id: '54b512762159c24803762acd',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/vcr/product/590697312b052bde4e48d1b5/37e15bba-05e2-4215-9fb1-f704fc1cb7bf',
    title: 'VCR  (Bukit Bintang)',
    tags: ['Western', 'Sandwiches', 'Cakes', 'Coffee'],
  },
  '60584c6c2c1966000688ac67': {
    id: '60584c6c2c1966000688ac67',
    title: 'storehubsyafinaz',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/vcr/product/590697312b052bde4e48d1b5/37e15bba-05e2-4215-9fb1-f704fc1cb7bf',
    tags: [],
  },
  '6066a9000c5cc30006affc5e': {
    id: '6066a9000c5cc30006affc5e',
    title: 'storehubsyafinaz',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/vcr/product/590697312b052bde4e48d1b5/37e15bba-05e2-4215-9fb1-f704fc1cb7bf',
    tags: ['Fusion', 'Nasi Lemak', 'Cold Desserts', 'Taco'],
  },
  '610779e39267a20006765182': {
    id: '610779e39267a20006765182',
    title: 'storehubsyafinaz',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/salakualalumpur/product/5c2612b8b331de69c87a1bf5/64d491a5-b4e5-47c9-dc4d-22e8c6f1dff1',
    tags: ['Pork-Free', 'Grill', 'Steak', 'Pasta', 'Chicken'],
  },
  '60500dc0153124000636d392': {
    id: '60500dc0153124000636d392',
    title: 'storehubaishah',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/salakualalumpur/product/5c2612b8b331de69c87a1bf5/64d491a5-b4e5-47c9-dc4d-22e8c6f1dff1',
    tags: [],
  },
  '620a2b94aae69f0006233207': {
    id: '620a2b94aae69f0006233207',
    title: 'aishahtest',
    image:
      'https://d2ncjxd2rk2vpl.cloudfront.net/doplohtiga/product/6020bfe8c3b3c4000634ce40/0d995e6c-ddf7-44d2-90da-4c5b22f41307',
    tags: [],
  },
};
