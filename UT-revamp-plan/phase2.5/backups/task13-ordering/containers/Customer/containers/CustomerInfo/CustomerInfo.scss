@use "../../../../../Variables" as *;

.ordering-customer {
  height: 100vh;
  background-color: $gray-6;

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
  }

  &__detail,
  &__button-link {
    width: 100%;
    background-color: $white;
    color: initial;
  }

  &__address-button {
    max-height: none;
    height: auto;
    width: 100%;
  }

  &__detail ~ &__detail::before {
    content: "";
    display: block;
    margin-left: 3.8vw;
    margin-right: 3.8vw;
    height: 2px;
    background-color: $gray-5;
  }

  &__time {
    display: block;
  }

  &__summary {
    width: 100%;
  }

  &__title {
    color: $theme-color;
  }

  &__address-detail-container::before {
    content: "";
    padding: 1.4vw;
    margin-left: 2.4vw;
    margin-right: 2.4vw;
    width: 18px;
    height: 18px;
  }

  &__address-detail {
    width: 100%;
    color: $gray-2;
    background-color: $gray-6;
  }

  &__address-content {
    width: 100%;
  }

  &__address-edit-button {
    color: $status-blue;
  }

  &__time {
    display: block;
  }

  &__button-back {
    width: 33.333333%;
  }
}

@media (min-width: 420px) {
  .ordering-customer {
    &__detail ~ &__detail::before {
      margin-left: 16px;
      margin-right: 16px;
    }

    &__address-detail-container::before {
      padding: 4px;
      margin-left: 8px;
      margin-right: 8px;
    }
  }
}
