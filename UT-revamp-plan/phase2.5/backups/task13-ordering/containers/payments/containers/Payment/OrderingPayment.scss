@use "../../../../../Variables" as *;

.ordering-payment {
  height: 100vh;

  &__container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
  }

  &__item-content {
    flex: 1;
  }

  &__image-container {
    display: inline-block;
    width: 16%;
  }

  &__image {
    width: 100%;
  }

  &__description {
    display: inline-block;
    width: 75%;
  }

  &__label {
    width: 100%;
  }

  &__item.disabled &__image-container {
    filter: grayscale(100%) contrast(100%) brightness(100%);
    -webkit-filter: grayscale(100%) contrast(100%) brightness(100%);
    -moz-filter: grayscale(100%) contrast(100%) brightness(100%);
    -ms-filter: grayscale(100%) contrast(100%) brightness(100%);
    -o-filter: grayscale(100%) contrast(100%) brightness(100%);
    transition: filter 0.4s;
    -webkit-transition: -webkit-filter 0.4s;
    -moz-transition: -moz-filter 0.4s;
    -ms-transition: -ms-filter 0.4s;
    -o-transition: -o-filter 0.4s;
  }

  &__item.disabled &__description,
  &__prompt {
    color: $gray-2;
  }

  &__item.disabled .radio {
    opacity: 0;
  }
}

.payment-item-prompt {
  top: 0;

  &__description {
    color: $gray-2;
    margin-left: auto;
    margin-right: auto;
    max-width: 280px;
  }

  &__default-button,
  &__fill-button {
    border-radius: 0;
    border: 0;
    border-top: 1px solid;
  }

  &__default-button {
    border-top-color: $gray-4;
  }

  &__fill-button {
    border-top-color: $theme-color;
  }
}

.ordering-card-list {
  &__image-container,
  &__description {
    display: inline-block;
  }

  &__image-container {
    width: 30%;
  }

  &__image {
    width: 80%;
  }

  &__description {
    width: 70%;
  }
}
