export const APP_TYPES = {
  CLEAR_ERROR: 'LOYALTY/APP/CLEAR_ERROR',

  // fetch core business
  FETCH_CORE_BUSINESS_REQUEST: 'LOYALTY/APP/FETCH_CORE_BUSINESS_REQUEST',
  <PERSON><PERSON><PERSON>_CORE_BUSINESS_SUCCESS: 'LOYALTY/APP/FETCH_CORE_BUSINESS_SUCCESS',
  FETCH_CORE_BUSINESS_FAILURE: 'LOYALTY/APP/FETCH_CORE_BUSINESS_FAILURE',

  // fetch business
  FETCH_CASHBACK_BUSINESS_REQUEST: 'LOYALTY/APP/FETCH_CASHBACK_BUSINESS_REQUEST',
  FETCH_CASHBACK_BUSINESS_SUCCESS: 'LOYALTY/APP/FETCH_CASHBACK_BUSINESS_SUCCESS',
  FETCH_CASH<PERSON><PERSON>K_BUSINESS_FAILURE: 'LOYALTY/APP/FETCH_CASHBACK_BUSINESS_FAILURE',

  // message modal
  SET_MESSAGE_INFO: 'LOYALTY/APP/SET_MESSAGE_INFO',
  SHOW_MESSAGE_MODAL: 'LOYALTY/APP/SHOW_MESSAGE_MODAL',
  HIDE_MESSAGE_MODAL: 'LOYALTY/APP/HIDE_MESSAGE_MODAL',

  // clear cashback message
  CLEAR_CASHBACK_MESSAGE_SUCCESS: 'LOYALTY/HOME/CLEAR_CASHBACK_MESSAGE_SUCCESS',

  // fetch login status
  FETCH_LOGIN_STATUS_REQUEST: 'LOYALTY/APP/FETCH_LOGIN_STATUS_REQUEST',
  FETCH_LOGIN_STATUS_SUCCESS: 'LOYALTY/APP/FETCH_LOGIN_STATUS_SUCCESS',
  FETCH_LOGIN_STATUS_FAILURE: 'LOYALTY/APP/FETCH_LOGIN_STATUS_FAILURE',
  RESET_LOGIN_STATUS_REQUEST: 'LOYALTY/APP/RESET_LOGIN_STATUS_REQUEST',

  // login
  CREATE_LOGIN_REQUEST: 'LOYALTY/APP/CREATE_LOGIN_REQUEST',
  CREATE_LOGIN_SUCCESS: 'LOYALTY/APP/CREATE_LOGIN_SUCCESS',
  CREATE_LOGIN_FAILURE: 'LOYALTY/APP/CREATE_LOGIN_FAILURE',
  CLEAR_TOKENS: 'LOYALTY/APP/CLEAR_TOKENS',

  // login by Alipay
  CREATE_LOGIN_ALIPAY_REQUEST: 'LOYALTY/APP/CREATE_LOGIN_ALIPAY_REQUEST',
  CREATE_LOGIN_ALIPAY_SUCCESS: 'LOYALTY/APP/CREATE_LOGIN_ALIPAY_SUCCESS',
  CREATE_LOGIN_ALIPAY_FAILURE: 'LOYALTY/APP/CREATE_LOGIN_ALIPAY_FAILURE',

  // get OTP
  RESET_GET_OTP_REQUEST: 'LOYALTY/APP/RESET_GET_OTP_REQUEST',
  GET_OTP_REQUEST: 'LOYALTY/APP/GET_OTP_REQUEST',
  GET_OTP_SUCCESS: 'LOYALTY/APP/GET_OTP_SUCCESS',
  GET_OTP_FAILURE: 'LOYALTY/APP/GET_OTP_FAILURE',

  // get phone WhatsApp support
  GET_WHATSAPPSUPPORT_REQUEST: 'LOYALTY/APP/GET_WHATSAPPSUPPORT_REQUEST',
  GET_WHATSAPPSUPPORT_SUCCESS: 'LOYALTY/APP/GET_WHATSAPPSUPPORT_SUCCESS',
  GET_WHATSAPPSUPPORT_FAILURE: 'LOYALTY/APP/GET_WHATSAPPSUPPORT_FAILURE',

  // OTP
  RESET_CREATE_OTP_REQUEST: 'LOYALTY/APP/RESET_CREATE_OTP_REQUEST',
  CREATE_OTP_REQUEST: 'LOYALTY/APP/CREATE_OTP_REQUEST',
  CREATE_OTP_SUCCESS: 'LOYALTY/APP/CREATE_OTP_SUCCESS',
  CREATE_OTP_FAILURE: 'LOYALTY/APP/CREATE_OTP_FAILURE',

  // Set login prompt
  SET_LOGIN_PROMPT: 'LOYALTY/APP/SET_LOGIN_PROMPT',

  // Fetch customer profile
  LOAD_CONSUMER_CUSTOMER_INFO_PENDING: 'loyalty/app/loadConsumerCustomerInfo/pending',
  LOAD_CONSUMER_CUSTOMER_INFO_FULFILLED: 'loyalty/app/loadConsumerCustomerInfo/fulfilled',
  LOAD_CONSUMER_CUSTOMER_INFO_REJECTED: 'loyalty/app/LoadConsumerCustomerInfo/rejected',
  RESET_CONSUMER_CUSTOMER_INFO: 'loyalty/app/resetConsumerCustomerInfo',

  // Update user
  UPDATE_USER: 'LOYALTY/APP/UPDATE_USER',

  // Profile
  LOAD_CONSUMER_PROFILE_PENDING: 'loyalty/profile/loadProfileInfo/pending',
  LOAD_CONSUMER_PROFILE_FULFILLED: 'loyalty/profile/loadProfileInfo/fulfilled',
  LOAD_CONSUMER_PROFILE_REJECTED: 'loyalty/profile/loadProfileInfo/rejected',

  // show request login page
  SHOW_LOGIN_MODAL: 'loyalty/app/showLoginModal',
  HIDE_LOGIN_MODAL: 'loyalty/app/hideLoginModal',

  // get cashback histories
  GET_CASHBACK_HISTORIES_REQUEST: 'LOYALTY/HOME/GET_CASHBACK_HISTORIES_REQUEST',
  GET_CASHBACK_HISTORIES_SUCCESS: 'LOYALTY/HOME/GET_CASHBACK_HISTORIES_SUCCESS',
  GET_CASHBACK_HISTORIES_FAILURE: 'LOYALTY/HOME/GET_CASHBACK_HISTORIES_FAILURE',
};

export const HOME_TYPES = {
  // set customerId
  SET_CUSTOMER_ID_SUCCESS: 'LOYALTY/HOME/SET_CUSTOMER_ID_SUCCESS',

  // receipt list
  FETCH_RECEIPT_LIST_REQUEST: 'LOYALTY/HOME/FETCH_RECEIPT_LIST_REQUEST',
  FETCH_RECEIPT_LIST_SUCCESS: 'LOYALTY/HOME/FETCH_RECEIPT_LIST_SUCCESS',
  FETCH_RECEIPT_LIST_FAILURE: 'LOYALTY/HOME/FETCH_RECEIPT_LIST_FAILURE',
};
