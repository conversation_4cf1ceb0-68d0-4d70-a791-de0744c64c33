.BillingAddressTitle {
  @apply tw-my-16 sm:tw-my-16px tw-text-lg tw-font-bold tw-capitalize;
}

.BillingAddressFormItemButtonContainer {
  @apply tw-relative;

  padding-bottom: 23.6px;
}

.BillingAddressFormItemButton {
  @apply tw-flex tw-items-center tw-gap-x-8 sm:tw-gap-x-8px tw-py-8 sm:tw-py-8px tw-px-12 sm:tw-px-12px tw-border tw-border-solid tw-border-gray-500 tw-rounded tw-w-full tw-cursor-pointer;
}

.BillingAddressFormItemButtonError {
  @apply tw-border-red;
}

.BillingAddressFormItemButtonLeft {
  @apply tw-flex-1 tw-flex tw-flex-col tw-gap-2 sm:tw-gap-2px;
}

.BillingAddressFormItemLabel {
  @apply tw-flex tw-items-center tw-gap-2 sm:tw-gap-2px tw-text-sm;
}

.BillingAddressFormItemLabelText {
  @apply tw-text-sm;
}

.BillingAddressFormItemLabelRequired {
  @apply tw-text-red;
}

.BillingAddressFormItemText {
  @apply tw-leading-relaxed;

  height: 20px;
}

.BillingAddressFormItemErrorMessage {
  @apply tw-absolute tw-bottom-0 tw-block tw-my-4 sm:tw-my-4px tw-text-sm tw-text-red;
}

.BillingAddressStateDrawer,
.BillingAddressCountryDrawer {
  @apply tw-p-0;
}

.BillingAddressStateDrawerCloseButton,
.BillingAddressCountryDrawerCloseButton {
  @apply tw-flex-shrink-0 tw-text-2xl tw-text-gray;
}

.BillingAddressStateDrawerHeaderTitleContainer,
.BillingAddressCountryDrawerHeaderTitleContainer {
  @apply tw-flex tw-flex-col tw-items-center;
}

.BillingAddressStateDrawerHeaderTitle,
.BillingAddressCountryDrawerHeaderTitle {
  @apply tw-font-bold tw-text-lg tw-leading-relaxed;
}
