import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLifecycles, useSetState } from 'react-use';
import { useTranslation } from 'react-i18next';
import { actions as rewardsActions } from '../../../../../redux/modules/rewards';
import { getIsConsumerEmptyReward, getIsSearchEmptyReward, getIsRewardListSearching } from './redux/selectors';
import { mounted } from './redux/thunks';
import Loader from '../../../../../common/components/Loader';
import SearchReward from './components/SearchReward';
import SkeletonLoader from './components/SkeletonLoader';
import TicketList from './components/TicketList';
import RewardDetailDrawer from './components/RewardDetailDrawer';
import EmptyVoucher from './components/EmptyVoucher';
import EmptySearchResult from './components/EmptySearchResult';
import styles from './RewardList.module.scss';

const RewardList = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const [mountedStatus, setMountedStatus] = useSetState(false);
  const isSearchEmptyReward = useSelector(getIsSearchEmptyReward);
  const isConsumerEmptyReward = useSelector(getIsConsumerEmptyReward);
  const isRewardListSearching = useSelector(getIsRewardListSearching);

  useLifecycles(
    () => {
      dispatch(mounted()).then(() => {
        setMountedStatus(true);
      });
    },
    () => {
      dispatch(rewardsActions.resetRewardsState());
    }
  );

  return (
    <>
      <SearchReward />
      {isSearchEmptyReward ? (
        <EmptySearchResult />
      ) : isConsumerEmptyReward ? (
        <EmptyVoucher />
      ) : isRewardListSearching ? (
        <section className={styles.RewardListSearchLoaderSection}>
          <div className={styles.RewardListSearchLoaderContainer}>
            <Loader className={styles.RewardListSearchLoader} size={30} />
            <span className={styles.RewardListSearchLoaderText}>{t('Searching')}</span>
          </div>
        </section>
      ) : mountedStatus ? (
        <>
          <TicketList />
          <RewardDetailDrawer />
        </>
      ) : (
        <SkeletonLoader />
      )}
    </>
  );
};

RewardList.displayName = 'RewardList';

export default RewardList;
