import { createSlice } from '@reduxjs/toolkit';
import { selectedRewardItem, showRewardDetailDrawer, hideRewardDetailDrawer } from './thunks';

const initialState = {
  searchBox: {
    isEmpty: true,
    error: null,
  },
  selectedReward: {
    id: null,
    uniquePromotionCodeId: null,
    code: null,
    type: null,
  },
  rewardDetailDrawerShow: false,
};

export const { reducer, actions } = createSlice({
  name: 'rewards/consumer/rewardList',
  initialState,
  reducers: {
    setIsSearchBoxEmpty: (state, { payload }) => {
      state.searchBox.isEmpty = payload;
    },
    searchBoxErrorUpdate: (state, { payload }) => {
      state.searchBox.error = payload;
    },
  },
  extraReducers: {
    [selectedRewardItem.fulfilled.type]: (state, { payload }) => {
      state.selectedReward.id = payload.id;
      state.selectedReward.uniquePromotionCodeId = payload.uniquePromotionCodeId;
      state.selectedReward.code = payload.code;
      state.selectedReward.type = payload.type;
    },
    [showRewardDetailDrawer.fulfilled.type]: state => {
      state.rewardDetailDrawerShow = true;
    },
    [hideRewardDetailDrawer.fulfilled.type]: state => {
      state.rewardDetailDrawerShow = false;
    },
  },
});

export default reducer;
