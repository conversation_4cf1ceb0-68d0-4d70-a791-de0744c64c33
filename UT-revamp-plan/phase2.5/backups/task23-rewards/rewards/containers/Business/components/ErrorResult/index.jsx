import React from 'react';
import PropTypes from 'prop-types';
import Result from '../../../../../common/components/Result';
import ResultContent from '../../../../../common/components/Result/ResultContent';

const ErrorResult = ({
  title,
  content,
  buttonText,
  imageSrc,
  header,
  mountAtRoot,
  isCloseButtonShow,
  onCloseButtonClick,
}) => (
  <Result
    header={header}
    mountAtRoot={mountAtRoot}
    show
    isCloseButtonShow={isCloseButtonShow}
    closeButtonContent={buttonText}
    onClose={onCloseButtonClick}
    disableBackButtonSupport
  >
    <ResultContent content={content} title={title} imageSrc={imageSrc} />
  </Result>
);

ErrorResult.displayName = 'ErrorResult';

ErrorResult.propTypes = {
  title: PropTypes.string,
  content: PropTypes.string,
  buttonText: PropTypes.string,
  imageSrc: PropTypes.string,
  header: PropTypes.node,
  mountAtRoot: PropTypes.bool,
  isCloseButtonShow: PropTypes.bool,
  onCloseButtonClick: PropTypes.func,
};

ErrorResult.defaultProps = {
  title: '',
  content: '',
  buttonText: '',
  imageSrc: '',
  header: null,
  mountAtRoot: false,
  isCloseButtonShow: true,
  onCloseButtonClick: () => {},
};

export default ErrorResult;
