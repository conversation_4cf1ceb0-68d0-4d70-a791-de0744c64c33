.MembershipTiersTabsSection {
  @apply tw-mx-auto;
}

.MembershipTiersTabsContainer {
  @apply tw-relative;
}

.MembershipTiersTabs {
  @apply tw-flex tw-justify-between tw-border-0 tw-border-b tw-border-solid tw-border-gray-400;
}

.MembershipTiersTabsActiveBlock {
  @apply tw-absolute tw-left-0 tw-bg-blue-darkest;

  top: 33px;
  height: 2px;
  min-width: 25%;
}

.MembershipTiersTab {
  @apply tw-flex-1 tw-text-center tw-cursor-pointer;
}

.MembershipTiersTabName {
  @apply tw-flex tw-items-center tw-justify-center tw-gap-4 sm:tw-gap-4px tw-w-full tw-p-6 sm:tw-p-6px tw-border-0 tw-bg-transparent tw-capitalize tw-leading-loose tw-text-gray-700 tw-cursor-pointer;

  outline: none;
  box-shadow: none;

  &__active {
    @apply tw-font-bold tw-text-blue-darkest;
  }
}

.MembershipTiersTabContent {
  @apply tw-hidden tw-px-12 sm:tw-px-12px;

  &__tab {
    min-height: 136px;
  }

  &__active {
    @apply tw-block;
  }
}

.MembershipTiersTabContentPrompt {
  @apply tw-my-16 sm:tw-my-16px tw-font-bold;
}

.MembershipTiersTabContentDescription {
  @apply tw-my-16 sm:tw-my-16px tw-space-y-16 sm:tw-space-y-16px;
}

.MembershipTiersTabContentDescriptionItem {
  @apply tw-flex tw-items-start tw-gap-8 sm:tw-gap-8px;

  &__locked {
    @apply tw-text-gray-700;
  }
}

.MembershipTiersTabContentDescriptionItemCheckedIcon {
  @apply tw-flex-shrink-0 tw-text-green;

  min-height: 22.4px;
}

.MembershipTiersTabContentDescriptionItemLockIcon {
  @apply tw-flex-shrink-0;

  min-height: 22.4px;
}

.MembershipTiersTabContentDescriptionItemText {
  @apply tw-leading-loose;
}
