import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useTranslation, Trans } from 'react-i18next';
import EarnedCashbackIcon from '../../../../../images/rewards-earned-cashback.svg';
import SeamlessLoyaltyImage from '../../../../../images/seamless-loyalty.png';
import CleverTap from '../../../../../utils/clevertap';
import { closeWebView } from '../../../../../utils/native-methods';
import { getEventTrackingUserCountry } from '../../../../../redux/modules/user/selectors';
import { getIsWebview } from '../../../../redux/modules/common/selectors';
import { getIsConfirmSharingNewCustomer } from '../../redux/common/selectors';
import { getIsCustomerCashbackAvailable } from './redux/selectors';
import NativeHeader from '../../../../../components/NativeHeader';
import { ObjectFitImage } from '../../../../../common/components/Image';
import { alert } from '../../../../../common/utils/feedback';
import SeamlessStoreInfo from './components/SeamlessStoreInfo';
import CashbackBlock from './components/CashbackBlock';
import styles from './SeamlessLoyalty.module.scss';

const SeamlessLoyalty = () => {
  const { t } = useTranslation('Rewards');
  const isWebview = useSelector(getIsWebview);
  const eventTrackingUserCountry = useSelector(getEventTrackingUserCountry);
  const isCustomerCashbackAvailable = useSelector(getIsCustomerCashbackAvailable);
  const isConfirmSharingNewCustomer = useSelector(getIsConfirmSharingNewCustomer);

  useEffect(() => {
    if (isCustomerCashbackAvailable) {
      alert(
        <div className={styles.SeamlessLoyaltyAlertContent}>
          <div className={styles.SeamlessLoyaltyAlertIcon}>
            <ObjectFitImage noCompression src={EarnedCashbackIcon} alt="Store New Member Icon in StoreHub" />
          </div>
          <h4 className={styles.SeamlessLoyaltyAlertTitle}>{t('SeamlessLoyaltyCashRedeemAlert')}</h4>
        </div>,
        {
          id: 'SeamlessLoyaltyInitialAlert',
          onClose: () => {
            CleverTap.pushEvent('POS Redemption Landing Page (Pop-up) - Click OKAY', {
              country: eventTrackingUserCountry,
            });
          },
        }
      );
    }
  }, [isCustomerCashbackAvailable, t, eventTrackingUserCountry]);

  return (
    <>
      {isWebview && (
        <NativeHeader
          navFunc={() => {
            CleverTap.pushEvent('POS Redemption Landing Page - Click Back', {
              country: eventTrackingUserCountry,
            });

            closeWebView();
          }}
        />
      )}
      <div className={`${styles.SeamlessLoyalty} tw-flex tw-flex-col`}>
        <SeamlessStoreInfo />
        {isCustomerCashbackAvailable ? (
          <section
            className={`${styles.SeamlessLoyaltyContent} tw-px-16 sm:tw-px-16px tw--mt-24 sm:tw--mt-24px tw-flex-1`}
          >
            <CashbackBlock />
          </section>
        ) : (
          <section className="tw-flex-1 tw-flex tw-flex-col tw-items-center">
            <h2
              className={`${styles.SeamlessLoyaltyGreetings} tw-flex-1 tw-flex tw-items-center tw-text-center tw-text-xl tw-leading-normal tw-font-bold`}
            >
              {isConfirmSharingNewCustomer ? (
                t('SeamlessLoyaltyNewUserGreetings')
              ) : (
                <Trans i18nKey="SeamlessLoyaltyUserGreetings">
                  Thanks for coming back! Visit us
                  <br />
                  again next time.
                </Trans>
              )}
            </h2>
            <ObjectFitImage
              noCompression
              className={`${styles.SeamlessLoyaltyDefaultImage} tw-flex-shrink-0`}
              src={SeamlessLoyaltyImage}
              alt="StoreHub store redemption"
            />
          </section>
        )}
      </div>
    </>
  );
};

SeamlessLoyalty.displayName = 'SeamlessLoyalty';

export default SeamlessLoyalty;
