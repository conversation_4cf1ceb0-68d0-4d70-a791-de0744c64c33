@use "../../../../../../../common/styles/variables" as *;

.SkeletonLoaderTicketContainer {
  @apply tw-block tw-p-12 sm:tw-p-12px;
}

.SkeletonLoaderTicket {
  height: 158px;
}

.SkeletonLoaderUniquePromoLimitationSection {
  @apply tw-p-12 sm:tw-p-12px;
}

.SkeletonLoaderUniquePromoLimitationTitleContainer {
  @apply tw-flex tw-items-center;

  max-width: 120px;
}

.SkeletonLoaderUniquePromoLimitationTitle {
  height: 24px;
}

.SkeletonLoaderUniquePromoLimitationDescriptionContainer {
  @apply tw-flex tw-flex-col tw-mt-16 sm:tw-mt-16px;

  max-width: 240px;
}

.SkeletonLoaderUniquePromoLimitationDescription {
  height: 22px;
}

.SkeletonLoaderHowToUseSection {
  @apply tw-p-12 sm:tw-p-12px;
}

.SkeletonLoaderHowToUseTitleContainer {
  @apply tw-flex tw-items-center;

  max-width: 100px;
}

.SkeletonLoaderHowToUseTitle {
  height: 25px;
}

.SkeletonLoaderHowToUseSubtitleContainer {
  @apply tw-flex tw-items-center tw-mt-16 sm:tw-mt-16px;

  max-width: 80px;
}

.SkeletonLoaderHowToUseSubtitle {
  height: 22px;
}

.SkeletonLoaderHowToUseDescriptionContainer {
  @apply tw-flex tw-flex-col tw-mt-16 sm:tw-mt-16px;
}

.SkeletonLoaderHowToUseDescription {
  height: 45px;
}

.SkeletonLoaderHowToUseItemsContainer {
  @apply tw-flex tw-flex-col tw-mt-4 sm:tw-mt-4px;

  max-width: 200px;
}

.SkeletonLoaderHowToUseItemsContainer > .SkeletonLoaderHowToUseItems:nth-child(1) {
  max-width: 80%;
}

.SkeletonLoaderHowToUseItemsContainer > .SkeletonLoaderHowToUseItems:nth-child(5) {
  max-width: 50%;
}
