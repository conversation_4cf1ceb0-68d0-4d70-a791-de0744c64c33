.MemberCardSection {
  @apply tw-relative tw-p-12 sm:tw-p-12px;

  &::before {
    @apply tw-absolute tw-left-0 tw-top-0 tw-w-full tw-bg-blue-darkest;

    content: "";
    height: 64.8%;
  }
}

.MemberCard {
  @apply tw-relative tw-p-12 sm:tw-p-12px tw-space-y-12 sm:tw-space-y-12px tw-rounded tw-w-full tw-bg-gray-50;

  min-height: 94px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
}

.MemberCardTop {
  @apply tw-flex tw-items-center tw-justify-between;
}

.MemberCardCustomerTierName {
  @apply tw-text-xl tw-font-bold tw-capitalize;
}

.MemberCardViewProfileButton {
  &:global(.type-text-ghost) {
    color: inherit;
  }
}

.MemberCardViewProfileButtonContent {
  @apply tw-flex tw-items-center tw-gap-4 sm:tw-gap-4px;
}

.MemberCardViewProfileText {
  @apply tw-text-sm tw-capitalize;
}

.MemberCardTiersProgress {
  @apply tw-relative;
}

.MemberCardProgress {
  @apply tw-absolute tw-top-1/2 tw-left-16px tw-right-16px tw-h-8px tw-rounded-lg tw-bg-gray-50 tw-overflow-hidden;

  transform: translateY(-50%);
}

.MemberCardProgressBar {
  @apply tw-absolute tw-left-0 tw-top-0 tw-inline-block tw-h-full tw-rounded tw-bg-blue-darkest;
}

.MemberCardTiers {
  @apply tw-relative tw-flex tw-items-center tw-justify-between;
}

.MemberCardTierIcon {
  @apply tw-inline-flex;

  width: 32px;
  height: 32px;

  > svg {
    width: 100%;
    height: 100%;
  }
}

.MemberCardTierProgressPromptContainer {
  @apply tw-flex tw-items-start tw-gap-2 sm:tw-gap-2px tw-my-2 sm:tw-my-2px;
}

.MemberCardTierProgressPrompt {
  @apply tw-leading-normal;

  > strong {
    font-size: inherit;
  }
}

.MemberCardTierProgressPromptToolTipContainer {
  @apply tw-relative tw-inline-flex tw-border-0 tw-bg-transparent;

  height: 1.4285rem;
  outline: none;
  box-shadow: none;
  color: inherit;

  &::before {
    @apply tw-absolute tw-top-1/2 tw-mt-2px tw-w-0 tw-h-0 tw-border-solid tw-border-transparent tw-invisible tw-opacity-0;

    border-width: 9px;
    border-bottom-width: 7px;
    border-bottom-color: rgba(0, 0, 0, 0.75);
    content: "";
  }

  &__show::before {
    @apply tw-visible tw-opacity-100;

    transition: all 0.3s ease-in-out;
  }

  svg {
    @apply tw-text-gray-800;

    color: inherit;
  }
}

.MemberCardTierProgressPromptToolTip {
  @apply tw-absolute tw-top-full tw-pt-8 sm:tw-pt-8px tw-z-100 tw-invisible tw-opacity-0;

  transform: translateX(-75%);
}

.MemberCardTierProgressPromptToolTipContainer__show > * {
  @apply tw-visible tw-opacity-100;

  transition: all 0.3s ease-in-out;
}

.MemberCardTierProgressPromptToolTipText {
  @apply tw-flex tw-p-6 sm:tw-p-6px tw-text-gray-50 tw-bg-black tw-bg-opacity-75 tw-rounded tw-whitespace-nowrap;
}
