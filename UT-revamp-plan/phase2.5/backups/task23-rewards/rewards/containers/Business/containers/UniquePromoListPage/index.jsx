import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useLifecycles } from 'react-use';
import { useTranslation } from 'react-i18next';
import RewardsEmptyListImage from '../../../../../images/rewards-empty-list-icon.svg';
import { getIsUniquePromoListEmpty } from '../../redux/common/selectors';
import { actions as rewardsCommonActions } from '../../redux/common';
import { getShouldSkeletonLoaderShow } from './redux/selectors';
import { backButtonClicked, mounted } from './redux/thunks';
import Frame from '../../../../../common/components/Frame';
import { ObjectFitImage } from '../../../../../common/components/Image';
import PageHeader from '../../../../../common/components/PageHeader';
import UniquePromoList from './components/UniquePromoList';
import SkeletonLoader from './components/SkeletonLoader';
import styles from './UniquePromoListPage.module.scss';

const UniquePromoListPage = () => {
  const { t } = useTranslation(['Rewards']);
  const dispatch = useDispatch();
  const isUniquePromoListEmpty = useSelector(getIsUniquePromoListEmpty);
  const shouldSkeletonLoaderShow = useSelector(getShouldSkeletonLoaderShow);
  const handleClickHeaderBackButton = useCallback(() => dispatch(backButtonClicked()), [dispatch]);

  useLifecycles(
    () => {
      dispatch(mounted());
    },
    () => {
      dispatch(rewardsCommonActions.loadUniquePromoListRequestReset());
    }
  );

  return (
    <Frame>
      <PageHeader
        className={styles.UniquePromoListPageHeader}
        title={t('MyRewards')}
        onBackArrowClick={handleClickHeaderBackButton}
      />
      {shouldSkeletonLoaderShow ? (
        <SkeletonLoader />
      ) : isUniquePromoListEmpty ? (
        <section className={styles.UniquePromoListEmptySection}>
          <div className={styles.UniquePromoListEmptyImage}>
            <ObjectFitImage noCompression src={RewardsEmptyListImage} />
          </div>
          <h4 className={styles.UniquePromoListEmptyTitle}>{t('UniquePromoListEmptyTitle')}</h4>
        </section>
      ) : (
        <section className={styles.UniquePromoListPageContent}>
          <UniquePromoList />
        </section>
      )}
    </Frame>
  );
};

UniquePromoListPage.displayName = 'UniquePromoListPage';

export default UniquePromoListPage;
