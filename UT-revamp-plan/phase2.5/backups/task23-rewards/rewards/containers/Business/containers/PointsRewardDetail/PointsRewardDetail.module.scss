@use "../../../../../common/styles/variables" as *;

.PointsRewardDetailTicket {
  @apply tw-m-16 sm:tw-m-16px;
}

.PointsRewardDetailTicket:global(.large.vertical) .PointsRewardDetailTicketMain {
  @apply tw-pb-16 sm:tw-pb-16px;
}

.PointsRewardDetailTicketMainContent {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-center;
}

.PointsRewardDetailTicketDiscountValue {
  @apply tw-text-3xl tw-font-bold;
}

.PointsRewardDetailTicketName {
  @apply tw-text-center tw-text-xl tw-leading-loose;
  @include text-line-clamp(2, 3.2em);

  width: 95%;
  max-width: 310px;
}

.PointsRewardDetailTicket:global(.large.vertical) .PointsRewardDetailTicketStub {
  @apply tw-text-center tw-pt-16 sm:tw-pt-16px;
}

.PointsRewardDetailTicketCostPoints {
  @apply tw-font-bold tw-bg-gray-200 tw-px-12 sm:tw-px-12px tw-py-4 sm:tw-py-4px tw-leading-loose tw-rounded-sm;
}

.PointsRewardDetailConditionTitle,
.PointsRewardDetailHowToUseTitle {
  @apply tw-text-lg tw-leading-relaxed tw-font-bold;
}

.PointsRewardDetailConditionContent {
  @apply tw-leading-loose;
}

.PointsRewardDetailContentArticleSection {
  @apply tw-m-16 sm:tw-m-16px;
}

.PointsRewardDetailHowToUseRedeemOnlineList {
  @apply tw-mx-8 sm:tw-mx-8px tw-list-disc tw-list-inside;
}

.PointsRewardDetailHowToUseRedeemOnlineItem {
  @apply tw-leading-loose;
}

.PointsRewardDetailApplicableProducts {
  @apply tw-m-16 sm:tw-m-16px tw-space-y-4 sm:tw-space-y-4px;
}

.PointsRewardDetailApplicableStores {
  @apply tw-m-16 sm:tw-m-16px tw-space-y-4 sm:tw-space-y-4px;
}

.PointsRewardDetailHowToUse {
  @apply tw-m-16 sm:tw-m-16px tw-space-y-16 sm:tw-space-y-16px;
}

.PointsRewardDetailHowToUseSubtitle {
  @apply tw-leading-loose tw-font-bold;
}

.PointsRewardDetailHowToUseContentDescription {
  @apply tw-leading-loose;
}

.PointsRewardDetailHowToUseRedeemOnlineList {
  @apply tw-mx-8 sm:tw-mx-8px tw-list-disc tw-list-inside;
}

.PointsRewardDetailHowToUseRedeemOnlineItem {
  @apply tw-leading-loose;
}

.PointsRewardDetailFooterContent {
  @apply tw-px-16 sm:tw-px-16px tw-py-8 sm:tw-py-8px;
}

.PointsRewardDetailFooterButton {
  @apply tw-uppercase;
}

.PointsRewardDetailClaimedAlertContent {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-center tw-leading-loose;
}

.PointsRewardDetailClaimedAlertIcon {
  @apply tw-mb-8 sm:tw-mb-8px tw-overflow-hidden;

  width: 120px;
  height: 80px;

  > figure {
    @apply tw-h-full tw-bg-transparent;
  }
}

.PointsRewardDetailClaimedAlertTitle {
  @apply tw-flex tw-justify-center tw-text-center tw-m-auto tw-text-xl tw-leading-normal tw-text-gray-800 tw-font-bold;

  max-width: 264px;
  word-break: break-word;
}

.PointsRewardDetailClaimedAlertDescription {
  @apply tw-flex tw-justify-center tw-text-center tw-mt-4 sm:tw-mt-4px tw-mb-8 sm:tw-mb-8px tw-leading-relaxed tw-text-gray-700;

  margin-left: auto;
  margin-right: auto;
  max-width: 360px;
  word-break: break-word;
}
