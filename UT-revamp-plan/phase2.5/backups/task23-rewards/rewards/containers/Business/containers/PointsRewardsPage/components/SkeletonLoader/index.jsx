import React from 'react';
import Skeleton, { SkeletonTheme } from '../../../../../../../common/components/ReactLoadingSkeleton';
import styles from './SkeletonLoader.module.scss';

const SkeletonLoader = () => (
  <SkeletonTheme duration={2}>
    <section className={styles.SkeletonLoaderPointsRewardsSection}>
      <Skeleton
        containerClassName={styles.SkeletonLoaderPointsRewardsContainer}
        className={styles.SkeletonLoaderPointsRewards}
        count={5}
      />
    </section>
  </SkeletonTheme>
);

SkeletonLoader.displayName = 'SkeletonLoader';

export default SkeletonLoader;
