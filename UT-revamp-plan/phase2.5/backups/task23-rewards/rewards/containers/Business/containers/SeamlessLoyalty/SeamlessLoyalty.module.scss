@use "../../../../../common/styles/variables" as *;

.SeamlessLoyalty {
  @apply tw-absolute tw-w-full tw-h-full;

  max-width: $contentWidthInBigScreen;
}

.SeamlessLoyaltyWeb {
  @extend .SeamlessLoyalty;
  @apply tw-fixed tw-left-0 tw-right-0 tw-flex tw-flex-col;
}

.SeamlessLoyaltyHeader {
  @apply tw-flex tw-justify-between tw-items-center tw-border-0 tw-border-b tw-border-solid tw-border-gray-200;

  max-height: 48px;
}

.SeamlessLoyaltyHeaderLogoContainer {
  @apply tw-flex tw-items-center tw-flex-shrink-0 tw-p-16 sm:tw-p-16px tw-h-full;
}

.SeamlessLoyaltyContent {
  margin-top: -24px;
}

.SeamlessLoyaltyWebContent {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-center tw-flex-1 tw-bg-no-repeat tw-bg-center;

  background-image: url("../../../../../images/seamless-loyalty-web-background.jpg");
  background-size: 100% 100%;
}

.SeamlessLoyaltyWebContentTitle {
  @apply tw-my-16 sm:tw-my-16px tw-text-center tw-text-3xl tw-leading-normal tw-text-gray-50 tw-font-bold;
}

.SeamlessLoyaltyWebLogoButtons {
  @apply tw-px-16 sm:tw-px-16px tw-my-16 sm:tw-my-16px tw-space-y-12 sm:tw-space-y-12px tw-w-full;

  min-height: 320px;
}

.SeamlessLoyaltyWebLogoButtonContent {
  @apply tw-flex tw-items-center tw-flex-1 tw-p-12 sm:tw-p-12px tw--m-8 sm:tw--m-8px tw-bg-gray-50 tw-rounded;

  width: 100%;
  height: 100%;
}

.SeamlessLoyaltyWebLogoButtonImage {
  @apply tw-flex-shrink-0;

  width: 32px;
  height: 32px;
}

.SeamlessLoyaltyWebLogoButtonText {
  @apply tw-text-left tw-flex-1 tw-px-8 sm:tw-px-8px tw-text-lg tw-font-bold;
}

.SeamlessLoyaltyGreetings {
  max-width: 260px;
}

.SeamlessLoyaltyDefaultImage {
  padding-top: 22%;
  background-color: transparent;
}

.SeamlessLoyaltyAlertContent {
  @apply tw-flex tw-flex-col tw-items-center tw-justify-center tw-leading-loose;
}

.SeamlessLoyaltyAlertIcon {
  @apply tw-mb-8 sm:tw-mb-8px tw-rounded-full tw-overflow-hidden;

  width: 80px;
  height: 80px;

  > figure {
    @apply tw-bg-transparent;
  }
}

.SeamlessLoyaltyAlertTitle {
  @apply tw-flex tw-justify-center tw-text-center tw-m-auto tw-text-xl tw-leading-normal tw-text-gray-800 tw-font-bold;

  max-width: 264px;
  word-break: break-word;
}

@media (min-width: 770px) {
  .SeamlessLoyaltyWeb {
    margin: auto;
    max-width: 414px;
  }
}
