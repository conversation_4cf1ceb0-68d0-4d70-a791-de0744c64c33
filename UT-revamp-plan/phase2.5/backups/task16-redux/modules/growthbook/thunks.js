import _merge from 'lodash/merge';
import { createAsyncThunk } from '@reduxjs/toolkit';
import GrowthBook from '../../../utils/growthbook';
import logger from '../../../utils/monitoring/logger';
import { FEATURE_KEYS, DEFAULT_FEATURE_FLAG_RESULTS } from './constants';

export const initGrowthBookInstance = createAsyncThunk('app/growthbook/initGrowthBookInstance', async () => {
  const growthbook = GrowthBook.getInstance();

  if (!growthbook) {
    throw new Error('GrowthBook is not enabled');
  }

  // NOTE: `init` call will not throw an error now.
  // Refer to: https://docs.growthbook.io/lib/js#error-handling
  const { success, source, error } = await growthbook.init({ timeout: 2000, streaming: true });

  if (success) {
    logger.log('Utils_GrowthBook_InitInstanceSucceed', { source });
  } else {
    logger.error('Utils_GrowthBook_InitInstanceFailed', {
      error: error?.message,
    });
  }
});

export const refreshFeatureFlags = createAsyncThunk('app/growthbook/refreshFeatureFlags', async () => {
  const growthbook = GrowthBook.getInstance();

  if (!growthbook) {
    throw new Error('GrowthBook is not enabled');
  }

  try {
    await growthbook.refreshFeatures();
  } catch (error) {
    logger.error('Utils_GrowthBook_RefreshFeaturesFailed', {
      error: error?.message,
    });

    throw error;
  }
});

export const updateFeatureFlagResults = createAsyncThunk('app/growthbook/updateFeatureFlagResults', () => {
  const growthbook = GrowthBook.getInstance();

  if (!growthbook) {
    throw new Error('GrowthBook is not enabled');
  }

  // Get the current feature flag results by iterating over the feature flag keys.
  const featureFlagResults = Object.values(FEATURE_KEYS)
    .map(key => ({ key, value: GrowthBook.getFeatureValue(key, DEFAULT_FEATURE_FLAG_RESULTS[key]) }))
    .reduce((acc, { key, value }) => ({ ...acc, [key]: value }), {});

  return _merge({}, DEFAULT_FEATURE_FLAG_RESULTS, featureFlagResults);
});
