# 导入路径替换完整性验证报告
生成时间: Thu Jun 19 12:30:27 CST 2025

## 概述
使用增强的grep命令深度搜索，确保没有代码仍在引用旧的工具函数路径。

## 1. 旧路径引用搜索结果

### 搜索模式:
- utils/constants
- utils/history
- utils/utils
- utils/api-request
- utils/request
- utils/datetime-lib
- utils/time-lib
- utils/form-validate
- utils/clevertap
- utils/native-methods
- utils/url
- utils/ui
- utils/geoUtils
- utils/modal-back-button-support

### 详细搜索结果:

#### utils/constants:
- **import语句**:       46 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       46 个
- **小计**: 92 个

**示例引用**:
```
src/e-invoice/components/BillingAddress/index.jsx:10:import { COUNTRIES, MALAYSIA_STATES, SEARCH_RADIO_LIST_INPUT_DEFAULT_FOCUS_DELAY } from '../../utils/constants';
src/e-invoice/components/SubmissionProcess/index.jsx:5:import { E_INVOICE_STATUS_TIMEOUT } from '../../utils/constants';
src/e-invoice/components/TransactionDetails/index.jsx:5:import { SEARCH_RADIO_LIST_INPUT_DEFAULT_FOCUS_DELAY } from '../../utils/constants';
src/e-invoice/components/BillingAddress/index.jsx:10:import { COUNTRIES, MALAYSIA_STATES, SEARCH_RADIO_LIST_INPUT_DEFAULT_FOCUS_DELAY } from '../../utils/constants';
src/e-invoice/components/SubmissionProcess/index.jsx:5:import { E_INVOICE_STATUS_TIMEOUT } from '../../utils/constants';
```

#### utils/history:
- **import语句**:        1 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        0 个
- **小计**: 1 个

**示例引用**:
```
src/user/containers/Routes.jsx:4:import { PAGE_ROUTES } from '../utils/history/constants';
```

#### utils/utils:
- **import语句**:       34 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       34 个
- **小计**: 68 个

**示例引用**:
```
src/stores/containers/Home/components/StoreList/index.jsx:3:import Utils from '../../../../../utils/utils';
src/components/Header.jsx:8:import Utils from '../utils/utils';
src/components/PhoneViewContainer.jsx:6:import Utils from '../utils/utils';
src/stores/containers/Home/components/StoreList/index.jsx:3:import Utils from '../../../../../utils/utils';
src/components/Header.jsx:8:import Utils from '../utils/utils';
```

#### utils/api-request:
- **import语句**:        2 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        2 个
- **小计**: 4 个

**示例引用**:
```
src/ordering/containers/payments/containers/Payment/index.jsx:46:import { fetchOrder } from '../../../../../utils/api-request';
src/ordering/containers/payments/redux/common/thunks/index.js:17:import { fetchOrder } from '../../../../../../utils/api-request';
src/ordering/containers/payments/containers/Payment/index.jsx:46:import { fetchOrder } from '../../../../../utils/api-request';
src/ordering/containers/payments/redux/common/thunks/index.js:17:import { fetchOrder } from '../../../../../../utils/api-request';
```

#### utils/request:
- **import语句**:        0 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        0 个
- **小计**: 0 个

#### utils/datetime-lib:
- **import语句**:       15 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       15 个
- **小计**: 30 个

**示例引用**:
```
src/rewards/containers/Business/components/CashbackBlock/index.jsx:7:import { formatTimeToDateString } from '../../../../../utils/datetime-lib';
src/rewards/containers/Business/containers/UniquePromoDetail/redux/selectors.js:11:import { formatTimeToDateString } from '../../../../../../utils/datetime-lib';
src/rewards/containers/Business/containers/CashbackCreditsHistory/CashbackHistory.jsx:6:import { formatTimeToDateString } from '../../../../../utils/datetime-lib';
src/rewards/containers/Business/components/CashbackBlock/index.jsx:7:import { formatTimeToDateString } from '../../../../../utils/datetime-lib';
src/rewards/containers/Business/containers/UniquePromoDetail/redux/selectors.js:11:import { formatTimeToDateString } from '../../../../../../utils/datetime-lib';
```

#### utils/time-lib:
- **import语句**:        0 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        0 个
- **小计**: 0 个

#### utils/form-validate:
- **import语句**:        0 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        0 个
- **小计**: 0 个

#### utils/clevertap:
- **import语句**:       70 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       70 个
- **小计**: 140 个

**示例引用**:
```
src/rewards/containers/Business/containers/UniquePromoDetail/redux/thunks.js:4:import CleverTap from '../../../../../../utils/clevertap';
src/rewards/containers/Business/containers/CashbackCreditsHistory/CashbackHistory.jsx:7:import CleverTap from '../../../../../utils/clevertap';
src/rewards/containers/Business/containers/CashbackCreditsHistory/StoreCreditsHistory.jsx:6:import CleverTap from '../../../../../utils/clevertap';
src/rewards/containers/Business/containers/UniquePromoDetail/redux/thunks.js:4:import CleverTap from '../../../../../../utils/clevertap';
src/rewards/containers/Business/containers/CashbackCreditsHistory/CashbackHistory.jsx:7:import CleverTap from '../../../../../utils/clevertap';
```

#### utils/native-methods:
- **import语句**:       41 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       41 个
- **小计**: 82 个

**示例引用**:
```
src/e-invoice/containers/Business/containers/Form/redux/thunks.js:3:import { goBack as nativeGoBack } from '../../../../../../utils/native-methods';
src/e-invoice/containers/Business/containers/Preview/redux/thunks.js:4:import { goBack as nativeGoBack } from '../../../../../../utils/native-methods';
src/e-invoice/containers/EInvoiceCategory/redux/thunks.js:3:import { goBack as nativeGoBack } from '../../../../utils/native-methods';
src/e-invoice/containers/Business/containers/Form/redux/thunks.js:3:import { goBack as nativeGoBack } from '../../../../../../utils/native-methods';
src/e-invoice/containers/Business/containers/Preview/redux/thunks.js:4:import { goBack as nativeGoBack } from '../../../../../../utils/native-methods';
```

#### utils/url:
- **import语句**:       20 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       20 个
- **小计**: 40 个

**示例引用**:
```
src/rewards/redux/modules/app/index.js:8:import Url from '../../../../utils/url';
src/stores/redux/modules/home.js:2:import Url from '../../../utils/url';
src/stores/redux/modules/tables.js:6:import Url from '../../../utils/url';
src/rewards/redux/modules/app/index.js:8:import Url from '../../../../utils/url';
src/stores/redux/modules/home.js:2:import Url from '../../../utils/url';
```

#### utils/ui:
- **import语句**:       10 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:       10 个
- **小计**: 20 个

**示例引用**:
```
src/common/components/Input/Input.jsx:3:import { getClassName } from '../../utils/ui';
src/common/components/Input/Birthday/index.jsx:6:import { getClassName } from '../../../utils/ui';
src/common/components/Input/PhoneNumber/PhoneNumber.jsx:5:import { getClassName } from '../../../utils/ui';
src/common/components/Input/Input.jsx:3:import { getClassName } from '../../utils/ui';
src/common/components/Input/Birthday/index.jsx:6:import { getClassName } from '../../../utils/ui';
```

#### utils/geoUtils:
- **import语句**:        8 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        8 个
- **小计**: 16 个

**示例引用**:
```
src/components/LocationPicker.jsx:8:} from '../utils/geoUtils';
src/components/LocationPicker.jsx:186:export * from '../utils/geoUtils';
src/ordering/containers/Menu/redux/stores/selectors.js:14:import { computeStraightDistance } from '../../../../../utils/geoUtils';
src/components/LocationPicker.jsx:8:} from '../utils/geoUtils';
src/components/LocationPicker.jsx:186:export * from '../utils/geoUtils';
```

#### utils/modal-back-button-support:
- **import语句**:        6 个
- **require语句**:        0 个
- **动态导入**:        0 个
- **路径字符串**:        6 个
- **小计**: 12 个

**示例引用**:
```
src/common/feedback/alert/Alert.jsx:5:import { withBackButtonSupport } from '../../../utils/modal-back-button-support';
src/common/components/Drawer/index.jsx:6:import { useBackButtonSupport } from '../../../utils/modal-back-button-support';
src/common/components/Result/index.jsx:5:import { useBackButtonSupport } from '../../../utils/modal-back-button-support';
src/common/feedback/alert/Alert.jsx:5:import { withBackButtonSupport } from '../../../utils/modal-back-button-support';
src/common/components/Drawer/index.jsx:6:import { useBackButtonSupport } from '../../../utils/modal-back-button-support';
```

## 2. 搜索总结

**总计发现**: 505 个旧路径引用需要处理

## 3. 特殊情况检查

### 注释中的引用:
- **单行注释**:       17 个引用
- **块注释**:        0 个引用

### 配置文件中的引用:
- **package.json**:        0 个引用
- **webpack.config.js**:        0 个引用
- **babel.config.js**:        0 个引用
- **.eslintrc.js**:        0 个引用

## 4. 清理建议

### 优先级处理顺序:
1. **高优先级**: import/require语句 - 影响代码功能
2. **中优先级**: 路径字符串引用 - 可能影响运行时
3. **低优先级**: 注释中的引用 - 仅影响文档
4. **最低优先级**: 配置文件引用 - 需要谨慎处理

### 建议的处理方法:
1. **使用自动清理脚本**: `UT-revamp-plan/phase3/cleanup-remaining-refs.sh`
2. **手动检查特殊情况**: 复杂的动态导入和配置文件引用
3. **分批处理**: 按模块逐个处理，便于测试验证
4. **验证测试**: 每次修改后运行相关测试

### 自动清理脚本:
已生成自动清理脚本: `UT-revamp-plan/phase3/cleanup-remaining-refs.sh`

使用方法:
```bash
./UT-revamp-plan/phase3/cleanup-remaining-refs.sh
```

## 5. 新路径使用验证

### 新路径导入统计:
- **总计**:      644 个新路径导入
- **constants**:      268 个
- **api**:       51 个
- **time**:       18 个
- **validation**:        2 个

## 6. 总结

- **旧路径引用**: 还有 505 个需要处理
- **新路径使用**: 已有      644 个在使用新路径
- **迁移进度**: 56.0% 已迁移到新路径
- **建议**: 使用生成的清理脚本处理剩余引用

---
*报告生成时间: Thu Jun 19 12:30:35 CST 2025*
