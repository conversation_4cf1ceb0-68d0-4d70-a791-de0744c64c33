#!/bin/bash
# 清理剩余旧路径引用脚本

echo "🧹 开始清理剩余的旧路径引用..."

# 1. 修复剩余的import语句
echo "1. 修复剩余的import语句..."

# CleverTap相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/clevertap'|from 'common/utils/analytics/clevertap'|g" {} \;

# Utils相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/utils'|from 'common/utils/system/utils'|g" {} \;

# Native methods相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/native-methods'|from 'common/utils/system/native-methods'|g" {} \;

# URL相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/url'|from 'common/utils/system/url'|g" {} \;

# UI相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/ui'|from 'common/utils/ui'|g" {} \;

# 2. 清理备份文件
echo "2. 清理备份文件..."
find src -name "*.bak" -delete

echo "✅ 清理完成！"
