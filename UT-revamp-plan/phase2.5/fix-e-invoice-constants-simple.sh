#!/bin/bash
# 简单修复 e-invoice 常量导入脚本

LOG_FILE="UT-revamp-plan/phase3/fix-e-invoice-constants-simple-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔧 简单修复 e-invoice 常量导入..."
echo "时间: $(date)"

# 定义需要从 e-invoice/utils/constants 导入的常量
E_INVOICE_SPECIFIC_CONSTANTS=(
    "E_INVOICE_STATUS_TIMEOUT"
    "E_INVOICE_APP_CONTAINER_ID"
    "E_INVOICE_TYPES"
    "E_INVOICE_STATUS"
    "E_INVOICE_BLOCK_ERROR_CODES"
    "GET_E_INVOICE_STATUS_ERROR_CODES"
    "POST_E_INVOICE_ERROR_CODES"
    "SUBMITTED_TIMEOUT_ERROR_MESSAGE"
    "MALAYSIA_STATES"
    "COUNTRIES"
    "CLASSIFICATIONS"
    "SPECIAL_FIELD_NAMES"
    "SEARCH_RADIO_LIST_INPUT_DEFAULT_FOCUS_DELAY"
    "PAGE_ROUTES"
    "PATHS"
    "GET_E_INVOICE_ERROR_CODES"
    "STATUS_TAG_COLORS"
    "STATUS_I18N_KEYS"
    "E_INVOICE_DOCUMENT_TYPES"
)

# 获取所有e-invoice文件
E_INVOICE_FILES=$(find src/e-invoice -name "*.js" -o -name "*.jsx")

echo "📋 处理 e-invoice 文件..."

for file in $E_INVOICE_FILES; do
    if [ -f "$file" ]; then
        # 检查文件是否包含从 common/utils/constants 的导入
        if grep -q "from.*common/utils/constants" "$file"; then
            echo "  🔧 处理文件: $file"
            
            # 备份文件
            cp "$file" "$file.backup"
            
            # 检查文件使用了哪些e-invoice特有的常量
            used_constants=()
            for const in "${E_INVOICE_SPECIFIC_CONSTANTS[@]}"; do
                if grep -q "\b$const\b" "$file"; then
                    used_constants+=("$const")
                fi
            done
            
            if [ ${#used_constants[@]} -gt 0 ]; then
                echo "    使用的e-invoice常量: ${used_constants[*]}"
                
                # 计算相对路径
                depth=$(echo "$file" | sed 's|src/e-invoice/||' | tr '/' '\n' | wc -l)
                depth=$((depth - 1))
                
                relative_prefix=""
                for ((i=0; i<depth; i++)); do
                    relative_prefix="../$relative_prefix"
                done
                
                e_invoice_path="${relative_prefix}utils/constants"
                
                # 构建导入列表
                import_list=$(IFS=', '; echo "${used_constants[*]}")
                
                # 从现有导入中移除这些常量
                for const in "${used_constants[@]}"; do
                    sed -i.tmp "s/, $const//g" "$file"
                    sed -i.tmp "s/$const, //g" "$file"
                    sed -i.tmp "s/{ $const }/{ }/g" "$file"
                done
                
                # 清理空的导入
                sed -i.tmp "/import { } from.*common\/utils\/constants/d" "$file"
                sed -i.tmp "/import {  } from.*common\/utils\/constants/d" "$file"
                
                # 在第一行添加新的导入
                temp_file=$(mktemp)
                echo "import { $import_list } from '$e_invoice_path';" > "$temp_file"
                cat "$file" >> "$temp_file"
                mv "$temp_file" "$file"
                
                # 清理临时文件
                rm -f "$file.tmp"
                
                echo "    ✅ 已添加 e-invoice 常量导入"
            else
                echo "    ⚠️ 未使用 e-invoice 特有常量"
            fi
            
            # 删除备份
            rm -f "$file.backup"
        fi
    fi
done

echo ""
echo "🧪 验证修复结果..."

# 检查剩余的错误
echo "📋 运行 ESLint 检查..."
if yarn eslint src/e-invoice --quiet; then
    echo "✅ e-invoice 目录 ESLint 通过"
else
    echo "⚠️ e-invoice 目录仍有一些错误，但应该大幅减少"
fi

echo ""
echo "✅ e-invoice 常量导入修复完成！"
