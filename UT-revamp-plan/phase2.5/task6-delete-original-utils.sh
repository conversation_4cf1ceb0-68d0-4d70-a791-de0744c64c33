#!/bin/bash
# 任务6: 删除原始工具函数文件脚本

LOG_FILE="UT-revamp-plan/phase3/task6-delete-original-utils-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🗑️ 任务6: 删除原始工具函数文件"
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 创建删除报告
REPORT_FILE="UT-revamp-plan/phase3/original-utils-deletion-$(date +%Y%m%d-%H%M%S).md"

cat > "$REPORT_FILE" << EOF
# 原始工具函数文件删除报告
生成时间: $(date)

## 概述
按依赖关系安全删除原始工具函数文件，叶子节点优先，确保不破坏现有功能。

EOF

echo ""
echo "🔍 1. 识别原始工具函数文件..."

# 获取所有utils目录下的原始工具函数文件
ORIGINAL_UTILS_FILES=$(find src/utils -name "*.js" -not -path "*/monitoring/*" -not -name "*.test.js" -not -name "*.spec.js" 2>/dev/null)
ORIGINAL_COUNT=$(echo "$ORIGINAL_UTILS_FILES" | grep -v "^$" | wc -l)

echo "发现原始工具函数文件: $ORIGINAL_COUNT 个"

cat >> "$REPORT_FILE" << EOF
## 1. 原始工具函数文件清单

**总计**: $ORIGINAL_COUNT 个原始工具函数文件

### 文件列表:
EOF

if [ $ORIGINAL_COUNT -gt 0 ]; then
    echo "  📋 原始工具函数文件列表:"
    echo "$ORIGINAL_UTILS_FILES" | while read file; do
        if [ -n "$file" ]; then
            echo "    - $file"
            echo "- $file" >> "$REPORT_FILE"
        fi
    done
else
    echo "  ✅ 没有发现原始工具函数文件"
    echo "- 没有发现原始工具函数文件" >> "$REPORT_FILE"
fi

echo ""
echo "🔍 2. 分析文件依赖关系..."

cat >> "$REPORT_FILE" << EOF

## 2. 依赖关系分析

### 文件使用情况:
EOF

# 检查每个原始工具函数文件的使用情况
echo "  📊 检查原始工具函数文件使用情况:"

DEPENDENCY_MAP=()
LOW_RISK_FILES=()
MEDIUM_RISK_FILES=()
HIGH_RISK_FILES=()

echo "$ORIGINAL_UTILS_FILES" | while read file; do
    if [ -n "$file" ] && [ -f "$file" ]; then
        # 提取文件名（不含路径和扩展名）
        filename=$(basename "$file" .js)
        
        # 搜索对该文件的导入引用
        import_count=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$filename\|import.*$filename" 2>/dev/null | grep -v "$file" | wc -l)
        
        echo "    $file: $import_count 个引用"
        echo "- **$file**: $import_count 个引用" >> "$REPORT_FILE"
        
        # 分类风险等级
        if [ $import_count -eq 0 ]; then
            echo "$file" >> "/tmp/low_risk_files.txt"
        elif [ $import_count -le 5 ]; then
            echo "$file" >> "/tmp/medium_risk_files.txt"
        else
            echo "$file" >> "/tmp/high_risk_files.txt"
        fi
        
        if [ $import_count -gt 0 ] && [ $import_count -le 3 ]; then
            echo "      引用文件:"
            find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$filename\|import.*$filename" 2>/dev/null | grep -v "$file" | head -3 | while read ref_file; do
                echo "        - $ref_file"
            done
        fi
    fi
done

# 读取分类结果
if [ -f "/tmp/low_risk_files.txt" ]; then
    LOW_RISK_COUNT=$(wc -l < "/tmp/low_risk_files.txt")
else
    LOW_RISK_COUNT=0
fi

if [ -f "/tmp/medium_risk_files.txt" ]; then
    MEDIUM_RISK_COUNT=$(wc -l < "/tmp/medium_risk_files.txt")
else
    MEDIUM_RISK_COUNT=0
fi

if [ -f "/tmp/high_risk_files.txt" ]; then
    HIGH_RISK_COUNT=$(wc -l < "/tmp/high_risk_files.txt")
else
    HIGH_RISK_COUNT=0
fi

echo ""
echo "🎯 3. 确定安全删除顺序..."

cat >> "$REPORT_FILE" << EOF

## 3. 删除策略

### 风险分类:
- **低风险** (0个引用): $LOW_RISK_COUNT 个文件
- **中风险** (1-5个引用): $MEDIUM_RISK_COUNT 个文件  
- **高风险** (>5个引用): $HIGH_RISK_COUNT 个文件

### 删除顺序:
EOF

echo "  📋 风险分类结果:"
echo "    🟢 低风险 (0个引用): $LOW_RISK_COUNT 个文件"
echo "    🟡 中风险 (1-5个引用): $MEDIUM_RISK_COUNT 个文件"
echo "    🔴 高风险 (>5个引用): $HIGH_RISK_COUNT 个文件"

# 显示具体文件
if [ -f "/tmp/low_risk_files.txt" ]; then
    echo "    低风险文件:"
    cat "/tmp/low_risk_files.txt" | while read file; do
        echo "      - $file"
    done
    
    cat >> "$REPORT_FILE" << EOF
#### 第一批（低风险）- 无引用文件: $LOW_RISK_COUNT 个
EOF
    cat "/tmp/low_risk_files.txt" | while read file; do
        echo "- $file" >> "$REPORT_FILE"
    done
fi

if [ -f "/tmp/medium_risk_files.txt" ]; then
    echo "    中风险文件:"
    cat "/tmp/medium_risk_files.txt" | while read file; do
        echo "      - $file"
    done
    
    cat >> "$REPORT_FILE" << EOF

#### 第二批（中风险）- 少量引用文件: $MEDIUM_RISK_COUNT 个
EOF
    cat "/tmp/medium_risk_files.txt" | while read file; do
        echo "- $file" >> "$REPORT_FILE"
    done
fi

if [ -f "/tmp/high_risk_files.txt" ]; then
    echo "    高风险文件:"
    cat "/tmp/high_risk_files.txt" | while read file; do
        echo "      - $file"
    done
    
    cat >> "$REPORT_FILE" << EOF

#### 第三批（高风险）- 大量引用文件: $HIGH_RISK_COUNT 个
EOF
    cat "/tmp/high_risk_files.txt" | while read file; do
        echo "- $file" >> "$REPORT_FILE"
    done
fi

echo ""
echo "🗑️ 4. 执行安全删除..."

cat >> "$REPORT_FILE" << EOF

## 4. 删除执行记录

EOF

TOTAL_DELETED=0

# 删除第一批（低风险）
if [ -f "/tmp/low_risk_files.txt" ] && [ $LOW_RISK_COUNT -gt 0 ]; then
    echo "  🗑️ 删除第一批（低风险）文件..."
    cat >> "$REPORT_FILE" << EOF
### 第一批删除（低风险）:
EOF
    
    cat "/tmp/low_risk_files.txt" | while read file; do
        if [ -n "$file" ] && [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
        fi
    done
    
    TOTAL_DELETED=$((TOTAL_DELETED + LOW_RISK_COUNT))
    echo "  ✅ 第一批删除完成: $LOW_RISK_COUNT 个文件"
fi

# 删除第二批（中风险）- 需要更谨慎
if [ -f "/tmp/medium_risk_files.txt" ] && [ $MEDIUM_RISK_COUNT -gt 0 ]; then
    echo "  🗑️ 删除第二批（中风险）文件..."
    cat >> "$REPORT_FILE" << EOF

### 第二批删除（中风险）:
EOF
    
    cat "/tmp/medium_risk_files.txt" | while read file; do
        if [ -n "$file" ] && [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
        fi
    done
    
    TOTAL_DELETED=$((TOTAL_DELETED + MEDIUM_RISK_COUNT))
    echo "  ✅ 第二批删除完成: $MEDIUM_RISK_COUNT 个文件"
fi

# 第三批（高风险）暂时保留
if [ $HIGH_RISK_COUNT -gt 0 ]; then
    echo "  ⚠️ 第三批（高风险）文件暂时保留，需要进一步分析"
    cat >> "$REPORT_FILE" << EOF

### 第三批（高风险）- 暂时保留:
EOF
    
    if [ -f "/tmp/high_risk_files.txt" ]; then
        cat "/tmp/high_risk_files.txt" | while read file; do
            echo "    保留: $file"
            echo "- ⚠️ 保留: $file（需要进一步分析引用）" >> "$REPORT_FILE"
        done
    fi
fi

echo ""
echo "🧪 5. 验证删除结果..."

# 验证删除结果
REMAINING_UTILS=$(find src/utils -name "*.js" -not -path "*/monitoring/*" -not -name "*.test.js" -not -name "*.spec.js" 2>/dev/null | wc -l)

echo "  删除前: $ORIGINAL_COUNT 个原始工具函数文件"
echo "  删除后: $REMAINING_UTILS 个原始工具函数文件"
echo "  已删除: $TOTAL_DELETED 个原始工具函数文件"

cat >> "$REPORT_FILE" << EOF

## 5. 删除结果验证

- **删除前**: $ORIGINAL_COUNT 个原始工具函数文件
- **删除后**: $REMAINING_UTILS 个原始工具函数文件
- **已删除**: $TOTAL_DELETED 个原始工具函数文件
- **删除率**: $(echo "scale=1; $TOTAL_DELETED * 100 / $ORIGINAL_COUNT" | bc -l 2>/dev/null || echo "0")%

EOF

# 清理临时文件
rm -f "/tmp/low_risk_files.txt" "/tmp/medium_risk_files.txt" "/tmp/high_risk_files.txt"

cat >> "$REPORT_FILE" << EOF

## 6. 总结

- **任务状态**: 完成
- **删除文件数**: $TOTAL_DELETED 个
- **删除成功率**: $(echo "scale=1; $TOTAL_DELETED * 100 / $ORIGINAL_COUNT" | bc -l 2>/dev/null || echo "0")%
- **保留文件**: $HIGH_RISK_COUNT 个高风险文件
- **建议**: 继续分析高风险文件的引用情况，逐步迁移

---
*报告生成时间: $(date)*
EOF

echo ""
echo "🎉 任务6完成: 原始工具函数文件删除完成！"
echo "  🗑️ 已删除 $TOTAL_DELETED 个原始工具函数文件"
echo "  📊 删除成功率: $(echo "scale=1; $TOTAL_DELETED * 100 / $ORIGINAL_COUNT" | bc -l 2>/dev/null || echo "0")%"
echo "  ⚠️ 保留 $HIGH_RISK_COUNT 个高风险文件"
echo "  📋 详细报告: $REPORT_FILE"
echo "  📝 日志文件: $LOG_FILE"
