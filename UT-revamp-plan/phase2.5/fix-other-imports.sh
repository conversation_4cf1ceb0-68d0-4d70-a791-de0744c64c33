#!/bin/bash
# 修复其他目录导入脚本

LOG_FILE="UT-revamp-plan/phase3/fix-other-imports-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔧 开始修复其他目录的导入..."
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 获取所有需要修复的其他文件（排除e-invoice和ordering）
OTHER_FILES=$(find src -name "*.js" -o -name "*.jsx" | grep -v "src/e-invoice" | grep -v "src/ordering" | xargs grep -l "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring")

echo "📋 需要修复的其他文件数量: $(echo "$OTHER_FILES" | wc -l)"

echo ""
echo "🔄 开始批量替换..."

# 执行替换
for file in $OTHER_FILES; do
    if [ -f "$file" ]; then
        echo "  🔧 处理文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 计算到common/utils的相对路径
        depth=$(echo "$file" | sed 's|src/||' | tr '/' '\n' | wc -l)
        depth=$((depth - 1))
        
        relative_prefix=""
        for ((i=0; i<depth; i++)); do
            relative_prefix="../$relative_prefix"
        done
        
        common_utils_path="${relative_prefix}common/utils"
        
        # 执行替换 - constants 相关
        sed -i.tmp "s|from '../../utils/constants'|from '${common_utils_path}/constants'|g" "$file"
        sed -i.tmp "s|from '../utils/constants'|from '${common_utils_path}/constants'|g" "$file"
        sed -i.tmp "s|from '../../../utils/constants'|from '${common_utils_path}/constants'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/constants'|from '${common_utils_path}/constants'|g" "$file"
        sed -i.tmp "s|from '../../../../../utils/constants'|from '${common_utils_path}/constants'|g" "$file"
        
        # history 相关
        sed -i.tmp "s|from '../utils/history'|from '${common_utils_path}/system/history'|g" "$file"
        sed -i.tmp "s|from '../../utils/history'|from '${common_utils_path}/system/history'|g" "$file"
        sed -i.tmp "s|from '../../../utils/history'|from '${common_utils_path}/system/history'|g" "$file"
        
        # utils 相关
        sed -i.tmp "s|from '../../utils/utils'|from '${common_utils_path}/system/utils'|g" "$file"
        sed -i.tmp "s|from '../../../utils/utils'|from '${common_utils_path}/system/utils'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/utils'|from '${common_utils_path}/system/utils'|g" "$file"
        
        # API 请求相关
        sed -i.tmp "s|from '../../utils/api-request'|from '${common_utils_path}/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../utils/api-request'|from '${common_utils_path}/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/api-request'|from '${common_utils_path}/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../utils/request'|from '${common_utils_path}/api/request'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/request'|from '${common_utils_path}/api/request'|g" "$file"
        
        # 时间相关
        sed -i.tmp "s|from '../../utils/datetime-lib'|from '${common_utils_path}/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from '../../../utils/datetime-lib'|from '${common_utils_path}/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/datetime-lib'|from '${common_utils_path}/time/datetime-lib'|g" "$file"
        sed -i.tmp "s|from '../../../utils/time-lib'|from '${common_utils_path}/time/time-lib'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/time-lib'|from '${common_utils_path}/time/time-lib'|g" "$file"
        
        # 表单验证相关
        sed -i.tmp "s|from '../../utils/form-validate'|from '${common_utils_path}/validation/form-validate'|g" "$file"
        sed -i.tmp "s|from '../../../utils/form-validate'|from '${common_utils_path}/validation/form-validate'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/form-validate'|from '${common_utils_path}/validation/form-validate'|g" "$file"
        
        # 其他常见工具函数
        sed -i.tmp "s|from '../../../utils/native-methods'|from '${common_utils_path}/system/native-methods'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/native-methods'|from '${common_utils_path}/system/native-methods'|g" "$file"
        sed -i.tmp "s|from '../../../utils/clevertap'|from '${common_utils_path}/analytics/clevertap'|g" "$file"
        sed -i.tmp "s|from '../../../../utils/clevertap'|from '${common_utils_path}/analytics/clevertap'|g" "$file"
        
        # 清理临时文件
        rm -f "$file.tmp"
        
        # 检查是否有变化
        if ! diff -q "$file" "$file.backup" > /dev/null 2>&1; then
            echo "    ✅ 文件已更新"
        else
            echo "    ⚠️ 文件无变化"
        fi
        
        # 删除备份文件
        rm -f "$file.backup"
    fi
done

echo ""
echo "🧪 验证修复结果..."

# 检查是否还有旧路径导入
REMAINING_OLD_IMPORTS=$(find src -name "*.js" -o -name "*.jsx" | grep -v "src/e-invoice" | grep -v "src/ordering" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)

echo "其他目录剩余的旧路径导入: $REMAINING_OLD_IMPORTS 个"

if [ "$REMAINING_OLD_IMPORTS" -gt 0 ]; then
    echo "⚠️ 仍有未处理的导入:"
    find src -name "*.js" -o -name "*.jsx" | grep -v "src/e-invoice" | grep -v "src/ordering" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | head -5
fi

echo ""
echo "📊 总体进展:"
TOTAL_REMAINING=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)
echo "全项目剩余旧路径导入: $TOTAL_REMAINING 个"

echo ""
echo "✅ 其他目录导入修复完成！"
