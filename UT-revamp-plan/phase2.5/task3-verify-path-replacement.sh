#!/bin/bash
# 任务3: 验证导入路径替换完整性脚本

LOG_FILE="UT-revamp-plan/phase3/task3-path-replacement-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔍 任务3: 验证导入路径替换完整性"
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 创建详细验证报告
REPORT_FILE="UT-revamp-plan/phase3/path-replacement-verification-$(date +%Y%m%d-%H%M%S).md"

cat > "$REPORT_FILE" << EOF
# 导入路径替换完整性验证报告
生成时间: $(date)

## 概述
使用增强的grep命令深度搜索，确保没有代码仍在引用旧的工具函数路径。

EOF

echo ""
echo "🔍 1. 深度搜索旧路径引用..."

# 定义需要搜索的旧路径模式
OLD_PATH_PATTERNS=(
    "utils/constants"
    "utils/history"
    "utils/utils"
    "utils/api-request"
    "utils/request"
    "utils/datetime-lib"
    "utils/time-lib"
    "utils/form-validate"
    "utils/clevertap"
    "utils/native-methods"
    "utils/url"
    "utils/ui"
    "utils/geoUtils"
    "utils/modal-back-button-support"
)

cat >> "$REPORT_FILE" << EOF
## 1. 旧路径引用搜索结果

### 搜索模式:
EOF

echo "  📋 搜索以下旧路径模式:"
for pattern in "${OLD_PATH_PATTERNS[@]}"; do
    echo "    - $pattern"
    echo "- $pattern" >> "$REPORT_FILE"
done

cat >> "$REPORT_FILE" << EOF

### 详细搜索结果:
EOF

echo ""
echo "🔍 2. 执行增强搜索..."

TOTAL_FOUND=0

for pattern in "${OLD_PATH_PATTERNS[@]}"; do
    echo "  🔍 搜索模式: $pattern"
    
    # 使用多种grep模式搜索
    # 1. 搜索import语句
    IMPORT_MATCHES=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*$pattern" 2>/dev/null | grep -v "common/utils" | wc -l)
    
    # 2. 搜索require语句
    REQUIRE_MATCHES=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "require.*$pattern" 2>/dev/null | grep -v "common/utils" | wc -l)
    
    # 3. 搜索动态导入
    DYNAMIC_MATCHES=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "import.*$pattern" 2>/dev/null | grep -v "from" | grep -v "common/utils" | wc -l)
    
    # 4. 搜索路径字符串
    PATH_MATCHES=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "'.*$pattern'" 2>/dev/null | grep -v "common/utils" | wc -l)
    
    TOTAL_PATTERN=$((IMPORT_MATCHES + REQUIRE_MATCHES + DYNAMIC_MATCHES + PATH_MATCHES))
    TOTAL_FOUND=$((TOTAL_FOUND + TOTAL_PATTERN))
    
    echo "    import语句: $IMPORT_MATCHES"
    echo "    require语句: $REQUIRE_MATCHES"
    echo "    动态导入: $DYNAMIC_MATCHES"
    echo "    路径字符串: $PATH_MATCHES"
    echo "    小计: $TOTAL_PATTERN"
    
    cat >> "$REPORT_FILE" << EOF

#### $pattern:
- **import语句**: $IMPORT_MATCHES 个
- **require语句**: $REQUIRE_MATCHES 个
- **动态导入**: $DYNAMIC_MATCHES 个
- **路径字符串**: $PATH_MATCHES 个
- **小计**: $TOTAL_PATTERN 个
EOF

    # 如果找到匹配项，显示具体示例
    if [ $TOTAL_PATTERN -gt 0 ]; then
        echo "    📋 示例:"
        cat >> "$REPORT_FILE" << EOF

**示例引用**:
\`\`\`
EOF
        
        # 显示前5个import匹配
        if [ $IMPORT_MATCHES -gt 0 ]; then
            find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*$pattern" 2>/dev/null | grep -v "common/utils" | head -3 | while read line; do
                echo "      $line"
                echo "$line" >> "$REPORT_FILE"
            done
        fi
        
        # 显示前3个路径字符串匹配
        if [ $PATH_MATCHES -gt 0 ]; then
            find src -name "*.js" -o -name "*.jsx" | xargs grep -n "'.*$pattern'" 2>/dev/null | grep -v "common/utils" | head -2 | while read line; do
                echo "      $line"
                echo "$line" >> "$REPORT_FILE"
            done
        fi
        
        echo "\`\`\`" >> "$REPORT_FILE"
    fi
    
    echo ""
done

echo "📊 总计发现: $TOTAL_FOUND 个旧路径引用"

cat >> "$REPORT_FILE" << EOF

## 2. 搜索总结

**总计发现**: $TOTAL_FOUND 个旧路径引用需要处理

EOF

echo ""
echo "🔍 3. 特殊情况检查..."

cat >> "$REPORT_FILE" << EOF
## 3. 特殊情况检查

### 注释中的引用:
EOF

# 检查注释中的旧路径引用
COMMENT_REFS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "//.*utils/" 2>/dev/null | wc -l)
BLOCK_COMMENT_REFS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "/\*.*utils/.*\*/" 2>/dev/null | wc -l)

echo "  注释中的引用: $COMMENT_REFS 个"
echo "  块注释中的引用: $BLOCK_COMMENT_REFS 个"

cat >> "$REPORT_FILE" << EOF
- **单行注释**: $COMMENT_REFS 个引用
- **块注释**: $BLOCK_COMMENT_REFS 个引用

### 配置文件中的引用:
EOF

# 检查配置文件中的引用
CONFIG_FILES=("package.json" "webpack.config.js" "babel.config.js" ".eslintrc.js" "jest.config.js")
CONFIG_REFS=0

echo "  📋 检查配置文件:"
for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        refs=$(grep -n "utils/" "$config_file" 2>/dev/null | wc -l)
        CONFIG_REFS=$((CONFIG_REFS + refs))
        echo "    $config_file: $refs 个引用"
        echo "- **$config_file**: $refs 个引用" >> "$REPORT_FILE"
    fi
done

echo ""
echo "🎯 4. 生成清理建议..."

cat >> "$REPORT_FILE" << EOF

## 4. 清理建议

### 优先级处理顺序:
1. **高优先级**: import/require语句 - 影响代码功能
2. **中优先级**: 路径字符串引用 - 可能影响运行时
3. **低优先级**: 注释中的引用 - 仅影响文档
4. **最低优先级**: 配置文件引用 - 需要谨慎处理

### 建议的处理方法:
EOF

# 生成具体的清理脚本
CLEANUP_SCRIPT="UT-revamp-plan/phase3/cleanup-remaining-refs.sh"
cat > "$CLEANUP_SCRIPT" << 'EOF'
#!/bin/bash
# 清理剩余旧路径引用脚本

echo "🧹 开始清理剩余的旧路径引用..."

# 1. 修复剩余的import语句
echo "1. 修复剩余的import语句..."

# CleverTap相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/clevertap'|from 'common/utils/analytics/clevertap'|g" {} \;

# Utils相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/utils'|from 'common/utils/system/utils'|g" {} \;

# Native methods相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/native-methods'|from 'common/utils/system/native-methods'|g" {} \;

# URL相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/url'|from 'common/utils/system/url'|g" {} \;

# UI相关
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/ui'|from 'common/utils/ui'|g" {} \;

# 2. 清理备份文件
echo "2. 清理备份文件..."
find src -name "*.bak" -delete

echo "✅ 清理完成！"
EOF

chmod +x "$CLEANUP_SCRIPT"

cat >> "$REPORT_FILE" << EOF
1. **使用自动清理脚本**: \`$CLEANUP_SCRIPT\`
2. **手动检查特殊情况**: 复杂的动态导入和配置文件引用
3. **分批处理**: 按模块逐个处理，便于测试验证
4. **验证测试**: 每次修改后运行相关测试

### 自动清理脚本:
已生成自动清理脚本: \`$CLEANUP_SCRIPT\`

使用方法:
\`\`\`bash
./$CLEANUP_SCRIPT
\`\`\`

EOF

echo "✅ 清理脚本已生成: $CLEANUP_SCRIPT"

echo ""
echo "📊 5. 验证新路径使用情况..."

cat >> "$REPORT_FILE" << EOF
## 5. 新路径使用验证

### 新路径导入统计:
EOF

# 统计新路径的使用情况
NEW_PATH_USAGE=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*common/utils" 2>/dev/null | wc -l)
echo "  新路径导入总数: $NEW_PATH_USAGE"

# 按新路径类型统计
NEW_CONSTANTS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*common/utils/constants" 2>/dev/null | wc -l)
NEW_API=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*common/utils/api" 2>/dev/null | wc -l)
NEW_TIME=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*common/utils/time" 2>/dev/null | wc -l)
NEW_VALIDATION=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*common/utils/validation" 2>/dev/null | wc -l)

echo "    constants: $NEW_CONSTANTS"
echo "    api: $NEW_API"
echo "    time: $NEW_TIME"
echo "    validation: $NEW_VALIDATION"

cat >> "$REPORT_FILE" << EOF
- **总计**: $NEW_PATH_USAGE 个新路径导入
- **constants**: $NEW_CONSTANTS 个
- **api**: $NEW_API 个
- **time**: $NEW_TIME 个
- **validation**: $NEW_VALIDATION 个

## 6. 总结

- **旧路径引用**: 还有 $TOTAL_FOUND 个需要处理
- **新路径使用**: 已有 $NEW_PATH_USAGE 个在使用新路径
- **迁移进度**: $(echo "scale=1; $NEW_PATH_USAGE * 100 / ($NEW_PATH_USAGE + $TOTAL_FOUND)" | bc -l)% 已迁移到新路径
- **建议**: 使用生成的清理脚本处理剩余引用

---
*报告生成时间: $(date)*
EOF

echo ""
echo "🎉 任务3完成: 导入路径替换完整性验证完成！"
echo "  📊 发现 $TOTAL_FOUND 个旧路径引用需要处理"
echo "  📊 已有 $NEW_PATH_USAGE 个新路径导入在使用"
echo "  📋 详细报告: $REPORT_FILE"
echo "  🧹 清理脚本: $CLEANUP_SCRIPT"
echo "  📝 日志文件: $LOG_FILE"
