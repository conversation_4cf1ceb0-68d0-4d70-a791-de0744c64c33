#!/bin/bash
# 验证当前状态脚本

LOG_FILE="UT-revamp-plan/phase3/pre-cleanup-check-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔍 开始验证当前状态..."
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

echo "📋 检查旧路径导入引用..."
OLD_IMPORTS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)
echo "发现旧路径导入: $OLD_IMPORTS 个"

if [ "$OLD_IMPORTS" -gt 0 ]; then
    echo "⚠️ 发现以下旧路径导入:"
    find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | head -10
    echo "..."
fi

echo "🔗 验证新路径导入..."
BROKEN_IMPORTS=0

# 检查常见的新路径导入
NEW_PATHS=(
    "common/utils/constants"
    "common/utils/api/request"
    "common/utils/time/time-lib"
    "common/utils/validation/form-validate"
)

for path in "${NEW_PATHS[@]}"; do
    echo "  检查路径: $path"
    if [ ! -f "src/$path/index.js" ] && [ ! -f "src/$path.js" ]; then
        echo "    ❌ 路径不存在: src/$path"
        ((BROKEN_IMPORTS++))
    else
        echo "    ✅ 路径正常"
    fi
done

echo "断开的导入路径: $BROKEN_IMPORTS 个"

echo "🧪 运行完整验证套件..."

echo "📋 ESLint 检查..."
if yarn eslint --quiet; then
    echo "✅ ESLint 通过"
else
    echo "❌ ESLint 失败"
    exit 1
fi

echo "🧪 测试套件..."
if yarn test --watchAll=false --passWithNoTests; then
    echo "✅ 测试通过"
else
    echo "❌ 测试失败"
    exit 1
fi

echo "🚀 编译检查..."
if timeout 120 yarn start --verify-only; then
    echo "✅ 编译通过"
else
    echo "❌ 编译失败或超时"
    exit 1
fi

echo "🎉 当前状态验证完成!"
echo "  - 旧路径导入: $OLD_IMPORTS 个"
echo "  - 断开导入: $BROKEN_IMPORTS 个"
echo "  - ESLint: 通过"
echo "  - 测试: 通过"
echo "  - 编译: 通过"
