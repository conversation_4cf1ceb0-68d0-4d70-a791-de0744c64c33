#!/bin/bash
# 分析旧路径导入分布脚本

LOG_FILE="UT-revamp-plan/phase3/old-imports-analysis-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔍 开始分析旧路径导入分布..."
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 创建临时文件存储所有旧路径导入
TEMP_FILE=$(mktemp)
find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" > "$TEMP_FILE"

echo "📊 总体统计:"
TOTAL_COUNT=$(wc -l < "$TEMP_FILE")
echo "  总计旧路径导入: $TOTAL_COUNT 个"

echo ""
echo "📁 按目录分布:"
echo "===================="

# 按目录统计
echo "🔸 e-invoice 目录:"
grep "^src/e-invoice" "$TEMP_FILE" | wc -l | xargs echo "  数量:"
echo "  示例:"
grep "^src/e-invoice" "$TEMP_FILE" | head -3 | sed 's/^/    /'

echo ""
echo "🔸 ordering 目录:"
grep "^src/ordering" "$TEMP_FILE" | wc -l | xargs echo "  数量:"
echo "  示例:"
grep "^src/ordering" "$TEMP_FILE" | head -3 | sed 's/^/    /'

echo ""
echo "🔸 其他目录:"
grep -v "^src/e-invoice" "$TEMP_FILE" | grep -v "^src/ordering" | wc -l | xargs echo "  数量:"
echo "  示例:"
grep -v "^src/e-invoice" "$TEMP_FILE" | grep -v "^src/ordering" | head -3 | sed 's/^/    /'

echo ""
echo "📋 按导入类型分布:"
echo "===================="

echo "🔸 constants 导入:"
grep "from.*utils/constants" "$TEMP_FILE" | wc -l | xargs echo "  数量:"

echo "🔸 history 导入:"
grep "from.*utils/history" "$TEMP_FILE" | wc -l | xargs echo "  数量:"

echo "🔸 其他工具函数导入:"
grep -v "from.*utils/constants" "$TEMP_FILE" | grep -v "from.*utils/history" | wc -l | xargs echo "  数量:"

echo ""
echo "🎯 需要处理的文件列表:"
echo "===================="

# 按目录分组显示需要处理的文件
echo "📁 e-invoice 目录文件:"
grep "^src/e-invoice" "$TEMP_FILE" | cut -d: -f1 | sort -u | sed 's/^/  - /'

echo ""
echo "📁 ordering 目录文件:"
grep "^src/ordering" "$TEMP_FILE" | cut -d: -f1 | sort -u | sed 's/^/  - /'

echo ""
echo "📁 其他目录文件:"
grep -v "^src/e-invoice" "$TEMP_FILE" | grep -v "^src/ordering" | cut -d: -f1 | sort -u | sed 's/^/  - /'

# 清理临时文件
rm -f "$TEMP_FILE"

echo ""
echo "✅ 分析完成！详细信息已保存到: $LOG_FILE"
