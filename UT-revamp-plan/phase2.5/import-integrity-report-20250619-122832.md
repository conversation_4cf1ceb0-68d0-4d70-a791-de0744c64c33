# 导入完整性检查报告
生成时间: Thu Jun 19 12:28:32 CST 2025

## 概述
本报告详细分析了当前代码库中的导入路径状态，检查Phase 2.5导入路径替换的完整性。

## 1. 旧路径导入统计

**总计**:      340 个旧路径导入仍需处理

### 按目录分布:
- **e-invoice**:       44 个
- **ordering**:      133 个
- **cashback**:       12 个
- **rewards**:       67 个
- **site**:       12 个
- **user**:        4 个
- **common**:       41 个
- **stores**:       10 个
- **voucher**:        1 个

### 按导入类型分布:
- **constants**:       46 个
- **history**:        1 个
- **api/request**:        3 个
- **time相关**:       17 个
- **form相关**:        0 个
- **其他**: 273 个

## 2. 新路径导入验证

### 新路径结构检查:
- ✅ **src/common/utils/constants** - 存在
- ❌ **src/common/utils/system/history** - 不存在
- ❌ **src/common/utils/system/utils** - 不存在
- ✅ **src/common/utils/api/request** - 存在
- ✅ **src/common/utils/time/datetime-lib** - 存在
- ✅ **src/common/utils/time/time-lib** - 存在
- ✅ **src/common/utils/validation/form-validate** - 存在

### 导入引用完整性:
- **from.*common/utils/constants**:      268 个引用
- **from.*common/utils/system/history**:        4 个引用
- **from.*common/utils/api/request**:       51 个引用
- **from.*common/utils/time**:       18 个引用
- **from.*common/utils/validation**:        2 个引用

## 3. 问题导入识别

### 需要手动处理的导入:
- 26 import CleverTap from '../../../../../utils/clevertap';
- 20 import Utils from '../../../../../utils/utils';
- 14 import { goBack as nativeGoBack } from '../../../../../../utils/native-methods';
- 13 import CleverTap from '../../../../../../utils/clevertap';
- 11 import CleverTap from '../../../../../../../utils/clevertap';
- 8 import Url from '../../../utils/url';
- 6 import Clevertap from '../../../../../utils/clevertap';
- 6 import * as NativeMethods from '../../../../../utils/native-methods';
- 6 import { getClassName } from '../../utils/ui';
- 6 import { closeWebView } from '../../../../../utils/native-methods';

## 4. 修复建议

### 优先级排序:
1. **高优先级**: constants导入 (      46 个) - 影响最广泛
2. **中优先级**: api/request导入 (       3 个) - 功能关键
3. **中优先级**: time相关导入 (      17 个) - 使用频繁
4. **低优先级**: history导入 (       1 个) - 影响较小
5. **低优先级**: form相关导入 (       0 个) - 局部影响

### 建议的修复策略:
1. **批量替换**: 使用脚本批量处理相同模式的导入
2. **手动修复**: 处理特殊情况和复杂依赖
3. **分模块处理**: 按目录逐个处理，便于测试验证
4. **渐进式修复**: 先修复影响最大的导入类型


### 自动修复脚本:
已生成自动修复脚本: `UT-revamp-plan/phase3/suggested-fixes.sh`

使用方法:
```bash
./UT-revamp-plan/phase3/suggested-fixes.sh
```

**注意**: 运行前请确保已备份代码！


## 5. 总结

- **当前状态**: 还有      340 个旧路径导入需要处理
- **主要问题**: constants导入 (      46 个) 是最大的问题
- **建议**: 使用生成的自动修复脚本进行批量处理
- **验证**: 修复后需要运行完整的测试套件

---
*报告生成时间: Thu Jun 19 12:28:34 CST 2025*
