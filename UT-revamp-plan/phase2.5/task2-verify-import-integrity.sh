#!/bin/bash
# 任务2: 验证导入完整性检查脚本

LOG_FILE="UT-revamp-plan/phase3/task2-import-integrity-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🔍 任务2: 验证导入完整性检查"
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 创建详细报告文件
REPORT_FILE="UT-revamp-plan/phase3/import-integrity-report-$(date +%Y%m%d-%H%M%S).md"

cat > "$REPORT_FILE" << EOF
# 导入完整性检查报告
生成时间: $(date)

## 概述
本报告详细分析了当前代码库中的导入路径状态，检查Phase 2.5导入路径替换的完整性。

EOF

echo ""
echo "📊 1. 统计旧路径导入..."

# 统计所有旧路径导入
OLD_IMPORTS_TOTAL=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)
echo "发现旧路径导入总数: $OLD_IMPORTS_TOTAL"

cat >> "$REPORT_FILE" << EOF
## 1. 旧路径导入统计

**总计**: $OLD_IMPORTS_TOTAL 个旧路径导入仍需处理

### 按目录分布:
EOF

# 按目录统计
echo "  📁 按目录分布:"
for dir in e-invoice ordering cashback rewards site user common stores voucher; do
    if [ -d "src/$dir" ]; then
        count=$(find "src/$dir" -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | wc -l)
        echo "    $dir: $count 个"
        echo "- **$dir**: $count 个" >> "$REPORT_FILE"
    fi
done

echo ""
echo "📋 2. 按导入类型分析..."

cat >> "$REPORT_FILE" << EOF

### 按导入类型分布:
EOF

# 按导入类型统计
CONSTANTS_COUNT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/constants" 2>/dev/null | grep -v "common/utils" | wc -l)
HISTORY_COUNT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/history" 2>/dev/null | grep -v "common/utils" | wc -l)
API_COUNT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/.*request" 2>/dev/null | grep -v "common/utils" | wc -l)
TIME_COUNT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/.*time" 2>/dev/null | grep -v "common/utils" | wc -l)
FORM_COUNT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/form" 2>/dev/null | grep -v "common/utils" | wc -l)
OTHER_COUNT=$((OLD_IMPORTS_TOTAL - CONSTANTS_COUNT - HISTORY_COUNT - API_COUNT - TIME_COUNT - FORM_COUNT))

echo "  constants: $CONSTANTS_COUNT"
echo "  history: $HISTORY_COUNT"
echo "  api/request: $API_COUNT"
echo "  time相关: $TIME_COUNT"
echo "  form相关: $FORM_COUNT"
echo "  其他: $OTHER_COUNT"

cat >> "$REPORT_FILE" << EOF
- **constants**: $CONSTANTS_COUNT 个
- **history**: $HISTORY_COUNT 个
- **api/request**: $API_COUNT 个
- **time相关**: $TIME_COUNT 个
- **form相关**: $FORM_COUNT 个
- **其他**: $OTHER_COUNT 个

EOF

echo ""
echo "🔍 3. 检查新路径导入状态..."

cat >> "$REPORT_FILE" << EOF
## 2. 新路径导入验证

### 新路径结构检查:
EOF

# 检查新路径结构
NEW_PATHS=(
    "src/common/utils/constants"
    "src/common/utils/system/history"
    "src/common/utils/system/utils"
    "src/common/utils/api/request"
    "src/common/utils/time/datetime-lib"
    "src/common/utils/time/time-lib"
    "src/common/utils/validation/form-validate"
)

echo "  📁 新路径结构验证:"
for path in "${NEW_PATHS[@]}"; do
    if [ -f "$path/index.js" ] || [ -f "$path.js" ]; then
        echo "    ✅ $path - 存在"
        echo "- ✅ **$path** - 存在" >> "$REPORT_FILE"
    else
        echo "    ❌ $path - 不存在"
        echo "- ❌ **$path** - 不存在" >> "$REPORT_FILE"
    fi
done

echo ""
echo "🧪 4. 检查导入引用完整性..."

cat >> "$REPORT_FILE" << EOF

### 导入引用完整性:
EOF

# 检查是否有断开的导入
BROKEN_IMPORTS=0
echo "  🔗 检查断开的导入引用:"

# 检查常见的新路径导入是否有效
NEW_IMPORT_PATTERNS=(
    "from.*common/utils/constants"
    "from.*common/utils/system/history"
    "from.*common/utils/api/request"
    "from.*common/utils/time"
    "from.*common/utils/validation"
)

for pattern in "${NEW_IMPORT_PATTERNS[@]}"; do
    count=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "$pattern" 2>/dev/null | wc -l)
    echo "    $pattern: $count 个引用"
    echo "- **$pattern**: $count 个引用" >> "$REPORT_FILE"
done

echo ""
echo "⚠️ 5. 识别问题导入..."

cat >> "$REPORT_FILE" << EOF

## 3. 问题导入识别

### 需要手动处理的导入:
EOF

# 找出最常见的问题导入
echo "  🚨 最常见的问题导入 (前10个):"
PROBLEM_IMPORTS=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -n "from.*utils/" 2>/dev/null | grep -v "common/utils" | grep -v "monitoring" | cut -d: -f3- | sort | uniq -c | sort -nr | head -10)

echo "$PROBLEM_IMPORTS" | while read line; do
    echo "    $line"
    echo "- $line" >> "$REPORT_FILE"
done

echo ""
echo "📁 6. 生成修复建议..."

cat >> "$REPORT_FILE" << EOF

## 4. 修复建议

### 优先级排序:
1. **高优先级**: constants导入 ($CONSTANTS_COUNT 个) - 影响最广泛
2. **中优先级**: api/request导入 ($API_COUNT 个) - 功能关键
3. **中优先级**: time相关导入 ($TIME_COUNT 个) - 使用频繁
4. **低优先级**: history导入 ($HISTORY_COUNT 个) - 影响较小
5. **低优先级**: form相关导入 ($FORM_COUNT 个) - 局部影响

### 建议的修复策略:
1. **批量替换**: 使用脚本批量处理相同模式的导入
2. **手动修复**: 处理特殊情况和复杂依赖
3. **分模块处理**: 按目录逐个处理，便于测试验证
4. **渐进式修复**: 先修复影响最大的导入类型

EOF

echo ""
echo "🎯 7. 生成具体修复命令..."

# 生成修复脚本
FIX_SCRIPT="UT-revamp-plan/phase3/suggested-fixes.sh"
cat > "$FIX_SCRIPT" << 'EOF'
#!/bin/bash
# 建议的修复命令

echo "🔧 执行建议的导入路径修复..."

# 1. 修复constants导入
echo "1. 修复constants导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/constants'|from 'common/utils/constants'|g" {} \;

# 2. 修复api-request导入
echo "2. 修复api-request导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/api-request'|from 'common/utils/api/request'|g" {} \;
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/request'|from 'common/utils/api/request'|g" {} \;

# 3. 修复time相关导入
echo "3. 修复time相关导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/datetime-lib'|from 'common/utils/time/datetime-lib'|g" {} \;
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/time-lib'|from 'common/utils/time/time-lib'|g" {} \;

# 4. 修复history导入
echo "4. 修复history导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/history'|from 'common/utils/system/history'|g" {} \;

# 5. 修复form相关导入
echo "5. 修复form相关导入..."
find src -name "*.js" -o -name "*.jsx" -exec sed -i.bak "s|from '[^']*utils/form-validate'|from 'common/utils/validation/form-validate'|g" {} \;

# 清理备份文件
echo "清理备份文件..."
find src -name "*.bak" -delete

echo "✅ 修复完成！请运行测试验证。"
EOF

chmod +x "$FIX_SCRIPT"

cat >> "$REPORT_FILE" << EOF

### 自动修复脚本:
已生成自动修复脚本: \`$FIX_SCRIPT\`

使用方法:
\`\`\`bash
./$FIX_SCRIPT
\`\`\`

**注意**: 运行前请确保已备份代码！

EOF

echo "✅ 修复脚本已生成: $FIX_SCRIPT"

echo ""
echo "📊 8. 总结..."

cat >> "$REPORT_FILE" << EOF

## 5. 总结

- **当前状态**: 还有 $OLD_IMPORTS_TOTAL 个旧路径导入需要处理
- **主要问题**: constants导入 ($CONSTANTS_COUNT 个) 是最大的问题
- **建议**: 使用生成的自动修复脚本进行批量处理
- **验证**: 修复后需要运行完整的测试套件

---
*报告生成时间: $(date)*
EOF

echo "🎉 任务2完成: 导入完整性检查完成！"
echo "  📊 发现 $OLD_IMPORTS_TOTAL 个旧路径导入需要处理"
echo "  📋 详细报告: $REPORT_FILE"
echo "  🔧 修复脚本: $FIX_SCRIPT"
echo "  📝 日志文件: $LOG_FILE"
