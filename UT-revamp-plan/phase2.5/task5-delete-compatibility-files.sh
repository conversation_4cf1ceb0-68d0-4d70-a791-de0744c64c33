#!/bin/bash
# 任务5: 删除兼容层文件脚本

LOG_FILE="UT-revamp-plan/phase3/task5-delete-compatibility-$(date +%Y%m%d-%H%M%S).log"
exec 1> >(tee -a "$LOG_FILE")
exec 2> >(tee -a "$LOG_FILE" >&2)

echo "🗑️ 任务5: 删除兼容层文件"
echo "时间: $(date)"
echo "日志文件: $LOG_FILE"

# 创建删除报告
REPORT_FILE="UT-revamp-plan/phase3/compatibility-files-deletion-$(date +%Y%m%d-%H%M%S).md"

cat > "$REPORT_FILE" << EOF
# 兼容层文件删除报告
生成时间: $(date)

## 概述
按依赖关系安全删除兼容层文件，叶子节点优先，确保不破坏现有功能。

EOF

echo ""
echo "🔍 1. 识别兼容层文件..."

# 识别兼容层文件的特征
# 1. 文件内容包含 "// Compatibility layer" 或类似注释
# 2. 文件主要是重新导出其他文件的内容
# 3. 文件名包含 "compat" 或在已知的兼容层位置

echo "  📋 搜索兼容层文件特征..."

# 搜索包含兼容层标识的文件
COMPAT_FILES_BY_COMMENT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "Compatibility\|compatibility\|COMPATIBILITY" 2>/dev/null | grep -v node_modules || true)
COMPAT_COUNT_COMMENT=$(echo "$COMPAT_FILES_BY_COMMENT" | grep -v "^$" | wc -l)

# 搜索主要是重新导出的文件（包含大量export语句但很少其他代码）
REEXPORT_FILES=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "^export.*from" 2>/dev/null | head -20 || true)

# 搜索已知的兼容层位置
KNOWN_COMPAT_LOCATIONS=(
    "src/utils/datetime-lib.js"
    "src/utils/time-lib.js"
    "src/utils/form-validate.js"
    "src/utils/api-request.js"
    "src/utils/request.js"
)

echo "发现兼容层文件候选:"
echo "  通过注释识别: $COMPAT_COUNT_COMMENT 个"

cat >> "$REPORT_FILE" << EOF
## 1. 兼容层文件识别

### 识别方法:
1. **注释标识**: 搜索包含 "Compatibility" 关键词的文件
2. **重新导出模式**: 主要包含 export...from 语句的文件
3. **已知位置**: 根据迁移记录识别的兼容层文件

### 识别结果:
- **通过注释识别**: $COMPAT_COUNT_COMMENT 个文件
EOF

if [ $COMPAT_COUNT_COMMENT -gt 0 ]; then
    echo "    文件列表:"
    echo "$COMPAT_FILES_BY_COMMENT" | while read file; do
        if [ -n "$file" ]; then
            echo "      - $file"
            echo "  - $file" >> "$REPORT_FILE"
        fi
    done
fi

echo ""
echo "🔍 2. 分析已知兼容层文件..."

cat >> "$REPORT_FILE" << EOF

- **已知兼容层位置**:
EOF

EXISTING_COMPAT_FILES=()
for file in "${KNOWN_COMPAT_LOCATIONS[@]}"; do
    if [ -f "$file" ]; then
        EXISTING_COMPAT_FILES+=("$file")
        echo "    ✅ $file - 存在"
        echo "  - ✅ $file - 存在" >> "$REPORT_FILE"
    else
        echo "    ❌ $file - 不存在"
        echo "  - ❌ $file - 不存在" >> "$REPORT_FILE"
    fi
done

EXISTING_COUNT=${#EXISTING_COMPAT_FILES[@]}
echo "发现已知兼容层文件: $EXISTING_COUNT 个"

echo ""
echo "🔍 3. 检查文件依赖关系..."

cat >> "$REPORT_FILE" << EOF

## 2. 依赖关系分析

### 兼容层文件使用情况:
EOF

# 检查每个兼容层文件的使用情况
echo "  📊 检查兼容层文件使用情况:"
for file in "${EXISTING_COMPAT_FILES[@]}"; do
    if [ -f "$file" ]; then
        # 提取文件名（不含路径和扩展名）
        filename=$(basename "$file" .js)
        filepath=$(dirname "$file")
        
        # 搜索对该文件的导入引用
        import_count=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$filename" 2>/dev/null | grep -v "$file" | wc -l)
        
        echo "    $file: $import_count 个引用"
        echo "- **$file**: $import_count 个引用" >> "$REPORT_FILE"
        
        if [ $import_count -gt 0 ]; then
            echo "      引用文件:"
            find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$filename" 2>/dev/null | grep -v "$file" | head -3 | while read ref_file; do
                echo "        - $ref_file"
            done
        fi
    fi
done

echo ""
echo "🎯 4. 确定安全删除顺序..."

cat >> "$REPORT_FILE" << EOF

## 3. 删除策略

### 安全删除原则:
1. **叶子节点优先**: 先删除没有被其他文件引用的兼容层文件
2. **逐步删除**: 每删除一批文件后验证系统状态
3. **保留关键文件**: 暂时保留被大量引用的兼容层文件

### 删除顺序:
EOF

# 按引用数量排序，优先删除引用少的文件
echo "  📋 按引用数量排序（叶子节点优先）:"

DELETION_CANDIDATES=()
LOW_RISK_FILES=()
MEDIUM_RISK_FILES=()
HIGH_RISK_FILES=()

for file in "${EXISTING_COMPAT_FILES[@]}"; do
    if [ -f "$file" ]; then
        filename=$(basename "$file" .js)
        import_count=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$filename" 2>/dev/null | grep -v "$file" | wc -l)
        
        if [ $import_count -eq 0 ]; then
            LOW_RISK_FILES+=("$file")
            echo "    🟢 低风险: $file (0 个引用)"
        elif [ $import_count -le 2 ]; then
            MEDIUM_RISK_FILES+=("$file")
            echo "    🟡 中风险: $file ($import_count 个引用)"
        else
            HIGH_RISK_FILES+=("$file")
            echo "    🔴 高风险: $file ($import_count 个引用)"
        fi
    fi
done

cat >> "$REPORT_FILE" << EOF
#### 第一批（低风险）- 无引用文件: ${#LOW_RISK_FILES[@]} 个
EOF

for file in "${LOW_RISK_FILES[@]}"; do
    echo "- $file" >> "$REPORT_FILE"
done

cat >> "$REPORT_FILE" << EOF

#### 第二批（中风险）- 少量引用文件: ${#MEDIUM_RISK_FILES[@]} 个
EOF

for file in "${MEDIUM_RISK_FILES[@]}"; do
    echo "- $file" >> "$REPORT_FILE"
done

cat >> "$REPORT_FILE" << EOF

#### 第三批（高风险）- 大量引用文件: ${#HIGH_RISK_FILES[@]} 个
EOF

for file in "${HIGH_RISK_FILES[@]}"; do
    echo "- $file" >> "$REPORT_FILE"
done

echo ""
echo "🗑️ 5. 执行安全删除..."

cat >> "$REPORT_FILE" << EOF

## 4. 删除执行记录

EOF

TOTAL_DELETED=0

# 删除第一批（低风险）
if [ ${#LOW_RISK_FILES[@]} -gt 0 ]; then
    echo "  🗑️ 删除第一批（低风险）文件..."
    cat >> "$REPORT_FILE" << EOF
### 第一批删除（低风险）:
EOF
    
    for file in "${LOW_RISK_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
            TOTAL_DELETED=$((TOTAL_DELETED + 1))
        fi
    done
    
    echo "  ✅ 第一批删除完成: ${#LOW_RISK_FILES[@]} 个文件"
fi

# 删除第二批（中风险）- 需要更谨慎
if [ ${#MEDIUM_RISK_FILES[@]} -gt 0 ]; then
    echo "  🗑️ 删除第二批（中风险）文件..."
    cat >> "$REPORT_FILE" << EOF

### 第二批删除（中风险）:
EOF
    
    for file in "${MEDIUM_RISK_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "    删除: $file"
            echo "- ✅ 删除: $file" >> "$REPORT_FILE"
            rm -f "$file"
            TOTAL_DELETED=$((TOTAL_DELETED + 1))
        fi
    done
    
    echo "  ✅ 第二批删除完成: ${#MEDIUM_RISK_FILES[@]} 个文件"
fi

# 第三批（高风险）暂时保留
if [ ${#HIGH_RISK_FILES[@]} -gt 0 ]; then
    echo "  ⚠️ 第三批（高风险）文件暂时保留，需要进一步分析"
    cat >> "$REPORT_FILE" << EOF

### 第三批（高风险）- 暂时保留:
EOF
    
    for file in "${HIGH_RISK_FILES[@]}"; do
        echo "    保留: $file"
        echo "- ⚠️ 保留: $file（需要进一步分析引用）" >> "$REPORT_FILE"
    done
fi

echo ""
echo "🧪 6. 验证删除结果..."

# 验证删除结果
REMAINING_COMPAT=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "Compatibility\|compatibility" 2>/dev/null | wc -l)

echo "  删除前: $EXISTING_COUNT 个兼容层文件"
echo "  删除后: $REMAINING_COMPAT 个兼容层文件"
echo "  已删除: $TOTAL_DELETED 个兼容层文件"

cat >> "$REPORT_FILE" << EOF

## 5. 删除结果验证

- **删除前**: $EXISTING_COUNT 个兼容层文件
- **删除后**: $REMAINING_COMPAT 个兼容层文件
- **已删除**: $TOTAL_DELETED 个兼容层文件
- **删除率**: $(echo "scale=1; $TOTAL_DELETED * 100 / $EXISTING_COUNT" | bc -l 2>/dev/null || echo "0")%

EOF

# 检查是否有断开的引用
echo "  🔍 检查断开的引用..."
BROKEN_IMPORTS=0

for file in "${LOW_RISK_FILES[@]}" "${MEDIUM_RISK_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        filename=$(basename "$file" .js)
        broken_refs=$(find src -name "*.js" -o -name "*.jsx" | xargs grep -l "from.*$filename" 2>/dev/null | wc -l)
        if [ $broken_refs -gt 0 ]; then
            BROKEN_IMPORTS=$((BROKEN_IMPORTS + broken_refs))
            echo "    ⚠️ $file 删除后有 $broken_refs 个断开的引用"
        fi
    fi
done

if [ $BROKEN_IMPORTS -gt 0 ]; then
    echo "  ⚠️ 发现 $BROKEN_IMPORTS 个断开的引用，需要修复"
    echo "- ⚠️ **断开的引用**: $BROKEN_IMPORTS 个需要修复" >> "$REPORT_FILE"
else
    echo "  ✅ 没有发现断开的引用"
    echo "- ✅ **断开的引用**: 无" >> "$REPORT_FILE"
fi

cat >> "$REPORT_FILE" << EOF

## 6. 总结

- **任务状态**: 完成
- **删除文件数**: $TOTAL_DELETED 个
- **删除成功率**: $(echo "scale=1; $TOTAL_DELETED * 100 / $EXISTING_COUNT" | bc -l 2>/dev/null || echo "0")%
- **保留文件**: ${#HIGH_RISK_FILES[@]} 个高风险文件
- **断开引用**: $BROKEN_IMPORTS 个需要修复

### 后续建议:
1. 修复断开的引用
2. 分析高风险文件的引用情况
3. 逐步处理剩余的兼容层文件

---
*报告生成时间: $(date)*
EOF

echo ""
echo "🎉 任务5完成: 兼容层文件删除完成！"
echo "  🗑️ 已删除 $TOTAL_DELETED 个兼容层文件"
echo "  📊 删除成功率: $(echo "scale=1; $TOTAL_DELETED * 100 / $EXISTING_COUNT" | bc -l 2>/dev/null || echo "0")%"
echo "  ⚠️ 保留 ${#HIGH_RISK_FILES[@]} 个高风险文件"
echo "  📋 详细报告: $REPORT_FILE"
echo "  📝 日志文件: $LOG_FILE"
