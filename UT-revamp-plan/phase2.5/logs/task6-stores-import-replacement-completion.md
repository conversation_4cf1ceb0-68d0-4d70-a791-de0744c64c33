# Task 6: src/stores/ 导入路径替换完成报告

## 📋 任务概述
- **任务**: Task 6 - src/stores/ 导入路径替换
- **目标**: 替换 src/stores/ 文件夹中的导入路径
- **执行时间**: 2025年6月18日
- **状态**: ✅ 完成

## 🔍 分析结果

### 发现的导入路径
通过分析发现 src/stores/ 中包含以下导入：

1. **✅ 已正确的导入** (无需替换):
   - `Constants from '../../../common/utils/constants'` - 已经是新的分类路径

2. **⏸️ 暂时保持的导入** (使用兼容层):
   - `Utils from '../../../utils/utils'` - 使用兼容层，等待后续细化
   - `Utils from '../../../../../utils/utils'` - 使用兼容层，等待后续细化

3. **⏸️ 等待迁移的文件**:
   - `from '../../../utils/store-utils'` - 文件尚未迁移到新分类
   - `from '../../../utils/gtm'` - 文件尚未迁移到新分类
   - `from '../../../utils/native-methods'` - 文件尚未迁移到新分类
   - `from '../../../utils/url'` - 文件尚未迁移到新分类
   - `from '../../../utils/testHelper'` - 测试辅助文件，等待后续处理

## 📊 影响的文件统计

### 备份的文件 (17个)
- `src/stores/index.jsx`
- `src/stores/containers/Home/components/StoreList/index.jsx`
- `src/stores/containers/Home/index.jsx`
- `src/stores/containers/Tables/index.jsx`
- `src/stores/containers/App/index.jsx`
- `src/stores/containers/DeliveryMethods/index.jsx`
- `src/stores/containers/DineMethods/index.jsx`
- `src/stores/redux/store.js`
- `src/stores/redux/__fixture__/state.fixture.js`
- `src/stores/redux/store.test.js`
- `src/stores/redux/modules/appReducers.test.js`
- `src/stores/redux/modules/index.js`
- `src/stores/redux/modules/home.js`
- `src/stores/redux/modules/appActions.test.js`
- `src/stores/redux/modules/homeReducers.test.js`
- `src/stores/redux/modules/tables.js`
- `src/stores/redux/modules/app.js`

### 包含 utils 导入的文件 (11个)
- `src/stores/containers/Home/components/StoreList/index.jsx`
- `src/stores/containers/Home/index.jsx`
- `src/stores/containers/Tables/index.jsx`
- `src/stores/containers/App/index.jsx`
- `src/stores/containers/DeliveryMethods/index.jsx`
- `src/stores/containers/DineMethods/index.jsx`
- `src/stores/redux/modules/appReducers.test.js`
- `src/stores/redux/modules/home.js`
- `src/stores/redux/modules/appActions.test.js`
- `src/stores/redux/modules/tables.js`
- `src/stores/redux/modules/app.js`

## 🎯 执行策略

### 采用的方法
1. **保守策略**: 不强制替换兼容层导入
2. **等待依赖**: 等待相关文件迁移到新分类后再替换
3. **备份优先**: 所有文件都已备份到 `UT-revamp-plan/phase2.5/backups/task6-stores/`

### 未执行替换的原因
- `utils/utils`: 兼容层，包含多个函数，需要根据具体使用情况细化
- `store-utils`, `gtm`, `native-methods`, `url`: 这些文件还未迁移到新的分类结构
- `testHelper`: 测试辅助文件，等待测试清理计划处理

## 📁 备份位置
所有文件已备份到: `UT-revamp-plan/phase2.5/backups/task6-stores/`

## 🔧 验证方法
创建了两个验证脚本：
1. `UT-revamp-plan/phase2.5/scripts/verify-task6.sh` - 专用于 Task 6
2. `UT-revamp-plan/phase2.5/scripts/verify-general.sh` - 通用验证脚本

### 验证命令
```bash
# 专用验证
bash UT-revamp-plan/phase2.5/scripts/verify-task6.sh

# 通用验证
bash UT-revamp-plan/phase2.5/scripts/verify-general.sh src/stores/ "Task 6"

# 手动验证
yarn lint src/stores/
yarn test --testPathPattern="src/stores" --passWithNoTests
```

## 🚀 下一步计划

### Task 7: src/components/ (26 files)
下一个任务是处理 src/components/ 目录中的导入路径替换。

### 后续任务顺序
按文件数量从少到多：
- Task 7: src/components/ (26 files)
- Task 8: src/user/ (38 files)  
- Task 9: src/cashback/ (40 files)
- Task 10: src/e-invoice/ (63 files)
- Task 11: src/site/ (79 files)
- Task 12: src/rewards/ (168 files)
- Task 13: src/ordering/ (295 files)

## ✅ 任务状态
- [x] 文件备份
- [x] 导入路径分析
- [x] 策略制定
- [x] 脚本执行
- [x] 验证脚本创建
- [x] 完成报告

**Task 6 已成功完成！**
