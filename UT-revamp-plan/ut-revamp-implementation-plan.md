# 单元测试清理、迁移与新架构实施计划大纲

## 1. 准备阶段

1. **项目分析**
   - 分析现有测试结构和覆盖率
   - 识别需要保留、迁移和清理的测试
   - 确定工具函数的依赖关系

2. **制定详细计划**
   - 创建测试清理清单
   - 制定工具函数迁移路线图
   - 设计新的测试架构标准

3. **建立基线**
   - 记录当前测试状态和覆盖率
   - 备份关键文件和测试

## 2. 清理阶段

1. **执行测试清理**
   - 删除不需要的Redux相关测试
   - 删除UI组件测试
   - 删除快照测试和辅助工具

2. **验证清理结果**
   - 确保保留的测试能正常运行
   - 修复清理过程中出现的问题
   - 验证应用能正常编译和启动(`yarn start`)
   - 更新测试配置文件

## 3. 迁移阶段

1. **工具函数迁移**
   - 按优先级迁移工具函数（高→中→低）
   - 更新测试文件的导入路径
   - 创建兼容层保持向后兼容

2. **验证迁移结果**
   - 确保迁移后的函数行为一致
   - 验证测试是否正常运行
   - 检查应用功能是否正常(`yarn start`验证)
   - 确保无编译错误和运行时错误

3. **更新导入路径**
   - 批量更新代码中的导入路径
   - 验证更新后的导入路径
   - 处理特殊情况和例外

## 4. 新测试架构实施

1. **建立测试标准**
   - 定义工具函数、选择器和thunk的测试标准
   - 创建测试模板和示例
   - 设置覆盖率目标

2. **Demo案例实施**
   - 公共方法案例：完成common/utils的测试
   - 业务逻辑案例：完成rewards/Business/MembershipForm的测试
   - 验证和调整测试标准

## 5. 分阶段实施计划

1. **阶段划分策略**
   - 按src下的实际文件夹结构划分实施阶段
   - 优先处理核心业务模块
   - 考虑模块间的依赖关系

2. **阶段前准备工作**
   - 分析当前阶段模块的代码结构
   - 识别需要测试的工具函数和业务逻辑
   - 创建自然语言测试用例清单，重点关注非UI、非Redux的逻辑

3. **阶段一：公共工具模块**
   - 完成src/common/utils目录下所有模块的测试（已在Demo中完成）
   - 审查自然语言测试用例
   - 实现测试代码
   - 验证测试覆盖率
   - 验证应用编译和启动正常(`yarn start`)

4. **阶段二：订单相关模块**
   - 完成src/ordering目录下的工具函数和业务逻辑测试
     - ordering/utils
     - ordering/services（如果存在）
     - ordering/containers/payments/utils（已在评估清单中）
   - 审查自然语言测试用例
   - 实现测试代码
   - 验证测试覆盖率
   - 验证应用编译和启动正常(`yarn start`)

5. **阶段三：电子发票模块**
   - 完成src/e-invoice目录下的工具函数和业务逻辑测试
     - e-invoice/utils（已在评估清单中）
     - e-invoice/services（如果存在）
   - 审查自然语言测试用例
   - 实现测试代码
   - 验证测试覆盖率
   - 验证应用编译和启动正常(`yarn start`)

6. **阶段四：奖励系统模块**
   - 完成src/rewards目录下的工具函数和业务逻辑测试
     - rewards/utils
     - rewards/services（如果存在）
     - rewards/Business/MembershipForm（作为业务逻辑测试的Demo案例）
   - 审查自然语言测试用例
   - 实现测试代码
   - 验证测试覆盖率
   - 验证应用编译和启动正常(`yarn start`)

7. **阶段五：其他业务模块**
   - 根据项目实际结构，完成其他业务模块的工具函数和业务逻辑测试
     - 可能包括src下的其他一级目录
     - 排除已经在前面阶段处理过的模块
   - 审查自然语言测试用例
   - 实现测试代码
   - 验证测试覆盖率
   - 验证应用编译和启动正常(`yarn start`)

8. **阶段间的过渡管理**
   - 每个阶段结束时进行全面测试
   - 验证应用编译和启动正常(`yarn start`)
   - 解决跨阶段的依赖问题
   - 更新测试文档和指南

9. **阶段完成标准**
   - 所有自然语言测试用例都已实现
   - 工具函数测试覆盖率达到90%以上
   - 业务逻辑测试覆盖率达到80%以上
   - 所有测试都能稳定通过
   - 应用能正常编译和启动(`yarn start`验证通过)
   - 测试代码符合项目规范

## 6. 持续集成

1. **更新CI/CD流程**
   - 更新CI/CD流程以适应新的测试架构
   - 设置测试覆盖率检查
   - 自动化测试报告生成

2. **监控和优化**
   - 跟踪测试覆盖率变化
   - 收集反馈并持续改进
   - 定期审查测试质量

## 7. 文档和维护

1. **更新项目文档**
   - 记录新的测试架构和标准
   - 提供测试编写指南
   - 说明迁移后的目录结构

2. **建立维护机制**
   - 定义测试维护责任
   - 创建测试质量检查流程
   - 设置定期审查机制

## 时间线

- **准备阶段**：1-2周
- **清理阶段**：1-2周
- **迁移阶段**：2-3周
- **新架构实施（Demo案例）**：2-3周
- **分阶段实施**：
  - 阶段一：1-2周
  - 阶段二：2-3周
  - 阶段三：1-2周
  - 阶段四：2-3周
  - 阶段五：2-3周
- **总体完成时间**：约3-5个月

## 关键成功指标

1. 工具函数测试覆盖率达到90%以上
2. 业务逻辑测试覆盖率达到80%以上
3. 所有公共工具函数成功迁移到新位置
4. 测试运行时间减少
5. 测试维护成本降低