name: Auto Assign PR to Contributors

on:
  pull_request:
    types: [opened, reopened]

jobs:
  auto-assign:
    runs-on: ubuntu-latest
    steps:
      - name: Assign PR based on base branch and set reviewers
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const prNumber = context.payload.pull_request.number;
            const baseBranch = context.payload.pull_request.base.ref;
            const headBranch = context.payload.pull_request.head.ref;
            const creator = context.payload.pull_request.user.login;
            const owner = context.repo.owner;
            const repo = context.repo.repo;

            console.log(`🔍 Processing PR #${prNumber} (${headBranch} → ${baseBranch})`);

            // Get unique commit authors
            const commits = await github.paginate(
              github.rest.pulls.listCommits,
              {
                owner,
                repo,
                pull_number: prNumber,
                per_page: 100,
              }
            );

            const commitAuthors = [...new Set(
              commits
                .filter(commit => commit.author?.login)
                .map(commit => commit.author.login)
            )];
            const filteredCreator = commitAuthors.filter(author => author !== creator);

            // Handle PR assignments based on branch conditions
            if (baseBranch === 'master') {
              // For PRs targeting master branch
              console.log(`🔗 Assigning PR to creator: ${creator}`);
              await github.rest.issues.addAssignees({
                owner,
                repo,
                issue_number: prNumber,
                assignees: [creator]
              });

              if (headBranch.startsWith('release') && filteredCreator.length > 0) {
                console.log(`👥 Setting reviewers: ${filteredCreator.join(', ')}`);
                try {
                  // Limit reviewers to maximum allowed by GitHub (typically 10)
                  const maxReviewers = filteredCreator.slice(0, 10);
                  await github.rest.pulls.requestReviewers({
                    owner,
                    repo,
                    pull_number: prNumber,
                    reviewers: filteredCreator
                  });
                } catch (error) {
                  console.log(`⚠️ Error requesting reviewers: ${error.message}`);
                }
              } else {
                console.log("❗ No reviewers found for release branch");
              }
            } else if (commitAuthors.length > 0) {
              // For PRs targeting non-master branches
              console.log(`📦 Assigning PR to commit authors: ${commitAuthors.join(', ')}`);
              // Limit assignees to maximum allowed by GitHub (10)
              const maxAssignees = commitAuthors.slice(0, 10);
              await github.rest.issues.addAssignees({
                owner,
                repo,
                issue_number: prNumber,
                assignees: maxAssignees
              });
            } else {
              console.log("❗ No assignable contributors found");
            }
