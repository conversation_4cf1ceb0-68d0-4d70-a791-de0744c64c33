name: Trigger Webhook on PR Merge to Main or Master

on:
  pull_request:
    types:
      - closed  # Trigger only when PR is closed

jobs:
  check-merge:
    runs-on: ubuntu-latest

    steps:
      # Trigger only when PR is merged to main/master and the merge is successful
      - name: Check if PR is merged to the main or master
        if: github.event.pull_request.merged == true && (github.event.pull_request.base.ref == 'main' || github.event.pull_request.base.ref == 'master')
        run: |
          echo "PR merged to main/master"
          
          # Step 1: Define your Webhook URL directly (not from Secrets)
          WEBHOOK_URL="https://storehub.sg.larksuite.com/base/automation/webhook/event/SxvMabx9qwk93ihy2ull9IwSgwg"  # Replace this with the URL set in your Webhook configuration

          # Step 2: Convert GitHub event to JSON and ensure proper formatting
          PAYLOAD=$(echo '${{ toJson(github.event) }}' | jq -c .)

          # Step 3: Trigger the Webhook
          curl -X POST -H "Content-Type: application/json" \
               -d "$PAYLOAD" \
               "$WEBHOOK_URL"  # Using the directly defined Webhook URL
