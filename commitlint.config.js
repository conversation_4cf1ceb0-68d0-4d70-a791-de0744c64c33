module.exports = {
  extends: ['@commitlint/config-conventional'],
  parserPreset: {
    parserOpts: {
      // FIXME: emoji pattern is not working, can only use wildcard for now.
      headerPattern: new RegExp(`^(?<emoji>.*\\s)?(?<type>\\w*)(?<scope>\\(.+\\))?:\\s(?<subject>.*)$`, 'u'),
      headerCorrespondence: ['emoji', 'type', 'scope', 'subject'],
    },
  },
  rules: {
    // We don't need to enforce the subject to be in a specific case.
    'subject-case': [0],
    // Sometimes Jira ticket title would be quite long.
    'header-max-length': [0],
  },
};
