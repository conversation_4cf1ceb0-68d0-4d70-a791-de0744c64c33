# BEEP

## Table of contents

1. [Abstraction](#abstraction)
2. [Environments](#environments)
3. [Setup](#setup)
4. [Get started](#get-started)
   1. [Installation](#installation)
   2. [Test environment .ENV](#env)
   3. [Set local domain](#set-local-domain)
   4. [Start F&B && Loyalty](#start-ordering-loyalty)
   5. [Start Beep Entrance](#beep-entrance)
   6. [Online Debug](#online-debug)
5. [Enable Growthbook](#enable-growthbook)
6. [Mock Server from Apifox](#mock-server-from-apifox)
7. [MCP Integration Setup](#mcp-integration-setup)
8. [Customize Workbox Service Workers](#customize-workbox-service-workers)
9. [I18N JSON File Style Guide](#i18n-json-style-guide)
10. [Style Guide](#style-guide)
11. [Analyzing bundle size](#analyzing-bundle-size)
12. [GTM settings for 3rd-party(TNGD & GCash) providers from Alipay MP](#gtm-settings-for-alipay-mp)
13. [Trouble Shooting](#trouble-shooting)
14. [data-test-id name convention for log](https://storehub.sg.larksuite.com/wiki/VpJEwqRClijvvWkqJM9lQXK4gCe?from=from_copylink)
15. [Test URL](https://github.com/storehubnet/beep-v1-webapp/wiki/Test-URL)

<a name="abstraction"></a>

## Abstraction

Front end of this project is bootstrapped by [CRA (Create React App)](https://create-react-app.dev/docs/getting-started).

<a name="environments"></a>

## Environments

- nvm
- node 18.19.0
- yarn

<a name="setup"></a>

## Setup

### Option 1: One-Click Setup (Recommended)

We provide a one-click setup script that will automatically handle all the setup steps for you, including nvm installation, Node.js setup, yarn installation, npm registry configuration, and project startup.

To use the setup script, follow these steps:

![Setup Script Instructions](docs/images/setup-script-instructions.jpeg)

Or simply run:
```bash
bash scripts/setup-and-run.sh
```

### Option 2: Manual Setup

```
# Install npm-cli-login package on global
npm install npm-cli-login -g

# setup the StoreHub private register
npm config set registry https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/ && npm config set ca ""

# Login on the register
npm-cli-login -u storehub -e <EMAIL> -p "a[RF8zZy" -r https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/

# setup commit message format
chmod +x .husky/commit-msg
```

<a name="get-started"></a>

## Get started

<a name="installation"></a>

### Installation

```sh
nvm use v18.19
yarn pre:env subenv={subenv name}
yarn
```

<a name="test-environment-env"></a>

### Test environment .ENV (Skip if done)

Please contact the project administrator to access apollo https://apollo.shub.us/config.html?#/appid=beep-v1-webapp , Some local environment variables need to use the value of the test environment

<a name="set-local-domain"></a>

### Set local domain (Skip if done)

{business} name can pick one account name from https://storehub.sg.larksuite.com/wiki/WH63w8ttnixlqPkWvAAl3OPng1f?from=from_copylink

```sh
(sudo) vim /etc/hosts (on Mac)
127.0.0.1 {business}.beep.local.shub.us
127.0.0.1 www.beep.local.shub.us
```

<a name="start-ordering-loyalty"></a>

### Start F&B && Loyalty

1. Start project

- Quickly start ordering using the test environment (Recommendation)
  [Proxying API Requests in Development](https://create-react-app.dev/docs/proxying-api-requests-in-development/)

  > 1. `yarn start`
  > 2. Visiting URL: `{business}.beep.local.shub.us:3000`
  > 3. In `{business}.beep.local.shub.us:3000/ordering/location`.If you need to fill in location, please enter KLCC
  > 4. Set \_\_s to local ordering page cookie from PROXY testing environment (optional)
  > 5. Set deliveryAddress to local sessionStorage from PROXY testing environment (optional)

- (Optional) Using HTTPS in Development

  > 1. Run `yarn generate-dev-ssl-cert` to create a certificate authority on your machine and generate certificates for this project.
  > 2. Run `yarn start:https` to serve pages over HTTPS.

<a name="beep-entrance"></a>

### Beep Entrance

1. Start site

- Quickly start site using the test environment (Recommendation)
  [Proxying API Requests in Development](https://create-react-app.dev/docs/proxying-api-requests-in-development/)

  > 1. `yarn start`
  > 2. Visiting Site URL: `www.beep.local.shub.us:3000/home`
  > 3. In `www.beep.local.shub.us:3000/ordering/location`.If you need to fill in location, please enter KLCC
  > 4. Visiting Scan Page URL: `www.beep.local.shub.us:3000/qrscan`

<a name="online-debug"></a>

### Online Debug (Source Map)

1. Setting aws keys

   ```sh
   open ~/.aws/credentials
   ```

   Update `aws_access_key_id` and `aws_secret_access_key`

2. Start source map
   #### on testing environment
   ```sh
   yarn serve:sourcemap --bucket beep-v2-web
   ```
   #### on staging environment
   ```sh
   yarn serve:sourcemap --bucket beep-v2-staging
   ```
   <a name="enable-growthbook"></a>

## Enable Growthbook

1. Update .env

   > Update `REACT_APP_GROWTHBOOK_ENABLED` and `REACT_APP_GROWTHBOOK_CLIENT_KEY` on `.env` (2. from Apollo FAT)

2. Restart project

<a name="mock-server-from-apifox"></a>

## Mock Server from Apifox

### Environments

- Installed and open Apifox app
- Completed Beep setup and installation

```sh
cp mock-server.example.js mock-server.js
```

### Start Mock

1. Update mock-server.js

   > 1. Update `projectId` to `beep-v1-bff project id of Apifox` (1. mock server project id)

2. Start mock-server

- Quickly start mock-server using the Apifox (Recommendation)
  > 1. Update mock api urls in `mock-server.js` (1. The rules for mock url have been written in mock-server. example. js inside)
  > 2. `yarn start:mock-server`
  > 3. Visiting any Beep page to check mock result

<a name="mcp-integration-setup"></a>

## MCP Integration Setup

Model Context Protocol (MCP) integration enables AI assistants to interact with external services like Figma, Jira, and Lark/Feishu. This setup allows for enhanced development workflows with AI-powered integrations.

### Prerequisites

- Node.js 18.19.0 or higher
- npm or yarn package manager
- Valid API credentials for the services you want to integrate

### Installed MCP Servers

The following MCP servers are installed and ready for configuration:

- **figma-developer-mcp@0.3.1** - Figma design file integration
- **jira-client@8.2.2** - Jira project management integration
- **lark-mcp@1.0.0** - Lark/Feishu enterprise collaboration integration

### Installation Commands

```bash
# Install MCP servers globally
npm install -g figma-developer-mcp jira-client lark-mcp
```

### API Credentials Setup

#### 1. Figma Personal Access Token

1. Login to [figma.com](https://figma.com)
2. Navigate: Profile Icon → **Settings** → **Security** tab
3. Locate **Personal access tokens** section
4. Click **Generate new token**
5. Provide descriptive name (e.g., "MCP Integration")
6. ⚠️ **Important**: Copy token immediately (cannot be viewed again)

#### 2. Jira API Token

1. Visit [id.atlassian.com/manage-profile/security/api-tokens](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click **Create API token**
3. Enter descriptive label (e.g., "MCP Integration")
4. Set expiration period (recommended: 365 days)
5. Create and copy token immediately

**Additional Required Information:**

- **JIRA_URL**: Your Jira instance URL (format: `https://company.atlassian.net`)
- **JIRA_USERNAME**: Your Atlassian account email address

#### 3. Lark (Feishu) App Credentials

1. Access developer platform:
   - 🇨🇳 **Feishu (China)**: [open.feishu.cn](https://open.feishu.cn)
   - 🌍 **Lark (International)**: [open.larksuite.com](https://open.larksuite.com)
2. Create Application → Self-built Application
3. Navigate to **Credentials & Basic Info** to obtain:
   - **App ID**
   - **App Secret**
4. Configure required permissions in **Permission Management**

### Environment Configuration

Create environment variables file:

```bash
# Create MCP environment variables file
cat > ~/.mcp_env << 'EOF'
# MCP Environment Variables
export FIGMA_ACCESS_TOKEN="your_figma_token_here"
export JIRA_URL="https://yourcompany.atlassian.net"
export JIRA_USERNAME="<EMAIL>"
export JIRA_API_TOKEN="your_jira_token_here"
export LARK_APP_ID="your_lark_app_id_here"
export LARK_APP_SECRET="your_lark_app_secret_here"
export LARK_BASE_URL="https://open.feishu.cn"
EOF

# Load environment variables
source ~/.mcp_env
```

### MCP Configuration Files

#### For Cursor (Project Configuration)

```bash
# Create Cursor MCP configuration directory
mkdir -p .cursor

# Create configuration file
cat > .cursor/mcp.json << 'EOF'
{
  "mcpServers": {
    "figma": {
      "command": "npx",
      "args": ["-y", "figma-developer-mcp", "--stdio"],
      "env": {
        "FIGMA_API_KEY": "your_figma_access_token_here"
      }
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github", "--repositories", "repo1,repo2"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here",
        "GITHUB_REPOSITORIES": "storehubnet/beep-v1-webapp,your-org/another-repo"
      }
    }
  }
}
EOF
```

#### For Claude Desktop

```bash
# Create Claude Desktop configuration directory
mkdir -p "~/Library/Application Support/Claude"

# Create configuration file
cat > "~/Library/Application Support/Claude/claude_desktop_config.json" << 'EOF'
{
  "mcpServers": {
    "figma": {
      "command": "figma-developer-mcp",
      "args": [],
      "env": {
        "FIGMA_ACCESS_TOKEN": "${FIGMA_ACCESS_TOKEN}"
      }
    }
  }
}
EOF
```

#### Environment Variables Usage

For Cursor, you can use environment variables in two ways:

1. **Direct in mcp.json**: Set values directly in the configuration file
2. **Using system environment variables**: Load from `.mcp_env` file:

```bash
# First, load environment variables
source ./.mcp_env

# Then use env command to pass them to MCP servers
env FIGMA_ACCESS_TOKEN="$FIGMA_ACCESS_TOKEN" cursor
```

### Testing Configuration

#### Test Figma MCP

```bash
# Test Figma MCP server functionality
figma-developer-mcp --figma-api-key="your_token" --help
```

#### Test Jira Connection

```bash
# Test Jira API connection
node -e "
const JiraClient = require('jira-client');
const jira = new JiraClient({
  protocol: 'https',
  host: process.env.JIRA_URL?.replace('https://', ''),
  username: process.env.JIRA_USERNAME,
  password: process.env.JIRA_API_TOKEN,
  apiVersion: '2',
  strictSSL: true
});
console.log('Jira client configuration successful');
"
```

### Available Functionality

#### Figma Integration

- Design file information and metadata retrieval
- Component and style library access
- Design asset and image export
- Team and project data access

#### Jira Integration

- Issue creation, updates, and queries
- Sprint and Board management
- Project information retrieval
- Comment and attachment handling

#### Lark Integration

- Message sending and receiving
- Calendar event management
- Document and spreadsheet operations
- User and group management

### Security Best Practices

- **Never commit API tokens to version control**
- **Rotate API tokens regularly** (quarterly recommended)
- **Use environment variables** for all sensitive credentials
- **Apply principle of least privilege** for API permissions
- **Monitor API usage** for unusual activity

### Troubleshooting

#### Common Issues

1. **Environment variables not loaded**

   ```bash
   # Solution: Reload environment variables
   source ~/.mcp_env
   ```

2. **Permission denied errors**

   - Verify API token permissions in respective platforms
   - Ensure all required scopes are enabled
   - Check token expiration dates

3. **Connection timeouts**
   - Verify firewall settings
   - Check proxy configuration
   - Confirm platform service status

#### Log Files

```bash
# View Claude Desktop logs
tail -f ~/Library/Logs/Claude/mcp*.log

# View Cursor logs
tail -f ~/Library/Logs/Cursor/mcp*.log
```

### Additional Resources

- [Figma API Documentation](https://www.figma.com/developers/api)
- [Jira REST API Documentation](https://developer.atlassian.com/cloud/jira/platform/rest/v2/)
- [Feishu Open Platform Documentation](https://open.feishu.cn/document/)
- [Lark Developer Documentation](https://open.larksuite.com/document)

#### GitHub Repository Configuration

To limit GitHub MCP to specific repositories, you can configure it in several ways:

**Method 1: Environment Variable (Recommended)**

```bash
# In your .mcp_env file, specify repositories to monitor
echo 'GITHUB_REPOSITORIES="storehubnet/beep-v1-webapp,your-org/another-repo"' >> .mcp_env
```

**Method 2: Configuration Arguments**

```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github",
        "--repositories",
        "storehubnet/beep-v1-webapp,your-org/repo2"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"
      }
    }
  }
}
```

**Method 3: Organization-specific Configuration**

```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here",
        "GITHUB_OWNER": "storehubnet",
        "GITHUB_REPOSITORIES": "beep-v1-webapp,other-repo"
      }
    }
  }
}
```

**Repository List Format:**

- Use comma-separated format: `"owner1/repo1,owner2/repo2"`
- For same organization: Set `GITHUB_OWNER` and list repo names only
- Support public and private repositories (based on token permissions)

**Example Configuration for This Project:**

```bash
# Update your .mcp_env file with specific repositories
cat >> ~/.mcp_env << 'EOF'
# GitHub MCP Configuration - Fixed Repositories
export GITHUB_PERSONAL_ACCESS_TOKEN="ghp_xxxxxxxxxxxxxxxxxxxxxxxxxx"
export GITHUB_REPOSITORIES="storehubnet/beep-v1-webapp,storehubnet/other-repo"
EOF

# Reload environment variables
source ~/.mcp_env
```

**Complete Cursor Configuration Example:**

```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "ghp_xxxxxxxxxxxxxxxxxxxxxxxxxx",
        "GITHUB_REPOSITORIES": "storehubnet/beep-v1-webapp,storehubnet/beep-mobile-app,your-org/frontend-utils"
      }
    }
  }
}
```

**Testing Repository Access:**

```bash
# Test GitHub MCP with specific repositories
node -e "
console.log('Testing GitHub MCP configuration...');
console.log('Repositories:', process.env.GITHUB_REPOSITORIES);
console.log('Token configured:', !!process.env.GITHUB_PERSONAL_ACCESS_TOKEN);
"
```

### MCP Startup Scripts

The project includes convenient startup scripts that automatically load environment variables and start Cursor with MCP services.

#### Quick Start (Recommended)

```bash
# Use the simple startup script
./mcp-scripts/start-mcp-simple.sh
```

This script:
1. Loads environment variables from `.mcp_env`
2. Exports them for Cursor
3. Starts Cursor (which reads `.cursor/mcp.json` automatically)

#### Available Scripts

**Simple Startup:**
```bash
./mcp-scripts/start-mcp-simple.sh     # Clean, minimal startup
./mcp-scripts/quick-start.sh          # One-liner command
```

**Full Startup with Diagnostics:**
```bash
./mcp-scripts/start-mcp.sh            # Includes status checks and detailed output
```

**Troubleshooting:**
```bash
./mcp-scripts/debug-mcp.sh            # Diagnose MCP configuration issues
```

**Windows Users:**
```cmd
mcp-scripts\start-mcp.bat             # Windows batch file version
```

#### How It Works

The startup scripts leverage the existing configuration files:

1. **Environment Variables**: Load from `.mcp_env` file
2. **MCP Configuration**: Cursor automatically reads `.cursor/mcp.json`
3. **Service Discovery**: MCP services start based on configuration

#### Manual Startup (Alternative)

If you prefer not to use scripts:

```bash
# Load environment variables
source .mcp_env

# Export key variables for Cursor
export FIGMA_ACCESS_TOKEN JIRA_URL JIRA_USERNAME JIRA_API_TOKEN
export JIRA_PROJECTS_FILTER ENABLED_TOOLS GITHUB_PERSONAL_ACCESS_TOKEN

# Start Cursor (reads .cursor/mcp.json automatically)
cursor .
```

#### Prerequisites for Startup Scripts

- ✅ Docker running (for mcp-atlassian/Jira integration)
- ✅ Node.js/npm installed (for figma-developer-mcp)
- ✅ Valid API tokens in `.mcp_env`
- ✅ Network connectivity

<a name="customize-workbox-service-workers"></a>

## Customize Workbox Service Workers

Get more from [Using Custom Workbox Service Workers with Create-React-App (without ejecting)
](https://karannagupta.com/using-custom-workbox-service-workers-with-create-react-app/)

<a name="i18n-json-style-guide"></a>

## I18N JSON File Style Guide

[react-i18next Doc](https://react.i18next.com/)

### JSON file name rules:

- Please use upper camel case (eg: OrderingHome)

### Key name rules:

- Please use upper camel case no including underscores and spaces
- Phrase remove spaces as key (eg: "OrderNow": "Order now")
- Paragraphs use descriptive phrases as keys (eg: "ClaimedProcessingText": "You've earned more cashback! We'll add it once it's been processed.")

### Content rules:

- Only the first letter can be capitalized for phrase, except for words like "OK" (eg: "OrderNow": "Order now")
- Paragraphs have only the first letter of each sentence capitalized (eg: "ClaimedProcessingText": "You've earned more cashback! We'll add it once it's been processed.")
- Follow English written rules

When you want to use html tag in translation, please use like:

```jsx
<Trans ns="Ordering" i18nKey="Greeting">
  hello, <span>User</span>
</Trans>
```

<a name="style-guide"></a>

## Style Guide

If you create a public component under `/src/components/`, please update the library in the style guide

### Start up the Style Guide locally

> browser will automatically start on port `9009`

```shell script
yarn storybook
```

### Manually publish style guide

```shell script
cd yarn && yarn chromatic --project-token=ejo2it5a6d
```

<a name="analyzing-bundle-size"></a>

## Analyzing bundle size

You need to comment some sentry settings on .env

```shell script
# SENTRY_ORG=
# SENTRY_PROJECT=
# SENTRY_AUTH_TOKEN=
```

```shell script
yarn build && yarn analyze
```

<a name="gtm-settings-for-alipay-mp"></a>

## GTM settings for 3rd-party(TNGD & GCash) providers from Alipay MP

If you don't have permission of GTM, you can ask project owner to help update.

1. Add a new trigger in GTM test triggers[https://tagmanager.google.com/#/container/accounts/**********/containers/********/workspaces/4/triggers]

- Name: User Agent - {Provider Name}
- Trigger Type: Page View
- This trigger fires on: Some Page Views
- Fire this trigger when an Event occurs and all of these conditions are true:
  a. jv – userAgent contains {ProviderName in userAgent}
  b. jv – userAgent contains MiniProgram

2. Add Triggering for `TT - Beepit.com Tiktok Pixel`

- Click Exceptions and Click `+` button
- Search 1. trigger name, and selected
- Save changes and submit the tags

* otherwise provider devtools response
  `Error Number: 305 Error Message: Embedded web view cannot load this URL. Solution The embedded web view of mini program xxxxxx canot load bytedance%3%2F%2Fdispatch_message%2F, because it's not an http(s) URL`

* Production configuration need PM to update and publish

<a name="trouble-shooting"></a>

## Trouble Shooting

Coming soon...
