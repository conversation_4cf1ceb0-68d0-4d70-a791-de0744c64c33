# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# production
build

# misc
.env
.mcp_env
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
# crash reports
report.*.json
.vscode
.history
.idea
*.log

# storybook build info
storybook-static/
build-storybook.log

mock-server.js

# all non-en i18n files
public/locales/*
!public/locales/en/

# local SSL certs
.cert

# Sonar Scanner
.scannerwork/
.qodo
